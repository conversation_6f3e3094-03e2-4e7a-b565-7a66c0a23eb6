<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.lenskart.nexs</groupId>
    <artifactId>nexs-cid-domain</artifactId>
    <version>1.2.24</version>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <java.version>1.8</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>2.3.0</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-core</artifactId>
            <version>2.3.0</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
            <version>2.3.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <version>2.5.14</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.24</version>
        </dependency>
        <dependency>
            <groupId>com.lenskart.nexs</groupId>
            <artifactId>nexs-microservice-commons-client</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.lenskart.nexs</groupId>
            <artifactId>nexs-ims-commons</artifactId>
            <version>1.16.12</version>
        </dependency>
    </dependencies>
    <repositories>
        <repository>
            <id>nexs-releases</id>
            <url>http://archiva-new.prod.internal:8080/repository/internal/nexs-releases</url>
        </repository>
        <repository>
            <id>archiva.default</id>
            <url>http://archiva-new.prod.internal:8080/repository/internal</url>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>nexs-releases</id>
            <url>http://archiva-new.prod.internal:8080/repository/internal/nexs-releases</url>
        </repository>
    </distributionManagement>
    <!--<build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>jaxb2-maven-plugin</artifactId>
                <version>1.6</version>
                <executions>
                    <execution>
                        <id>xjc</id>
                        <goals>
                            <goal>xjc</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <schemaDirectory>${project.basedir}/src/main/resources/</schemaDirectory>
                    <outputDirectory>${project.basedir}/src/main/java</outputDirectory>
                    <clearOutputDir>false</clearOutputDir>
                </configuration>
            </plugin>
        </plugins>
    </build>-->
</project>
