<xs:schema
           xmlns:xs="http://www.w3.org/2001/XMLSchema"

           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xmlns:xsd="http://www.w3.org/2001/XMLSchema"

           xmlns:tns="http://nexs.lenskart.com/cid/request/updateInventory"
           targetNamespace="http://nexs.lenskart.com/cid/request/updateInventory"
           xmlns:encoding="http://schemas.xmlsoap.org/soap/encoding/"
           xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/envelope/"
           elementFormDefault="qualified">

    <xs:element name="StockAdjustment">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="ProductIdArr" type="tns:ProductIdArr"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="ProductIdArr">
              <xs:sequence>
                    <xs:element name="Item" type="tns:Item"  minOccurs="1" maxOccurs="unbounded"/>
                </xs:sequence>


    </xs:complexType>

    <xs:complexType name="Item">
            <xs:sequence>
                <xs:element name="ProductId" minOccurs="1" nillable="false"  type="xs:string">
                </xs:element>
                <xs:element name="Qty" minOccurs="1" nillable="false" type="xs:int">
                </xs:element>
                <xs:element name="UpdateTime" minOccurs="1" nillable="false" type="xs:string">
                </xs:element>
            </xs:sequence>
        </xs:complexType>

    <xs:element name="StockAdjustmentResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="return" type="tns:requestStatus"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="requestStatus">
        <xs:all>
            <xs:element name="SuccessCode" type="xs:string"/>
            <xs:element name="Msg" type="xs:string"/>
        </xs:all>
    </xs:complexType>

</xs:schema>