package com.lenskart.nexs.cid.request;

import com.lenskart.nexs.cid.dto.WarehouseBlockedOrderItemDto;
import com.lenskart.nexs.ims.request.StockCheck;
import lombok.*;

import java.util.Map;

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class InventoryBlockRequestWrapper {
    private Integer orderId;
    private StockCheck stockCheckRequest;
    private Map<Integer, WarehouseBlockedOrderItemDto> itemsToUpdate;
}
