package com.lenskart.nexs.cid.request;

import lombok.*;
import org.apache.commons.lang3.time.DateUtils;

import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class InventoryReconciliationRequest {
    private Integer productId;
    private Long incrementId;
    private Date unFulfillableTime;
    private Integer unFulfillableCount;

    public void setUnFulfillableTime(String unFulfillableTime) throws ParseException {
        Date unFulfillableDate = DateUtils.parseDate(unFulfillableTime, "yyyy-MM-dd HH:mm:ss", "dd/MM-yyyy");
        LocalDateTime ldt = LocalDateTime.ofInstant(unFulfillableDate.toInstant(), ZoneId.systemDefault()).minusHours(5).minusMinutes(30);
        this.unFulfillableTime = Date.from(ldt.atZone(ZoneId.systemDefault()).toInstant());
    }
}
