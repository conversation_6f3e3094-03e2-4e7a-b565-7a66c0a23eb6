//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.7 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2021.02.18 at 02:06:21 PM IST 
//
@XmlSchema(
        namespace = "http://tempuri.org",
        elementFormDefault = XmlNsForm.QUALIFIED,
        xmlns = {
                @XmlNs(prefix="", namespaceURI="http://tempuri.org")
        }
)

//@javax.xml.bind.annotation.XmlSchema(namespace = "http://tempuri.org", elementFormDefault = javax.xml.bind.annotation.XmlNsForm.QUALIFIED)
package com.lenskart.nexs.cid.request.updateinventory;

import javax.xml.bind.annotation.XmlNs;
import javax.xml.bind.annotation.XmlNsForm;
import javax.xml.bind.annotation.XmlSchema;