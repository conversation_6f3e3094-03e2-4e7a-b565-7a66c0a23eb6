package com.lenskart.nexs.cid.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;

@Data
@Validated
@JsonIgnoreProperties(ignoreUnknown = true)
public class InventoryItemCountRR {

    @NotBlank
    private Integer productId;

    @NotBlank
    private Integer requestedQty;

    private Long totalAvailableQty;

    private Long fulfillableQty;

    private Long unFulfillableQty;

}
