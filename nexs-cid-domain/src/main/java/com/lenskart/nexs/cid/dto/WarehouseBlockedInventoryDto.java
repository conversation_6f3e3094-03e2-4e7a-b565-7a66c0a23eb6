package com.lenskart.nexs.cid.dto;

import com.lenskart.nexs.commons.dto.BaseDto;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
public class WarehouseBlockedInventoryDto extends BaseDto {
    private Long productId;
    private String facility;
    private Integer quantity;
    private String legalOwner;
    private Integer unfulfillableQty;
}
