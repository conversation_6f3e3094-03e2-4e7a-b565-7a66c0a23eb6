package com.lenskart.nexs.cid.dto;

import com.lenskart.nexs.cid.enums.JunoApiStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * DTO for JunoAuditLog entity
 */
@Getter
@Setter
@ToString
public class JunoAuditLogDto {

    private Long id;
    private String correlationId;
    private String requestPayload;
    private String responsePayload;
    private JunoApiStatus status;
    private Integer httpStatusCode;
    private String errorMessage;
    private String errorStackTrace;
    private Date requestTimestamp;
    private Date responseTimestamp;
    private Long durationMs;
    private Integer retryCount;
    private String apiEndpoint;
    private String productIds;
    private String facilityCodes;
    private String legalOwners;
    private Integer transactionCount;
    private String source;
    private Date createdAt;
    private Date updatedAt;
    private String createdBy;
    private String updatedBy;
}
