//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.7 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2021.02.18 at 02:06:21 PM IST 
//


package com.lenskart.nexs.cid.request.updateinventory;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.lenskart.nexs.cid.request.updateinventory package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.lenskart.nexs.cid.request.updateinventory
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link StockAdjustment }
     * 
     */
    public StockAdjustment createStockAdjustment() {
        return new StockAdjustment();
    }

    /**
     * Create an instance of {@link ProductIdArr }
     * 
     */
    public ProductIdArr createProductIdArr() {
        return new ProductIdArr();
    }

    /**
     * Create an instance of {@link StockAdjustmentResponse }
     * 
     */
    public StockAdjustmentResponse createStockAdjustmentResponse() {
        return new StockAdjustmentResponse();
    }

    /**
     * Create an instance of {@link RequestStatus }
     * 
     */
    public RequestStatus createRequestStatus() {
        return new RequestStatus();
    }

    /**
     * Create an instance of {@link Item }
     * 
     */
    public Item createItem() {
        return new Item();
    }

}
