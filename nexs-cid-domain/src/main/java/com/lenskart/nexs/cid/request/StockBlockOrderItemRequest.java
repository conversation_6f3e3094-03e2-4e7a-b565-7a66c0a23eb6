package com.lenskart.nexs.cid.request;

import lombok.*;

import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class StockBlockOrderItemRequest {

    @NotNull(message = "productId cannot be null")
    private Long productId;

    @NotNull(message = "requiredQuantity cannot be null")
    private Integer requiredQuantity;

    private List<Integer> orderItems = null;
}
