package com.lenskart.nexs.cid.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.nexs.cid.dto.WarehouseInventoryDto;
import lombok.*;

import java.util.List;
import java.util.Objects;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class WarehouseInventoryResponse {

    @JsonProperty("totalCount")
    private long totalCount;

    @JsonProperty("whInventories")
    private List<WarehouseInventoryDto> warehouseInventoryDtoList;

}
