package com.lenskart.nexs.cid.dto;

import com.lenskart.nexs.cid.enums.WarehouseBlockedOrderItemStatus;
import com.lenskart.nexs.commons.dto.BaseDto;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
public class WarehouseBlockedOrderItemDto extends BaseDto {
    private Long orderItemId;
    private Long orderId;
    private WarehouseBlockedOrderItemStatus warehouseBlockedOrderItemStatus;
    private WarehouseBlockedInventoryDto warehouseBlockedInventoryDto;
    private String entityType;
}
