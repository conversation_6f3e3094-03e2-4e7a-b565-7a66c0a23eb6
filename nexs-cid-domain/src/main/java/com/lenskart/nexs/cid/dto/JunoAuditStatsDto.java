package com.lenskart.nexs.cid.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * DTO for Juno audit statistics
 */
@Getter
@Setter
@ToString
public class JunoAuditStatsDto {

    private Long totalRequests;
    private Long successfulRequests;
    private Long failedRequests;
    private Long timeoutRequests;
    private Long retryRequests;
    private Double successRate;
    private Double failureRate;
    private Long averageDurationMs;
    private Long maxDurationMs;
    private Long minDurationMs;
    private Integer maxRetryCount;
    private Long totalTransactions;
}
