package com.lenskart.nexs.cid.dto;

import com.lenskart.nexs.commons.dto.BaseDto;
import com.lenskart.nexs.ims.enums.LocationType;
import com.lenskart.nexs.ims.model.Availability;
import com.lenskart.nexs.ims.model.Condition;
import com.lenskart.nexs.ims.model.Status;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@ToString(callSuper = true)
public class WarehouseInventoryDto extends BaseDto {
    private Long productId;
    private String facility;
    private Integer quantity;
    private Integer blockedQuantity;
    private Condition condition;
    private Availability availability;
    private Status status;
    private String supplier;
    private String legalOwner;
    private LocationType locationType = LocationType.DEFAULT;
    private Integer masterPid;
    private Boolean enabled;
    private Date reconciliationEventTime;
    private Integer unfulfillableQty;
    private Integer bufferedQuantity;
}
