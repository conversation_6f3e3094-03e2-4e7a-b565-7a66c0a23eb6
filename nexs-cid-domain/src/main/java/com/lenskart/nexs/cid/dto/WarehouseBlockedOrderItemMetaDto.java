package com.lenskart.nexs.cid.dto;

import com.lenskart.nexs.cid.enums.FulfillableStatus;
import com.lenskart.nexs.commons.dto.BaseDto;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
public class WarehouseBlockedOrderItemMetaDto extends BaseDto {
    private Long warehouseBlockedOrderItemId;
    private FulfillableStatus fulfillableStatus;
}
