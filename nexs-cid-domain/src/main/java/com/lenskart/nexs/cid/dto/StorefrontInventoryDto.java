package com.lenskart.nexs.cid.dto;

import com.lenskart.nexs.commons.dto.BaseDto;
import com.lenskart.nexs.ims.model.Availability;
import com.lenskart.nexs.ims.model.Condition;
import com.lenskart.nexs.ims.model.Status;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
public class StorefrontInventoryDto extends BaseDto {
    private Long productId;
    private Integer quantity;
    private Integer bufferedQuantity;
    private Condition condition;
    private Availability availability;
    private Status status;
    private String legalOwner;
}
