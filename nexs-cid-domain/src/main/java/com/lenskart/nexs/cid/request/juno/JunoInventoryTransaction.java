package com.lenskart.nexs.cid.request.juno;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import java.util.Arrays;

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class JunoInventoryTransaction {
    private Integer productId;
    private String facilityCode;
    private Integer quantity;
    private String transactionType;
    private String legalOwner;
    @JsonIgnore
    private String updateTime;

}
