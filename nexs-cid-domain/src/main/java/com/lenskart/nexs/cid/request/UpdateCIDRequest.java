package com.lenskart.nexs.cid.request;

import com.lenskart.nexs.ims.request.InventoryUpdateRequest;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@Validated
public class UpdateCIDRequest {
    @Valid
    @NotEmpty(message = "inventoryUpdateRequests cannot be empty")
    List<InventoryUpdateRequest> inventoryUpdateRequests;
}
