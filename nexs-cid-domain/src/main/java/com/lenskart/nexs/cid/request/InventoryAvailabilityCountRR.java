package com.lenskart.nexs.cid.request;

import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@Validated
public class InventoryAvailabilityCountRR {

    @NotEmpty
    private List<InventoryItemCountRR> items;

    @NotBlank
    private String facility;

    private String legalOwner = "LKIN";

    private String requestedBy;

}