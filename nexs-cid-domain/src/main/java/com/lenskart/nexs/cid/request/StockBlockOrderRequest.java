package com.lenskart.nexs.cid.request;

import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class StockBlockOrderRequest {

    @NotNull(message = "orderId cannot be null")
    private Integer orderId;

    @NotNull(message = "facility cannot be null")
    private String facility;

    @NotEmpty(message = "stockBlockOrderItems cannot be empty")
    List<StockBlockOrderItemRequest> items;

    private boolean isOnShelfBlockingAllowed = false;

    private boolean isPartialBlockingAllowed = false;

    private String updatedBy;

    private String legalOwner = "LKIN";

    private boolean isUnfulfillableBlockingAllowed = false;

}
