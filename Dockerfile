FROM public.ecr.aws/z5y1f1y8/maven:3.6-amazoncorretto-8 as builder
RUN mkdir -p /app
RUN mkdir -p /root/.m2/
COPY ./nexs-cid-service/ /app/

WORKDIR /app/
RUN aws s3 cp s3://deployment.lenskartprod.internal/newrelic-java.zip .
RUN mkdir /app/newrelic-java
RUN unzip newrelic-java.zip -d /app/newrelic-java
RUN aws s3 cp s3://deployment.lenskartprod.internal/settings.xml /root/.m2/
RUN mvn clean install -DskipTests -e

FROM public.ecr.aws/z5y1f1y8/amazoncorretto:8u282
RUN mkdir -p /app
WORKDIR /app/
COPY --from=builder /app/target/*.jar .
COPY --from=builder /app/newrelic-java/* /app/newrelic-java/
RUN chown 1000 -R /app
ENTRYPOINT java -javaagent:/app/newrelic-java/newrelic.jar -Dserver.port=80 -Dspring.profiles.active=$PROFILE -Dserver.port=80 -jar /app/*.jar