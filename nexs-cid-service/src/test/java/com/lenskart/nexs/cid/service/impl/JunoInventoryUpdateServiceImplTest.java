package com.lenskart.nexs.cid.service.impl;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.cid.connector.CatalogOpsConnector;
import com.lenskart.nexs.cid.connector.JunoConnector;
import com.lenskart.nexs.cid.connector.OrderInterceptorConnector;
import com.lenskart.nexs.cid.connector.OrderManagementServiceConnector;
import com.lenskart.nexs.cid.dto.WarehouseBlockedInventoryDto;
import com.lenskart.nexs.cid.dto.WarehouseInventoryDto;
import com.lenskart.nexs.cid.request.juno.JunoInventoryUpdateRequest;
import com.lenskart.nexs.cid.response.MetaDataResponse;
import com.lenskart.nexs.cid.response.UnSynedCountResponse;
import com.lenskart.nexs.cid.service.WarehouseBlockedInventoryService;
import com.lenskart.nexs.cid.service.WarehouseInventoryService;
import com.lenskart.nexs.cid.util.LegalOwnerUtil;
import com.lenskart.nexs.ims.model.Availability;
import com.lenskart.nexs.ims.model.Condition;
import com.lenskart.nexs.ims.model.Status;
import com.lenskart.nexs.ims.request.InventoryUpdateRequest;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import com.lenskart.nexs.ims.request.Product;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ContextConfiguration(classes = {JunoInventoryUpdateServiceImpl.class})
@ExtendWith(SpringExtension.class)
@TestPropertySource(properties = {"juno.product.classification.notallowed=123,456", "facility.defaultHubCode=NXS1"})
class JunoInventoryUpdateServiceImplTest {

    @Autowired
    private JunoInventoryUpdateServiceImpl junoInventoryUpdateService;

    @MockBean
    protected CatalogOpsConnector catalogOpsConnector;

    @MockBean
    protected WarehouseInventoryService warehouseInventoryService;

    @MockBean
    protected WarehouseBlockedInventoryService warehouseBlockedInventoryService;

    @MockBean
    protected OrderManagementServiceConnector orderOpsConnector;

    @MockBean
    protected OrderInterceptorConnector orderInterceptorConnector;

    @MockBean
    protected JunoConnector junoConnector;

    @MockBean
    private StringRedisTemplate stringRedisTemplate;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(junoInventoryUpdateService, "defaultHubCode", "NXS1");
    }

    @Test
    void testCheckUnSyncedCountAndSendUpdateToJuno2() throws Exception {
        JunoInventoryUpdateServiceImpl junoInventoryUpdateServiceImpl = new JunoInventoryUpdateServiceImpl();

        ArrayList<InventoryUpdateRequest> inventoryUpdateRequestList = new ArrayList<>();
        inventoryUpdateRequestList.add(new InventoryUpdateRequest());
        assertThrows(Exception.class,
                () -> junoInventoryUpdateServiceImpl.checkUnSyncedCountAndSendUpdateToJuno(inventoryUpdateRequestList));
    }

    @Test
    void testCheckUnSyncedCountAndSendUpdateToJuno4() throws Exception {
        JunoInventoryUpdateServiceImpl junoInventoryUpdateServiceImpl = new JunoInventoryUpdateServiceImpl();

        ArrayList<InventoryUpdateRequest> inventoryUpdateRequestList = new ArrayList<>();
        inventoryUpdateRequestList.add(null);
        assertThrows(Exception.class,
                () -> junoInventoryUpdateServiceImpl.checkUnSyncedCountAndSendUpdateToJuno(inventoryUpdateRequestList));
    }

    @Test
    @Disabled
    void testCheckUnSyncedCountAndSendUpdateToJuno5() throws Exception {
        JunoInventoryUpdateServiceImpl junoInventoryUpdateServiceImpl = new JunoInventoryUpdateServiceImpl();
        InventoryUpdateRequest inventoryUpdateRequest = mock(InventoryUpdateRequest.class);
        when(inventoryUpdateRequest.getAvailability()).thenReturn(Availability.AVAILABLE);
        when(inventoryUpdateRequest.getCondition()).thenReturn(Condition.GOOD);
        when(inventoryUpdateRequest.getStatus()).thenReturn(Status.DEFAULT);
        when(inventoryUpdateRequest.getProductId()).thenReturn(123);
        when(inventoryUpdateRequest.getFacility()).thenReturn("Facility");
        when(inventoryUpdateRequest.getLegalOwner()).thenReturn("Legal Owner");

        ArrayList<InventoryUpdateRequest> inventoryUpdateRequestList = new ArrayList<>();
        inventoryUpdateRequestList.add(inventoryUpdateRequest);
        assertThrows(Exception.class,
                () -> junoInventoryUpdateServiceImpl.checkUnSyncedCountAndSendUpdateToJuno(inventoryUpdateRequestList));
        verify(inventoryUpdateRequest).getProductId();
    }

    @Test
    @Disabled
    void testCheckUnSyncedCountAndSendUpdateToJuno7() throws Exception {

        WarehouseInventoryDto warehouseInventoryDto = new WarehouseInventoryDto();
        warehouseInventoryDto.setAvailability(Availability.AVAILABLE);
        warehouseInventoryDto.setBlockedQuantity(1);
        warehouseInventoryDto.setCondition(Condition.GOOD);
        LocalDateTime atStartOfDayResult = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto.setCreatedAt(Date.from(atStartOfDayResult.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        warehouseInventoryDto.setEnabled(true);
        warehouseInventoryDto.setFacility("productId.eq:");
        warehouseInventoryDto.setId(123L);
        warehouseInventoryDto.setLegalOwner("productId.eq:");
        warehouseInventoryDto.setMasterPid(1);
        warehouseInventoryDto.setProductId(123L);
        warehouseInventoryDto.setQuantity(1);
        LocalDateTime atStartOfDayResult1 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto
                .setReconciliationEventTime(Date.from(atStartOfDayResult1.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto.setStatus(Status.DEFAULT);
        warehouseInventoryDto.setSupplier("productId.eq:");
        LocalDateTime atStartOfDayResult2 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto.setUpdatedAt(Date.from(atStartOfDayResult2.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto.setUpdatedBy("2020-03-01");
        warehouseInventoryDto.setVersion(1L);

        ArrayList<WarehouseInventoryDto> warehouseInventoryDtoList = new ArrayList<>();
        warehouseInventoryDtoList.add(warehouseInventoryDto);
        WarehouseInventoryServiceImpl warehouseInventoryServiceImpl = mock(WarehouseInventoryServiceImpl.class);
        when(warehouseInventoryServiceImpl.search((String) any())).thenReturn(warehouseInventoryDtoList);

        JunoInventoryUpdateServiceImpl junoInventoryUpdateServiceImpl = new JunoInventoryUpdateServiceImpl();
        junoInventoryUpdateServiceImpl.setWarehouseInventoryService(warehouseInventoryServiceImpl);
        InventoryUpdateRequest inventoryUpdateRequest = mock(InventoryUpdateRequest.class);
        when(inventoryUpdateRequest.getAvailability()).thenReturn(Availability.AVAILABLE);
        when(inventoryUpdateRequest.getCondition()).thenReturn(Condition.GOOD);
        when(inventoryUpdateRequest.getStatus()).thenReturn(Status.DEFAULT);
        when(inventoryUpdateRequest.getProductId()).thenReturn(123);
        when(inventoryUpdateRequest.getFacility()).thenReturn("Facility");
        when(inventoryUpdateRequest.getLegalOwner()).thenReturn("Legal Owner");

        Product product = new Product();
        product.setClassification(123);
        when(catalogOpsConnector.findByProductId(any())).thenReturn(product);

        ArrayList<InventoryUpdateRequest> inventoryUpdateRequestList = new ArrayList<>();
        inventoryUpdateRequestList.add(inventoryUpdateRequest);

        junoInventoryUpdateService.checkUnSyncedCountAndSendUpdateToJuno(inventoryUpdateRequestList);
        verify(inventoryUpdateRequest).getProductId();
        verify(inventoryUpdateRequest, atLeast(1)).getProductId();
    }

    @Test
    void testCheckUnSyncedCountAndSendUpdateToJuno8() throws Exception {
        WarehouseInventoryServiceImpl warehouseInventoryServiceImpl = mock(WarehouseInventoryServiceImpl.class);
        when(warehouseInventoryServiceImpl.search((String) any())).thenReturn(new ArrayList<>());

        JunoInventoryUpdateServiceImpl junoInventoryUpdateServiceImpl = new JunoInventoryUpdateServiceImpl();
        junoInventoryUpdateServiceImpl.setWarehouseInventoryService(warehouseInventoryServiceImpl);
        InventoryUpdateRequest inventoryUpdateRequest = mock(InventoryUpdateRequest.class);
        inventoryUpdateRequest.setAvailability(Availability.AVAILABLE);
        inventoryUpdateRequest.setCondition(Condition.GOOD);
        inventoryUpdateRequest.setStatus(Status.DEFAULT);
        inventoryUpdateRequest.setProductId(123);
        inventoryUpdateRequest.setFacility("Facility");
        inventoryUpdateRequest.setLegalOwner("Legal Owner");
        WarehouseBlockedInventoryDto warehouseBlockedInventoryDto = new WarehouseBlockedInventoryDto();
        warehouseBlockedInventoryDto.setQuantity(1);
        when(warehouseBlockedInventoryService.findByProductIdAndFacilityAndLegalOwner(123L,
                "Facility", "Legal Owner")).thenReturn(warehouseBlockedInventoryDto);
        junoInventoryUpdateServiceImpl.setCatalogOpsConnector(new CatalogOpsConnector());
        Product product = new Product();
        product.setClassification(678);
        when(catalogOpsConnector.findByProductId(any())).thenReturn(product);
        ArrayList<InventoryUpdateRequest> inventoryUpdateRequestList = new ArrayList<>();
        inventoryUpdateRequestList.add(inventoryUpdateRequest);
        assertThrows(Exception.class,
                () -> junoInventoryUpdateService.checkUnSyncedCountAndSendUpdateToJuno(inventoryUpdateRequestList));
        verify(inventoryUpdateRequest).getAvailability();
        verify(inventoryUpdateRequest).getCondition();
        verify(inventoryUpdateRequest).getStatus();
        verify(inventoryUpdateRequest, atLeast(1)).getProductId();
        verify(inventoryUpdateRequest, atLeast(1)).getFacility();
        verify(inventoryUpdateRequest, atLeast(1)).getLegalOwner();
    }

    @Test
    void testCheckUnSyncedCountAndSendUpdateToJuno9() throws Exception {
        WarehouseInventoryServiceImpl warehouseInventoryServiceImpl = mock(WarehouseInventoryServiceImpl.class);
        when(warehouseInventoryServiceImpl.search((String) any())).thenReturn(new ArrayList<>());

        JunoInventoryUpdateServiceImpl junoInventoryUpdateServiceImpl = new JunoInventoryUpdateServiceImpl();
        junoInventoryUpdateServiceImpl.setWarehouseInventoryService(warehouseInventoryServiceImpl);
        InventoryUpdateRequest inventoryUpdateRequest = mock(InventoryUpdateRequest.class);
        inventoryUpdateRequest.setAvailability(Availability.AVAILABLE);
        inventoryUpdateRequest.setCondition(Condition.GOOD);
        inventoryUpdateRequest.setStatus(Status.DEFAULT);
        inventoryUpdateRequest.setProductId(123);
        inventoryUpdateRequest.setFacility("Facility");
        inventoryUpdateRequest.setLegalOwner("Legal Owner");
        WarehouseBlockedInventoryDto warehouseBlockedInventoryDto = new WarehouseBlockedInventoryDto();
        warehouseBlockedInventoryDto.setQuantity(1);
        when(warehouseBlockedInventoryService.findByProductIdAndFacilityAndLegalOwner(123L,
                "Facility", "Legal Owner")).thenReturn(warehouseBlockedInventoryDto);
        junoInventoryUpdateServiceImpl.setCatalogOpsConnector(new CatalogOpsConnector());
        Product product = new Product();
        product.setClassification(123);
        when(catalogOpsConnector.findByProductId(any())).thenReturn(product);
        ArrayList<InventoryUpdateRequest> inventoryUpdateRequestList = new ArrayList<>();
        inventoryUpdateRequestList.add(inventoryUpdateRequest);
        junoInventoryUpdateService.checkUnSyncedCountAndSendUpdateToJuno(inventoryUpdateRequestList);
    }
}

