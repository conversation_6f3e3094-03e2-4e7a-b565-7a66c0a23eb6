package com.lenskart.nexs.cid.util;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.lenskart.nexs.cid.dto.WarehouseInventoryDto;
import com.lenskart.nexs.ims.enums.LocationType;
import com.lenskart.nexs.ims.model.Availability;
import com.lenskart.nexs.ims.model.Condition;
import com.lenskart.nexs.ims.model.Status;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.Test;

class ConsolidatedInvCountUtilTest {
    /**
     * Method under test: {@link ConsolidatedInvCountUtil#getTotalAvlWhInv(List)}
     */
    @Test
    void testGetTotalAvlWhInv() {
        assertEquals(0L, ConsolidatedInvCountUtil.getTotalAvlWhInv(new ArrayList<>()));
    }

    /**
     * Method under test: {@link ConsolidatedInvCountUtil#getTotalAvlWhInv(List)}
     */
    @Test
    void testGetTotalAvlWhInv2() {
        WarehouseInventoryDto warehouseInventoryDto = new WarehouseInventoryDto();
        warehouseInventoryDto.setAvailability(Availability.AVAILABLE);
        warehouseInventoryDto.setBlockedQuantity(1);
        warehouseInventoryDto.setCondition(Condition.GOOD);
        LocalDateTime atStartOfDayResult = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto.setCreatedAt(Date.from(atStartOfDayResult.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        warehouseInventoryDto.setEnabled(true);
        warehouseInventoryDto.setFacility("Facility");
        warehouseInventoryDto.setId(123L);
        warehouseInventoryDto.setLegalOwner("Legal Owner");
        warehouseInventoryDto.setLocationType(LocationType.ASRS);
        warehouseInventoryDto.setMasterPid(1);
        warehouseInventoryDto.setProductId(123L);
        warehouseInventoryDto.setQuantity(1);
        LocalDateTime atStartOfDayResult1 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto
                .setReconciliationEventTime(Date.from(atStartOfDayResult1.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto.setStatus(Status.DEFAULT);
        warehouseInventoryDto.setSupplier("Supplier");
        LocalDateTime atStartOfDayResult2 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto.setUpdatedAt(Date.from(atStartOfDayResult2.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto.setUpdatedBy("2020-03-01");
        warehouseInventoryDto.setVersion(1L);

        ArrayList<WarehouseInventoryDto> warehouseInventoryDtoList = new ArrayList<>();
        warehouseInventoryDtoList.add(warehouseInventoryDto);
        assertEquals(0L, ConsolidatedInvCountUtil.getTotalAvlWhInv(warehouseInventoryDtoList));
    }

    /**
     * Method under test: {@link ConsolidatedInvCountUtil#getTotalAvlWhInv(List)}
     */
    @Test
    void testGetTotalAvlWhInv3() {
        WarehouseInventoryDto warehouseInventoryDto = new WarehouseInventoryDto();
        warehouseInventoryDto.setAvailability(Availability.AVAILABLE);
        warehouseInventoryDto.setBlockedQuantity(1);
        warehouseInventoryDto.setCondition(Condition.GOOD);
        LocalDateTime atStartOfDayResult = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto.setCreatedAt(Date.from(atStartOfDayResult.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        warehouseInventoryDto.setEnabled(true);
        warehouseInventoryDto.setFacility("Facility");
        warehouseInventoryDto.setId(123L);
        warehouseInventoryDto.setLegalOwner("Legal Owner");
        warehouseInventoryDto.setLocationType(LocationType.ASRS);
        warehouseInventoryDto.setMasterPid(1);
        warehouseInventoryDto.setProductId(123L);
        warehouseInventoryDto.setQuantity(1);
        LocalDateTime atStartOfDayResult1 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto
                .setReconciliationEventTime(Date.from(atStartOfDayResult1.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto.setStatus(Status.DEFAULT);
        warehouseInventoryDto.setSupplier("Supplier");
        LocalDateTime atStartOfDayResult2 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto.setUpdatedAt(Date.from(atStartOfDayResult2.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto.setUpdatedBy("2020-03-01");
        warehouseInventoryDto.setVersion(1L);

        WarehouseInventoryDto warehouseInventoryDto1 = new WarehouseInventoryDto();
        warehouseInventoryDto1.setAvailability(Availability.AVAILABLE);
        warehouseInventoryDto1.setBlockedQuantity(5);
        warehouseInventoryDto1.setCondition(Condition.GOOD);
        LocalDateTime atStartOfDayResult3 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto1.setCreatedAt(Date.from(atStartOfDayResult3.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto1.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        warehouseInventoryDto1.setEnabled(true);
        warehouseInventoryDto1.setFacility("Facility");
        warehouseInventoryDto1.setId(123L);
        warehouseInventoryDto1.setLegalOwner("Legal Owner");
        warehouseInventoryDto1.setLocationType(LocationType.ASRS);
        warehouseInventoryDto1.setMasterPid(1);
        warehouseInventoryDto1.setProductId(123L);
        warehouseInventoryDto1.setQuantity(5);
        LocalDateTime atStartOfDayResult4 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto1
                .setReconciliationEventTime(Date.from(atStartOfDayResult4.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto1.setStatus(Status.DEFAULT);
        warehouseInventoryDto1.setSupplier("Supplier");
        LocalDateTime atStartOfDayResult5 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto1.setUpdatedAt(Date.from(atStartOfDayResult5.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto1.setUpdatedBy("2020-03-01");
        warehouseInventoryDto1.setVersion(5L);

        ArrayList<WarehouseInventoryDto> warehouseInventoryDtoList = new ArrayList<>();
        warehouseInventoryDtoList.add(warehouseInventoryDto1);
        warehouseInventoryDtoList.add(warehouseInventoryDto);
        assertEquals(0L, ConsolidatedInvCountUtil.getTotalAvlWhInv(warehouseInventoryDtoList));
    }

    /**
     * Method under test: {@link ConsolidatedInvCountUtil#getReservedAvlWhInv(List)}
     */
    @Test
    void testGetReservedAvlWhInv() {
        assertEquals(0L, ConsolidatedInvCountUtil.getReservedAvlWhInv(new ArrayList<>()));
    }

    /**
     * Method under test: {@link ConsolidatedInvCountUtil#getReservedAvlWhInv(List)}
     */
    @Test
    void testGetReservedAvlWhInv2() {
        WarehouseInventoryDto warehouseInventoryDto = new WarehouseInventoryDto();
        warehouseInventoryDto.setAvailability(Availability.AVAILABLE);
        warehouseInventoryDto.setBlockedQuantity(1);
        warehouseInventoryDto.setCondition(Condition.GOOD);
        LocalDateTime atStartOfDayResult = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto.setCreatedAt(Date.from(atStartOfDayResult.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        warehouseInventoryDto.setEnabled(true);
        warehouseInventoryDto.setFacility("Facility");
        warehouseInventoryDto.setId(123L);
        warehouseInventoryDto.setLegalOwner("Legal Owner");
        warehouseInventoryDto.setLocationType(LocationType.ASRS);
        warehouseInventoryDto.setMasterPid(1);
        warehouseInventoryDto.setProductId(123L);
        warehouseInventoryDto.setQuantity(1);
        LocalDateTime atStartOfDayResult1 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto
                .setReconciliationEventTime(Date.from(atStartOfDayResult1.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto.setStatus(Status.DEFAULT);
        warehouseInventoryDto.setSupplier("Supplier");
        LocalDateTime atStartOfDayResult2 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto.setUpdatedAt(Date.from(atStartOfDayResult2.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto.setUpdatedBy("2020-03-01");
        warehouseInventoryDto.setVersion(1L);

        ArrayList<WarehouseInventoryDto> warehouseInventoryDtoList = new ArrayList<>();
        warehouseInventoryDtoList.add(warehouseInventoryDto);
        assertEquals(0L, ConsolidatedInvCountUtil.getReservedAvlWhInv(warehouseInventoryDtoList));
    }

    /**
     * Method under test: {@link ConsolidatedInvCountUtil#getReservedAvlWhInv(List)}
     */
    @Test
    void testGetReservedAvlWhInv3() {
        WarehouseInventoryDto warehouseInventoryDto = new WarehouseInventoryDto();
        warehouseInventoryDto.setAvailability(Availability.AVAILABLE);
        warehouseInventoryDto.setBlockedQuantity(1);
        warehouseInventoryDto.setCondition(Condition.GOOD);
        LocalDateTime atStartOfDayResult = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto.setCreatedAt(Date.from(atStartOfDayResult.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        warehouseInventoryDto.setEnabled(true);
        warehouseInventoryDto.setFacility("Facility");
        warehouseInventoryDto.setId(123L);
        warehouseInventoryDto.setLegalOwner("Legal Owner");
        warehouseInventoryDto.setLocationType(LocationType.ASRS);
        warehouseInventoryDto.setMasterPid(1);
        warehouseInventoryDto.setProductId(123L);
        warehouseInventoryDto.setQuantity(1);
        LocalDateTime atStartOfDayResult1 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto
                .setReconciliationEventTime(Date.from(atStartOfDayResult1.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto.setStatus(Status.DEFAULT);
        warehouseInventoryDto.setSupplier("Supplier");
        LocalDateTime atStartOfDayResult2 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto.setUpdatedAt(Date.from(atStartOfDayResult2.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto.setUpdatedBy("2020-03-01");
        warehouseInventoryDto.setVersion(1L);

        WarehouseInventoryDto warehouseInventoryDto1 = new WarehouseInventoryDto();
        warehouseInventoryDto1.setAvailability(Availability.AVAILABLE);
        warehouseInventoryDto1.setBlockedQuantity(5);
        warehouseInventoryDto1.setCondition(Condition.GOOD);
        LocalDateTime atStartOfDayResult3 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto1.setCreatedAt(Date.from(atStartOfDayResult3.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto1.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        warehouseInventoryDto1.setEnabled(true);
        warehouseInventoryDto1.setFacility("Facility");
        warehouseInventoryDto1.setId(123L);
        warehouseInventoryDto1.setLegalOwner("Legal Owner");
        warehouseInventoryDto1.setLocationType(LocationType.ASRS);
        warehouseInventoryDto1.setMasterPid(1);
        warehouseInventoryDto1.setProductId(123L);
        warehouseInventoryDto1.setQuantity(5);
        LocalDateTime atStartOfDayResult4 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto1
                .setReconciliationEventTime(Date.from(atStartOfDayResult4.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto1.setStatus(Status.DEFAULT);
        warehouseInventoryDto1.setSupplier("Supplier");
        LocalDateTime atStartOfDayResult5 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto1.setUpdatedAt(Date.from(atStartOfDayResult5.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto1.setUpdatedBy("2020-03-01");
        warehouseInventoryDto1.setVersion(5L);

        ArrayList<WarehouseInventoryDto> warehouseInventoryDtoList = new ArrayList<>();
        warehouseInventoryDtoList.add(warehouseInventoryDto1);
        warehouseInventoryDtoList.add(warehouseInventoryDto);
        assertEquals(0L, ConsolidatedInvCountUtil.getReservedAvlWhInv(warehouseInventoryDtoList));
    }

    /**
     * Method under test: {@link ConsolidatedInvCountUtil#getVirtualWhInv(List)}
     */
    @Test
    void testGetVirtualWhInv() {
        assertEquals(0L, ConsolidatedInvCountUtil.getVirtualWhInv(new ArrayList<>()));
    }

    /**
     * Method under test: {@link ConsolidatedInvCountUtil#getVirtualWhInv(List)}
     */
    @Test
    void testGetVirtualWhInv2() {
        WarehouseInventoryDto warehouseInventoryDto = new WarehouseInventoryDto();
        warehouseInventoryDto.setAvailability(Availability.AVAILABLE);
        warehouseInventoryDto.setBlockedQuantity(1);
        warehouseInventoryDto.setCondition(Condition.GOOD);
        LocalDateTime atStartOfDayResult = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto.setCreatedAt(Date.from(atStartOfDayResult.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        warehouseInventoryDto.setEnabled(true);
        warehouseInventoryDto.setFacility("Facility");
        warehouseInventoryDto.setId(123L);
        warehouseInventoryDto.setLegalOwner("Legal Owner");
        warehouseInventoryDto.setLocationType(LocationType.ASRS);
        warehouseInventoryDto.setMasterPid(1);
        warehouseInventoryDto.setProductId(123L);
        warehouseInventoryDto.setQuantity(1);
        LocalDateTime atStartOfDayResult1 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto
                .setReconciliationEventTime(Date.from(atStartOfDayResult1.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto.setStatus(Status.DEFAULT);
        warehouseInventoryDto.setSupplier("Supplier");
        LocalDateTime atStartOfDayResult2 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto.setUpdatedAt(Date.from(atStartOfDayResult2.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto.setUpdatedBy("2020-03-01");
        warehouseInventoryDto.setVersion(1L);

        ArrayList<WarehouseInventoryDto> warehouseInventoryDtoList = new ArrayList<>();
        warehouseInventoryDtoList.add(warehouseInventoryDto);
        assertEquals(0L, ConsolidatedInvCountUtil.getVirtualWhInv(warehouseInventoryDtoList));
    }

    /**
     * Method under test: {@link ConsolidatedInvCountUtil#getVirtualWhInv(List)}
     */
    @Test
    void testGetVirtualWhInv3() {
        WarehouseInventoryDto warehouseInventoryDto = new WarehouseInventoryDto();
        warehouseInventoryDto.setAvailability(Availability.AVAILABLE);
        warehouseInventoryDto.setBlockedQuantity(1);
        warehouseInventoryDto.setCondition(Condition.GOOD);
        LocalDateTime atStartOfDayResult = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto.setCreatedAt(Date.from(atStartOfDayResult.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        warehouseInventoryDto.setEnabled(true);
        warehouseInventoryDto.setFacility("Facility");
        warehouseInventoryDto.setId(123L);
        warehouseInventoryDto.setLegalOwner("Legal Owner");
        warehouseInventoryDto.setLocationType(LocationType.ASRS);
        warehouseInventoryDto.setMasterPid(1);
        warehouseInventoryDto.setProductId(123L);
        warehouseInventoryDto.setQuantity(1);
        LocalDateTime atStartOfDayResult1 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto
                .setReconciliationEventTime(Date.from(atStartOfDayResult1.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto.setStatus(Status.DEFAULT);
        warehouseInventoryDto.setSupplier("Supplier");
        LocalDateTime atStartOfDayResult2 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto.setUpdatedAt(Date.from(atStartOfDayResult2.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto.setUpdatedBy("2020-03-01");
        warehouseInventoryDto.setVersion(1L);

        WarehouseInventoryDto warehouseInventoryDto1 = new WarehouseInventoryDto();
        warehouseInventoryDto1.setAvailability(Availability.AVAILABLE);
        warehouseInventoryDto1.setBlockedQuantity(5);
        warehouseInventoryDto1.setCondition(Condition.GOOD);
        LocalDateTime atStartOfDayResult3 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto1.setCreatedAt(Date.from(atStartOfDayResult3.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto1.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        warehouseInventoryDto1.setEnabled(true);
        warehouseInventoryDto1.setFacility("Facility");
        warehouseInventoryDto1.setId(123L);
        warehouseInventoryDto1.setLegalOwner("Legal Owner");
        warehouseInventoryDto1.setLocationType(LocationType.ASRS);
        warehouseInventoryDto1.setMasterPid(1);
        warehouseInventoryDto1.setProductId(123L);
        warehouseInventoryDto1.setQuantity(5);
        LocalDateTime atStartOfDayResult4 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto1
                .setReconciliationEventTime(Date.from(atStartOfDayResult4.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto1.setStatus(Status.DEFAULT);
        warehouseInventoryDto1.setSupplier("Supplier");
        LocalDateTime atStartOfDayResult5 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseInventoryDto1.setUpdatedAt(Date.from(atStartOfDayResult5.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseInventoryDto1.setUpdatedBy("2020-03-01");
        warehouseInventoryDto1.setVersion(5L);

        ArrayList<WarehouseInventoryDto> warehouseInventoryDtoList = new ArrayList<>();
        warehouseInventoryDtoList.add(warehouseInventoryDto1);
        warehouseInventoryDtoList.add(warehouseInventoryDto);
        assertEquals(0L, ConsolidatedInvCountUtil.getVirtualWhInv(warehouseInventoryDtoList));
    }
}

