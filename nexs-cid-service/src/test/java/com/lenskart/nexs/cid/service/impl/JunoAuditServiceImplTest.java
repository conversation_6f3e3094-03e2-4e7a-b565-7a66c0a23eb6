package com.lenskart.nexs.cid.service.impl;

import com.lenskart.nexs.cid.dto.JunoAuditLogDto;
import com.lenskart.nexs.cid.dto.JunoAuditStatsDto;
import com.lenskart.nexs.cid.entity.JunoAuditLog;
import com.lenskart.nexs.cid.enums.JunoApiStatus;
import com.lenskart.nexs.cid.repository.JunoAuditLogRepository;
import com.lenskart.nexs.cid.request.juno.JunoInventoryTransaction;
import com.lenskart.nexs.cid.request.juno.JunoInventoryUpdateRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class JunoAuditServiceImplTest {

    @Mock
    private JunoAuditLogRepository junoAuditLogRepository;

    @InjectMocks
    private JunoAuditServiceImpl junoAuditService;

    private JunoInventoryUpdateRequest testRequest;
    private JunoAuditLog testAuditLog;

    @BeforeEach
    void setUp() {
        // Create test request
        JunoInventoryTransaction transaction = new JunoInventoryTransaction();
        transaction.setProductId(12345);
        transaction.setFacilityCode("FC001");
        transaction.setQuantity(10);
        transaction.setTransactionType("EXACT");
        transaction.setLegalOwner("LK");

        testRequest = new JunoInventoryUpdateRequest();
        testRequest.setTransactions(Arrays.asList(transaction));
        testRequest.setSource("CID");

        // Create test audit log
        testAuditLog = new JunoAuditLog();
        testAuditLog.setId(1L);
        testAuditLog.setCorrelationId("test-correlation-id");
        testAuditLog.setStatus(JunoApiStatus.PENDING);
        testAuditLog.setRequestTimestamp(new Date());
        testAuditLog.setRetryCount(0);
    }

    @Test
    void testCreateAuditLog() {
        // Given
        String correlationId = "test-correlation-id";
        String apiEndpoint = "http://test-api/endpoint";
        
        when(junoAuditLogRepository.save(any(JunoAuditLog.class))).thenReturn(testAuditLog);

        // When
        JunoAuditLog result = junoAuditService.createAuditLog(correlationId, testRequest, apiEndpoint);

        // Then
        assertNotNull(result);
        assertEquals(correlationId, result.getCorrelationId());
        assertEquals(JunoApiStatus.PENDING, result.getStatus());
        verify(junoAuditLogRepository).save(any(JunoAuditLog.class));
    }

    @Test
    void testUpdateAuditLogWithResponse() {
        // Given
        Long auditLogId = 1L;
        String responsePayload = "success response";
        JunoApiStatus status = JunoApiStatus.SUCCESS;
        Integer httpStatusCode = 200;
        Long durationMs = 1000L;

        when(junoAuditLogRepository.findById(auditLogId)).thenReturn(Optional.of(testAuditLog));
        when(junoAuditLogRepository.save(any(JunoAuditLog.class))).thenReturn(testAuditLog);

        // When
        junoAuditService.updateAuditLogWithResponse(auditLogId, responsePayload, status, httpStatusCode, durationMs);

        // Then
        verify(junoAuditLogRepository).findById(auditLogId);
        verify(junoAuditLogRepository).save(any(JunoAuditLog.class));
    }

    @Test
    void testUpdateAuditLogWithError() {
        // Given
        Long auditLogId = 1L;
        String errorMessage = "Test error";
        String stackTrace = "Test stack trace";
        JunoApiStatus status = JunoApiStatus.FAILED;
        Integer httpStatusCode = 500;
        Long durationMs = 2000L;

        when(junoAuditLogRepository.findById(auditLogId)).thenReturn(Optional.of(testAuditLog));
        when(junoAuditLogRepository.save(any(JunoAuditLog.class))).thenReturn(testAuditLog);

        // When
        junoAuditService.updateAuditLogWithError(auditLogId, errorMessage, stackTrace, status, httpStatusCode, durationMs);

        // Then
        verify(junoAuditLogRepository).findById(auditLogId);
        verify(junoAuditLogRepository).save(any(JunoAuditLog.class));
    }

    @Test
    void testIncrementRetryCount() {
        // Given
        Long auditLogId = 1L;
        testAuditLog.setRetryCount(2);

        when(junoAuditLogRepository.findById(auditLogId)).thenReturn(Optional.of(testAuditLog));
        when(junoAuditLogRepository.save(any(JunoAuditLog.class))).thenReturn(testAuditLog);

        // When
        junoAuditService.incrementRetryCount(auditLogId);

        // Then
        verify(junoAuditLogRepository).findById(auditLogId);
        verify(junoAuditLogRepository).save(any(JunoAuditLog.class));
    }

    @Test
    void testFindByCorrelationId() {
        // Given
        String correlationId = "test-correlation-id";
        List<JunoAuditLog> auditLogs = Arrays.asList(testAuditLog);

        when(junoAuditLogRepository.findByCorrelationIdOrderByCreatedAtDesc(correlationId)).thenReturn(auditLogs);

        // When
        List<JunoAuditLogDto> result = junoAuditService.findByCorrelationId(correlationId);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(correlationId, result.get(0).getCorrelationId());
        verify(junoAuditLogRepository).findByCorrelationIdOrderByCreatedAtDesc(correlationId);
    }

    @Test
    void testFindByStatus() {
        // Given
        JunoApiStatus status = JunoApiStatus.SUCCESS;
        Pageable pageable = PageRequest.of(0, 10);
        Page<JunoAuditLog> auditLogsPage = new PageImpl<>(Arrays.asList(testAuditLog));

        when(junoAuditLogRepository.findByStatusOrderByCreatedAtDesc(status, pageable)).thenReturn(auditLogsPage);

        // When
        Page<JunoAuditLogDto> result = junoAuditService.findByStatus(status, pageable);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        verify(junoAuditLogRepository).findByStatusOrderByCreatedAtDesc(status, pageable);
    }

    @Test
    void testGetAuditStats() {
        // Given
        Date startDate = new Date();
        Date endDate = new Date();

        when(junoAuditLogRepository.countByDateRange(startDate, endDate)).thenReturn(100L);
        when(junoAuditLogRepository.countByStatusAndDateRange(JunoApiStatus.SUCCESS, startDate, endDate)).thenReturn(80L);
        when(junoAuditLogRepository.countByStatusAndDateRange(JunoApiStatus.FAILED, startDate, endDate)).thenReturn(15L);
        when(junoAuditLogRepository.countByStatusAndDateRange(JunoApiStatus.TIMEOUT, startDate, endDate)).thenReturn(3L);
        when(junoAuditLogRepository.countByStatusAndDateRange(JunoApiStatus.RETRY, startDate, endDate)).thenReturn(2L);

        // When
        JunoAuditStatsDto result = junoAuditService.getAuditStats(startDate, endDate);

        // Then
        assertNotNull(result);
        assertEquals(100L, result.getTotalRequests());
        assertEquals(80L, result.getSuccessfulRequests());
        assertEquals(15L, result.getFailedRequests());
        assertEquals(80.0, result.getSuccessRate());
        assertEquals(15.0, result.getFailureRate());
    }

    @Test
    void testUpdateAuditLogWithResponse_NotFound() {
        // Given
        Long auditLogId = 999L;
        when(junoAuditLogRepository.findById(auditLogId)).thenReturn(Optional.empty());

        // When
        junoAuditService.updateAuditLogWithResponse(auditLogId, "response", JunoApiStatus.SUCCESS, 200, 1000L);

        // Then
        verify(junoAuditLogRepository).findById(auditLogId);
        verify(junoAuditLogRepository, never()).save(any(JunoAuditLog.class));
    }
}
