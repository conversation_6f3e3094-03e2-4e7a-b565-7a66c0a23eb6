package com.lenskart.nexs.cid.service.impl;

import com.lenskart.nexs.cid.entity.JunoAuditLog;
import com.lenskart.nexs.cid.enums.JunoApiStatus;
import com.lenskart.nexs.cid.repository.JunoAuditLogRepository;
import com.lenskart.nexs.cid.request.juno.JunoInventoryTransaction;
import com.lenskart.nexs.cid.request.juno.JunoInventoryUpdateRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class JunoAuditServiceImplTest {

    @Mock
    private JunoAuditLogRepository junoAuditLogRepository;

    @InjectMocks
    private JunoAuditServiceImpl junoAuditService;

    private JunoInventoryUpdateRequest testRequest;

    @BeforeEach
    void setUp() {
        // Create test request
        JunoInventoryTransaction transaction = new JunoInventoryTransaction();
        transaction.setProductId(12345);
        transaction.setFacilityCode("FC001");
        transaction.setQuantity(10);
        transaction.setTransactionType("EXACT");
        transaction.setLegalOwner("LK");
        transaction.setUpdateTime("1234567890");

        testRequest = new JunoInventoryUpdateRequest();
        testRequest.setTransactions(Arrays.asList(transaction));
        testRequest.setSource("CID");
    }

    @Test
    void testCreateAuditLogs() {
        // Given
        String correlationId = "test-correlation-id";
        String apiEndpoint = "http://test-api/endpoint";

        when(junoAuditLogRepository.save(any(JunoAuditLog.class))).thenReturn(new JunoAuditLog());

        // When
        junoAuditService.createAuditLogs(correlationId, testRequest, apiEndpoint);

        // Then
        verify(junoAuditLogRepository, times(1)).save(any(JunoAuditLog.class));
    }

    @Test
    void testUpdateAuditLogsWithResponse() {
        // Given
        String correlationId = "test-correlation-id";
        String responsePayload = "success response";
        JunoApiStatus status = JunoApiStatus.SUCCESS;
        Integer httpStatusCode = 200;
        Long durationMs = 1000L;

        JunoAuditLog auditLog = new JunoAuditLog();
        auditLog.setCorrelationId(correlationId);
        List<JunoAuditLog> auditLogs = Arrays.asList(auditLog);

        when(junoAuditLogRepository.findByCorrelationIdOrderByCreatedAtDesc(correlationId)).thenReturn(auditLogs);
        when(junoAuditLogRepository.save(any(JunoAuditLog.class))).thenReturn(auditLog);

        // When
        junoAuditService.updateAuditLogsWithResponse(correlationId, responsePayload, status, httpStatusCode, durationMs);

        // Then
        verify(junoAuditLogRepository).findByCorrelationIdOrderByCreatedAtDesc(correlationId);
        verify(junoAuditLogRepository, times(1)).save(any(JunoAuditLog.class));
    }

    @Test
    void testUpdateAuditLogsWithError() {
        // Given
        String correlationId = "test-correlation-id";
        String errorMessage = "Test error";
        JunoApiStatus status = JunoApiStatus.FAILED;
        Integer httpStatusCode = 500;
        Long durationMs = 2000L;

        JunoAuditLog auditLog = new JunoAuditLog();
        auditLog.setCorrelationId(correlationId);
        List<JunoAuditLog> auditLogs = Arrays.asList(auditLog);

        when(junoAuditLogRepository.findByCorrelationIdOrderByCreatedAtDesc(correlationId)).thenReturn(auditLogs);
        when(junoAuditLogRepository.save(any(JunoAuditLog.class))).thenReturn(auditLog);

        // When
        junoAuditService.updateAuditLogsWithError(correlationId, errorMessage, status, httpStatusCode, durationMs);

        // Then
        verify(junoAuditLogRepository).findByCorrelationIdOrderByCreatedAtDesc(correlationId);
        verify(junoAuditLogRepository, times(1)).save(any(JunoAuditLog.class));
    }

    @Test
    void testUpdateAuditLogsWithResponse_NotFound() {
        // Given
        String correlationId = "non-existent-correlation-id";
        when(junoAuditLogRepository.findByCorrelationIdOrderByCreatedAtDesc(correlationId)).thenReturn(Arrays.asList());

        // When
        junoAuditService.updateAuditLogsWithResponse(correlationId, "response", JunoApiStatus.SUCCESS, 200, 1000L);

        // Then
        verify(junoAuditLogRepository).findByCorrelationIdOrderByCreatedAtDesc(correlationId);
        verify(junoAuditLogRepository, never()).save(any(JunoAuditLog.class));
    }
}
