package com.lenskart.nexs.cid.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.cid.dto.WarehouseBlockedInventoryDto;
import com.lenskart.nexs.cid.entity.StorefrontInventory;
import com.lenskart.nexs.cid.service.WarehouseBlockedInventoryService;
import com.lenskart.nexs.commons.entity.BaseEntity;
import com.lenskart.nexs.commons.repository.BaseRepository;
import com.lenskart.nexs.ims.response.ConsolidatedInvInfo;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;

import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ContextConfiguration(classes = {WarehouseInventoryServiceImpl.class})
@ExtendWith(SpringExtension.class)
class WarehouseInventoryServiceImplTest {
    @MockBean
    private BaseRepository<BaseEntity> baseRepository;

    @MockBean
    private BaseRepository baseRepository1;

    @MockBean
    private ObjectMapper objectMapper;

    @MockBean
    private WarehouseBlockedInventoryService warehouseBlockedInventoryService;

    @Autowired
    private WarehouseInventoryServiceImpl warehouseInventoryServiceImpl;

    @MockBean
    private static Logger logger;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(warehouseInventoryServiceImpl, "logger", logger);
    }

    /**
     * Method under test: {@link WarehouseInventoryServiceImpl#getConsolidatedInvInfo(String, Integer, String)}
     */
    @Test
    @Disabled("TODO: Complete this test")
    void testGetConsolidatedInvInfo() {
        WarehouseBlockedInventoryDto warehouseBlockedInventoryDto = new WarehouseBlockedInventoryDto();
        LocalDateTime atStartOfDayResult = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseBlockedInventoryDto.setCreatedAt(Date.from(atStartOfDayResult.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseBlockedInventoryDto.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        warehouseBlockedInventoryDto.setFacility("Facility");
        warehouseBlockedInventoryDto.setId(123L);
        warehouseBlockedInventoryDto.setLegalOwner("Legal Owner");
        warehouseBlockedInventoryDto.setProductId(123L);
        warehouseBlockedInventoryDto.setQuantity(1);
        LocalDateTime atStartOfDayResult1 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseBlockedInventoryDto.setUpdatedAt(Date.from(atStartOfDayResult1.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseBlockedInventoryDto.setUpdatedBy("2020-03-01");
        warehouseBlockedInventoryDto.setVersion(1L);
        when(warehouseBlockedInventoryService.findByProductIdAndFacilityAndLegalOwner((Long) any(), (String) any(),
                (String) any())).thenReturn(warehouseBlockedInventoryDto);
        when(baseRepository.findAll((Specification<BaseEntity>) any())).thenReturn(new ArrayList<>());
        ConsolidatedInvInfo actualConsolidatedInvInfo = warehouseInventoryServiceImpl
                .getConsolidatedInvInfo("Facility Code", 123, "Legal Owner");
        assertEquals(0L, actualConsolidatedInvInfo.getAllocatedInventory().longValue());
        assertEquals(0L, actualConsolidatedInvInfo.getVirtualInventory().longValue());
        assertEquals(0L, actualConsolidatedInvInfo.getTotalInventory().longValue());
        assertEquals(0L, actualConsolidatedInvInfo.getReservedInventory().longValue());
        assertEquals(0L, actualConsolidatedInvInfo.getPendingPutAwayInventory().longValue());
        assertEquals(0L, actualConsolidatedInvInfo.getGrnInventory().longValue());
        assertEquals(1L, actualConsolidatedInvInfo.getBlockedInventory().longValue());
        assertEquals(-1L, actualConsolidatedInvInfo.getAvailableInventory().longValue());
        verify(warehouseBlockedInventoryService).findByProductIdAndFacilityAndLegalOwner((Long) any(), (String) any(),
                (String) any());
        verify(baseRepository).findAll((Specification<BaseEntity>) any());
    }

    /**
     * Method under test: {@link WarehouseInventoryServiceImpl#getConsolidatedInvInfo(String, Integer, String)}
     */
    @Test
    @Disabled("TODO: Complete this test")
    void testGetConsolidatedInvInfo2() {
        WarehouseBlockedInventoryDto warehouseBlockedInventoryDto = mock(WarehouseBlockedInventoryDto.class);
        when(warehouseBlockedInventoryDto.getQuantity()).thenReturn(1);
        doNothing().when(warehouseBlockedInventoryDto).setFacility((String) any());
        doNothing().when(warehouseBlockedInventoryDto).setLegalOwner((String) any());
        doNothing().when(warehouseBlockedInventoryDto).setProductId((Long) any());
        doNothing().when(warehouseBlockedInventoryDto).setQuantity((Integer) any());
        doNothing().when(warehouseBlockedInventoryDto).setCreatedAt((Date) any());
        doNothing().when(warehouseBlockedInventoryDto).setCreatedBy((String) any());
        doNothing().when(warehouseBlockedInventoryDto).setId((Long) any());
        doNothing().when(warehouseBlockedInventoryDto).setUpdatedAt((Date) any());
        doNothing().when(warehouseBlockedInventoryDto).setUpdatedBy((String) any());
        doNothing().when(warehouseBlockedInventoryDto).setVersion((Long) any());
        LocalDateTime atStartOfDayResult = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseBlockedInventoryDto.setCreatedAt(Date.from(atStartOfDayResult.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseBlockedInventoryDto.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        warehouseBlockedInventoryDto.setFacility("Facility");
        warehouseBlockedInventoryDto.setId(123L);
        warehouseBlockedInventoryDto.setLegalOwner("Legal Owner");
        warehouseBlockedInventoryDto.setProductId(123L);
        warehouseBlockedInventoryDto.setQuantity(1);
        LocalDateTime atStartOfDayResult1 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseBlockedInventoryDto.setUpdatedAt(Date.from(atStartOfDayResult1.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseBlockedInventoryDto.setUpdatedBy("2020-03-01");
        warehouseBlockedInventoryDto.setVersion(1L);
        when(warehouseBlockedInventoryService.findByProductIdAndFacilityAndLegalOwner((Long) any(), (String) any(),
                (String) any())).thenReturn(warehouseBlockedInventoryDto);
        when(baseRepository.findAll((Specification<BaseEntity>) any())).thenReturn(new ArrayList<>());
        ConsolidatedInvInfo actualConsolidatedInvInfo = warehouseInventoryServiceImpl
                .getConsolidatedInvInfo("Facility Code", 123, "Legal Owner");
        assertEquals(0L, actualConsolidatedInvInfo.getAllocatedInventory().longValue());
        assertEquals(0L, actualConsolidatedInvInfo.getVirtualInventory().longValue());
        assertEquals(0L, actualConsolidatedInvInfo.getTotalInventory().longValue());
        assertEquals(0L, actualConsolidatedInvInfo.getReservedInventory().longValue());
        assertEquals(0L, actualConsolidatedInvInfo.getPendingPutAwayInventory().longValue());
        assertEquals(0L, actualConsolidatedInvInfo.getGrnInventory().longValue());
        assertEquals(1L, actualConsolidatedInvInfo.getBlockedInventory().longValue());
        assertEquals(-1L, actualConsolidatedInvInfo.getAvailableInventory().longValue());
        verify(warehouseBlockedInventoryService).findByProductIdAndFacilityAndLegalOwner((Long) any(), (String) any(),
                (String) any());
        verify(warehouseBlockedInventoryDto, atLeast(1)).getQuantity();
        verify(warehouseBlockedInventoryDto).setFacility((String) any());
        verify(warehouseBlockedInventoryDto).setLegalOwner((String) any());
        verify(warehouseBlockedInventoryDto).setProductId((Long) any());
        verify(warehouseBlockedInventoryDto).setQuantity((Integer) any());
        verify(warehouseBlockedInventoryDto).setCreatedAt((Date) any());
        verify(warehouseBlockedInventoryDto).setCreatedBy((String) any());
        verify(warehouseBlockedInventoryDto).setId((Long) any());
        verify(warehouseBlockedInventoryDto).setUpdatedAt((Date) any());
        verify(warehouseBlockedInventoryDto).setUpdatedBy((String) any());
        verify(warehouseBlockedInventoryDto).setVersion((Long) any());
        verify(baseRepository).findAll((Specification<BaseEntity>) any());
    }

    /**
     * Method under test: {@link WarehouseInventoryServiceImpl#getConsolidatedInvInfo(String, Integer, String)}
     */
    @Test
    @Disabled("TODO: Complete this test")
    void testGetConsolidatedInvInfo3() {
        WarehouseBlockedInventoryDto warehouseBlockedInventoryDto = mock(WarehouseBlockedInventoryDto.class);
        when(warehouseBlockedInventoryDto.getQuantity()).thenReturn(null);
        doNothing().when(warehouseBlockedInventoryDto).setFacility((String) any());
        doNothing().when(warehouseBlockedInventoryDto).setLegalOwner((String) any());
        doNothing().when(warehouseBlockedInventoryDto).setProductId((Long) any());
        doNothing().when(warehouseBlockedInventoryDto).setQuantity((Integer) any());
        doNothing().when(warehouseBlockedInventoryDto).setCreatedAt((Date) any());
        doNothing().when(warehouseBlockedInventoryDto).setCreatedBy((String) any());
        doNothing().when(warehouseBlockedInventoryDto).setId((Long) any());
        doNothing().when(warehouseBlockedInventoryDto).setUpdatedAt((Date) any());
        doNothing().when(warehouseBlockedInventoryDto).setUpdatedBy((String) any());
        doNothing().when(warehouseBlockedInventoryDto).setVersion((Long) any());
        LocalDateTime atStartOfDayResult = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseBlockedInventoryDto.setCreatedAt(Date.from(atStartOfDayResult.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseBlockedInventoryDto.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        warehouseBlockedInventoryDto.setFacility("Facility");
        warehouseBlockedInventoryDto.setId(123L);
        warehouseBlockedInventoryDto.setLegalOwner("Legal Owner");
        warehouseBlockedInventoryDto.setProductId(123L);
        warehouseBlockedInventoryDto.setQuantity(1);
        LocalDateTime atStartOfDayResult1 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseBlockedInventoryDto.setUpdatedAt(Date.from(atStartOfDayResult1.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseBlockedInventoryDto.setUpdatedBy("2020-03-01");
        warehouseBlockedInventoryDto.setVersion(1L);
        when(warehouseBlockedInventoryService.findByProductIdAndFacilityAndLegalOwner((Long) any(), (String) any(),
                (String) any())).thenReturn(warehouseBlockedInventoryDto);
        when(baseRepository.findAll((Specification<BaseEntity>) any())).thenReturn(new ArrayList<>());
        ConsolidatedInvInfo actualConsolidatedInvInfo = warehouseInventoryServiceImpl
                .getConsolidatedInvInfo("Facility Code", 123, "Legal Owner");
        assertEquals(0L, actualConsolidatedInvInfo.getAllocatedInventory().longValue());
        assertEquals(0L, actualConsolidatedInvInfo.getVirtualInventory().longValue());
        assertEquals(0L, actualConsolidatedInvInfo.getTotalInventory().longValue());
        assertEquals(0L, actualConsolidatedInvInfo.getReservedInventory().longValue());
        assertEquals(0L, actualConsolidatedInvInfo.getPendingPutAwayInventory().longValue());
        assertEquals(0L, actualConsolidatedInvInfo.getGrnInventory().longValue());
        assertEquals(0L, actualConsolidatedInvInfo.getBlockedInventory().longValue());
        assertEquals(0L, actualConsolidatedInvInfo.getAvailableInventory().longValue());
        verify(warehouseBlockedInventoryService).findByProductIdAndFacilityAndLegalOwner((Long) any(), (String) any(),
                (String) any());
        verify(warehouseBlockedInventoryDto).getQuantity();
        verify(warehouseBlockedInventoryDto).setFacility((String) any());
        verify(warehouseBlockedInventoryDto).setLegalOwner((String) any());
        verify(warehouseBlockedInventoryDto).setProductId((Long) any());
        verify(warehouseBlockedInventoryDto).setQuantity((Integer) any());
        verify(warehouseBlockedInventoryDto).setCreatedAt((Date) any());
        verify(warehouseBlockedInventoryDto).setCreatedBy((String) any());
        verify(warehouseBlockedInventoryDto).setId((Long) any());
        verify(warehouseBlockedInventoryDto).setUpdatedAt((Date) any());
        verify(warehouseBlockedInventoryDto).setUpdatedBy((String) any());
        verify(warehouseBlockedInventoryDto).setVersion((Long) any());
        verify(baseRepository).findAll((Specification<BaseEntity>) any());
    }

    /**
     * Method under test: {@link WarehouseInventoryServiceImpl#getConsolidatedInvInfo(String, Integer, String)}
     */
    @Test
    @Disabled("TODO: Complete this test")
    void testGetConsolidatedInvInfo4() {
        // TODO: Complete this test.
        //   Reason: R013 No inputs found that don't throw a trivial exception.
        //   Diffblue Cover tried to run the arrange/act section, but the method under
        //   test threw
        //   java.lang.NullPointerException
        //       at java.util.stream.ReduceOps$1ReducingSink.accept(ReduceOps.java:80)
        //       at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
        //       at java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:175)
        //       at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
        //       at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
        //       at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
        //       at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
        //       at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.util.stream.ReferencePipeline.reduce(ReferencePipeline.java:541)
        //       at com.lenskart.nexs.cid.util.ConsolidatedInvCountUtil.getTotalInventory(ConsolidatedInvCountUtil.java:63)
        //       at com.lenskart.nexs.cid.service.impl.WarehouseInventoryServiceImpl.getConsolidatedInvInfo(WarehouseInventoryServiceImpl.java:75)
        //   See https://diff.blue/R013 to resolve this issue.

        WarehouseBlockedInventoryDto warehouseBlockedInventoryDto = mock(WarehouseBlockedInventoryDto.class);
        when(warehouseBlockedInventoryDto.getQuantity()).thenReturn(1);
        doNothing().when(warehouseBlockedInventoryDto).setFacility((String) any());
        doNothing().when(warehouseBlockedInventoryDto).setLegalOwner((String) any());
        doNothing().when(warehouseBlockedInventoryDto).setProductId((Long) any());
        doNothing().when(warehouseBlockedInventoryDto).setQuantity((Integer) any());
        doNothing().when(warehouseBlockedInventoryDto).setCreatedAt((Date) any());
        doNothing().when(warehouseBlockedInventoryDto).setCreatedBy((String) any());
        doNothing().when(warehouseBlockedInventoryDto).setId((Long) any());
        doNothing().when(warehouseBlockedInventoryDto).setUpdatedAt((Date) any());
        doNothing().when(warehouseBlockedInventoryDto).setUpdatedBy((String) any());
        doNothing().when(warehouseBlockedInventoryDto).setVersion((Long) any());
        LocalDateTime atStartOfDayResult = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseBlockedInventoryDto.setCreatedAt(Date.from(atStartOfDayResult.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseBlockedInventoryDto.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        warehouseBlockedInventoryDto.setFacility("Facility");
        warehouseBlockedInventoryDto.setId(123L);
        warehouseBlockedInventoryDto.setLegalOwner("Legal Owner");
        warehouseBlockedInventoryDto.setProductId(123L);
        warehouseBlockedInventoryDto.setQuantity(1);
        LocalDateTime atStartOfDayResult1 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseBlockedInventoryDto.setUpdatedAt(Date.from(atStartOfDayResult1.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseBlockedInventoryDto.setUpdatedBy("2020-03-01");
        warehouseBlockedInventoryDto.setVersion(1L);
        when(warehouseBlockedInventoryService.findByProductIdAndFacilityAndLegalOwner((Long) any(), (String) any(),
                (String) any())).thenReturn(warehouseBlockedInventoryDto);

        ArrayList<BaseEntity> baseEntityList = new ArrayList<>();
        baseEntityList.add(new StorefrontInventory());
        when(baseRepository.findAll((Specification<BaseEntity>) any())).thenReturn(baseEntityList);
        warehouseInventoryServiceImpl.getConsolidatedInvInfo("Facility Code", 123, "Legal Owner");
    }

    /**
     * Method under test: {@link WarehouseInventoryServiceImpl#getConsolidatedInvInfo(String, Integer, String)}
     */
    @Test
    @Disabled("TODO: Complete this test")
    void testGetConsolidatedInvInfo5() {
        // TODO: Complete this test.
        //   Reason: R013 No inputs found that don't throw a trivial exception.
        //   Diffblue Cover tried to run the arrange/act section, but the method under
        //   test threw
        //   java.lang.NullPointerException
        //       at java.util.stream.ReduceOps$1ReducingSink.accept(ReduceOps.java:80)
        //       at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
        //       at java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:175)
        //       at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
        //       at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
        //       at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
        //       at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
        //       at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        //       at java.util.stream.ReferencePipeline.reduce(ReferencePipeline.java:541)
        //       at com.lenskart.nexs.cid.util.ConsolidatedInvCountUtil.getTotalInventory(ConsolidatedInvCountUtil.java:63)
        //       at com.lenskart.nexs.cid.service.impl.WarehouseInventoryServiceImpl.getConsolidatedInvInfo(WarehouseInventoryServiceImpl.java:75)
        //   See https://diff.blue/R013 to resolve this issue.

        WarehouseBlockedInventoryDto warehouseBlockedInventoryDto = mock(WarehouseBlockedInventoryDto.class);
        when(warehouseBlockedInventoryDto.getQuantity()).thenReturn(1);
        doNothing().when(warehouseBlockedInventoryDto).setFacility((String) any());
        doNothing().when(warehouseBlockedInventoryDto).setLegalOwner((String) any());
        doNothing().when(warehouseBlockedInventoryDto).setProductId((Long) any());
        doNothing().when(warehouseBlockedInventoryDto).setQuantity((Integer) any());
        doNothing().when(warehouseBlockedInventoryDto).setCreatedAt((Date) any());
        doNothing().when(warehouseBlockedInventoryDto).setCreatedBy((String) any());
        doNothing().when(warehouseBlockedInventoryDto).setId((Long) any());
        doNothing().when(warehouseBlockedInventoryDto).setUpdatedAt((Date) any());
        doNothing().when(warehouseBlockedInventoryDto).setUpdatedBy((String) any());
        doNothing().when(warehouseBlockedInventoryDto).setVersion((Long) any());
        LocalDateTime atStartOfDayResult = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseBlockedInventoryDto.setCreatedAt(Date.from(atStartOfDayResult.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseBlockedInventoryDto.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        warehouseBlockedInventoryDto.setFacility("Facility");
        warehouseBlockedInventoryDto.setId(123L);
        warehouseBlockedInventoryDto.setLegalOwner("Legal Owner");
        warehouseBlockedInventoryDto.setProductId(123L);
        warehouseBlockedInventoryDto.setQuantity(1);
        LocalDateTime atStartOfDayResult1 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseBlockedInventoryDto.setUpdatedAt(Date.from(atStartOfDayResult1.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseBlockedInventoryDto.setUpdatedBy("2020-03-01");
        warehouseBlockedInventoryDto.setVersion(1L);
        when(warehouseBlockedInventoryService.findByProductIdAndFacilityAndLegalOwner((Long) any(), (String) any(),
                (String) any())).thenReturn(warehouseBlockedInventoryDto);

        ArrayList<BaseEntity> baseEntityList = new ArrayList<>();
        baseEntityList.add(new StorefrontInventory());
        baseEntityList.add(new StorefrontInventory());
        when(baseRepository.findAll((Specification<BaseEntity>) any())).thenReturn(baseEntityList);
        warehouseInventoryServiceImpl.getConsolidatedInvInfo("Facility Code", 123, "Legal Owner");
    }

    /**
     * Method under test: {@link WarehouseInventoryServiceImpl#getConsolidatedInvInfo(String, Integer, String)}
     */
    @Test
    @Disabled("TODO: Complete this test")
    void testGetConsolidatedInvInfo6() {
        // TODO: Complete this test.
        //   Reason: R013 No inputs found that don't throw a trivial exception.
        //   Diffblue Cover tried to run the arrange/act section, but the method under
        //   test threw
        //   java.lang.NullPointerException
        //       at com.lenskart.nexs.cid.service.impl.WarehouseInventoryServiceImpl.getConsolidatedInvInfo(WarehouseInventoryServiceImpl.java:73)
        //   See https://diff.blue/R013 to resolve this issue.

        WarehouseBlockedInventoryDto warehouseBlockedInventoryDto = mock(WarehouseBlockedInventoryDto.class);
        when(warehouseBlockedInventoryDto.getQuantity()).thenReturn(1);
        doNothing().when(warehouseBlockedInventoryDto).setFacility((String) any());
        doNothing().when(warehouseBlockedInventoryDto).setLegalOwner((String) any());
        doNothing().when(warehouseBlockedInventoryDto).setProductId((Long) any());
        doNothing().when(warehouseBlockedInventoryDto).setQuantity((Integer) any());
        doNothing().when(warehouseBlockedInventoryDto).setCreatedAt((Date) any());
        doNothing().when(warehouseBlockedInventoryDto).setCreatedBy((String) any());
        doNothing().when(warehouseBlockedInventoryDto).setId((Long) any());
        doNothing().when(warehouseBlockedInventoryDto).setUpdatedAt((Date) any());
        doNothing().when(warehouseBlockedInventoryDto).setUpdatedBy((String) any());
        doNothing().when(warehouseBlockedInventoryDto).setVersion((Long) any());
        LocalDateTime atStartOfDayResult = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseBlockedInventoryDto.setCreatedAt(Date.from(atStartOfDayResult.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseBlockedInventoryDto.setCreatedBy("Jan 1, 2020 8:00am GMT+0100");
        warehouseBlockedInventoryDto.setFacility("Facility");
        warehouseBlockedInventoryDto.setId(123L);
        warehouseBlockedInventoryDto.setLegalOwner("Legal Owner");
        warehouseBlockedInventoryDto.setProductId(123L);
        warehouseBlockedInventoryDto.setQuantity(1);
        LocalDateTime atStartOfDayResult1 = LocalDate.of(1970, 1, 1).atStartOfDay();
        warehouseBlockedInventoryDto.setUpdatedAt(Date.from(atStartOfDayResult1.atZone(ZoneId.of("UTC")).toInstant()));
        warehouseBlockedInventoryDto.setUpdatedBy("2020-03-01");
        warehouseBlockedInventoryDto.setVersion(1L);
        when(warehouseBlockedInventoryService.findByProductIdAndFacilityAndLegalOwner((Long) any(), (String) any(),
                (String) any())).thenReturn(warehouseBlockedInventoryDto);
        when(baseRepository.findAll((Specification<BaseEntity>) any())).thenReturn(new ArrayList<>());
        warehouseInventoryServiceImpl.getConsolidatedInvInfo("Facility Code", null, "Legal Owner");
    }
}

