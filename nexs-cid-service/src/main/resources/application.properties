spring.application.name=${APP_NAME:nexs-cid}
spring.config.import=consul:,vault://lenskart/nexs/nexs-config/${VAULT_DEFAULT_CONTEXT}
spring.cloud.consul.host=${CONSUL_SERVER:localhost}
spring.cloud.consul.port=${CONSUL_PORT:8500}
spring.cloud.consul.enabled=true
spring.cloud.consul.config.enabled=true
spring.cloud.consul.config.prefix=${CONFIG_PREFIX:nexsconfig}
spring.cloud.consul.config.name=${spring.application.name},${PROFILE:preprod-k8s}
spring.cloud.consul.config.format=PROPERTIES
spring.cloud.consul.config.data-key=data
spring.cloud.consul.config.default-context=common
spring.cloud.consul.config.profile-separator=,
spring.cloud.consul.config.acl-token=${ACL_TOKEN:}
spring.cloud.consul.config.watch.enabled=${WATCH_ENABLE:true}
spring.cloud.consul.config.discovery.catalog-services-watch-delay=${WATCH_DELAY:30000}
spring.cloud.consul.discovery.enabled=false
spring.cloud.consul.service-registry.enabled=false
spring.cloud.compatibility-verifier.enabled=false
spring.cloud.vault.enabled=${VAULT_ENABLED:true}
spring.cloud.vault.uri=${VAULT_URI:http://vault.infra.svc.cluster.local:8200}
spring.cloud.vault.authentication=${VAULT_AUTHENTICATION:KUBERNETES}
spring.cloud.vault.kubernetes.role=${VAULT_ROLE}
spring.cloud.vault.kubernetes.kubernetes-path=${VAULT_KUBERNETES-PATH:kubernetes}
spring.cloud.vault.kubernetes.service-account-token-file=${VAULT_SERVICE-ACCOUNT-TOKEN-FILE:/var/run/secrets/kubernetes.io/serviceaccount/token}
spring.cloud.vault.kv.enabled=true
spring.cloud.vault.kv.backend=${VAULT_BACKEND:lenskart/nexs/nexs-config}
spring.cloud.vault.kv.backend.profile-separator=/
spring.cloud.vault.kv.default-context=${VAULT_DEFAULT_CONTEXT}
spring.cloud.vault.kv.application-name=${VAULT_DEFAULT_CONTEXT}
