package com.lenskart.nexs.cid.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.lenskart.nexs.cid.util.ObjectHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Collections;

@Slf4j
@Component
public class CountryFacilityConfig {

    @Value("${country.facility.mapping}")
    private String countryFacilityJson;

    private static Map<String, List<String>> countryFacilityMap;

    @PostConstruct
    private void init() {
        try {
            log.info("countryFacilityJson value {}", countryFacilityJson);
            countryFacilityMap = ObjectHelper.getObjectMapper().readValue(countryFacilityJson, new TypeReference<HashMap<String, List<String>>>(){});
        } catch (JsonMappingException e) {
            throw new RuntimeException(e);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static Map<String, List<String>> getCountryFacilityMap() {
        return countryFacilityMap;
    }

    public static List<String> getFacilitiesForCountry(String countryCode) {
        return countryFacilityMap.getOrDefault(countryCode, Collections.emptyList());
    }
} 