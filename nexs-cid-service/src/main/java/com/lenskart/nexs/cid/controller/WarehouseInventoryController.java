package com.lenskart.nexs.cid.controller;

import com.lenskart.nexs.commons.dto.BaseResponseModel;
import com.lenskart.nexs.cid.constant.CommonConstants;
import com.lenskart.nexs.cid.constant.ControllerUrl;
import com.lenskart.nexs.cid.dto.WarehouseInventoryDto;
import com.lenskart.nexs.cid.entity.WarehouseInventory;
import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.cid.facade.InventoryUpdateFacade;
import com.lenskart.nexs.cid.request.UpdateCIDRequest;
import com.lenskart.nexs.commonlogger.annotations.RestLogging;
import com.lenskart.nexs.commons.controller.BaseController;
import com.lenskart.nexs.commons.response.CommonResponseBuilder;
import com.lenskart.nexs.commons.response.ResponseDTO;
import com.lenskart.nexs.constants.responseMessage.ResponseCodes;
import io.micrometer.core.annotation.Timed;
import lombok.Setter;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Timed
@RestController
@RequestMapping(ControllerUrl.BASE_URL)
public class WarehouseInventoryController extends BaseController<ResponseDTO, WarehouseInventoryDto, WarehouseInventory> {

    private static final String MESSAGE = "Inventory Change request recorded";

    @Setter(onMethod__ = {@Autowired})
    protected InventoryUpdateFacade inventoryUpdateFacade;

    @RestLogging
    @PostMapping(value = ControllerUrl.UPDATE_CID_INVENTORY,
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    public ResponseEntity<BaseResponseModel> updateInventory(@Valid @RequestBody UpdateCIDRequest updateCIDRequest) throws ApplicationException {
        if (MDC.get(CommonConstants.USER_ID) == null) {
            if (updateCIDRequest.getInventoryUpdateRequests().get(0).getUpdatedBy() != null) {
                MDC.put(CommonConstants.USER_ID, updateCIDRequest.getInventoryUpdateRequests().get(0).getUpdatedBy());
            } else {
                MDC.put(CommonConstants.USER_ID, CommonConstants.IMS_USER);
            }
        }
        inventoryUpdateFacade.updateInventory(updateCIDRequest);
        return CommonResponseBuilder.successResponse(null, MESSAGE, ResponseCodes.RESPONSE_SUCCESS);
    }
}
