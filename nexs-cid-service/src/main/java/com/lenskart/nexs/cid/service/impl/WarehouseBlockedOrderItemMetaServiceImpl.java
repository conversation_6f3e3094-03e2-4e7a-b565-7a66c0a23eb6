package com.lenskart.nexs.cid.service.impl;

import com.lenskart.nexs.cid.dto.WarehouseBlockedOrderItemMetaDto;
import com.lenskart.nexs.cid.entity.WarehouseBlockedOrderItemMeta;
import com.lenskart.nexs.cid.service.WarehouseBlockedOrderItemMetaService;
import com.lenskart.nexs.commons.service.impl.BaseServiceImp;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class WarehouseBlockedOrderItemMetaServiceImpl extends BaseServiceImp<WarehouseBlockedOrderItemMetaDto, WarehouseBlockedOrderItemMeta> implements WarehouseBlockedOrderItemMetaService {
    @Override
    public WarehouseBlockedOrderItemMetaDto findByOrderItemId(Long orderItemId) {
        String searchTerm = "warehouseBlockedOrderItemId.eq:" + orderItemId;
        return  findBySearchTerms(searchTerm);
    }

    @Override
    public List<WarehouseBlockedOrderItemMetaDto> findAllByOrderItemId(String orderItemIds) {
        String searchTerm = "warehouseBlockedOrderItemId.in:" + orderItemIds;
        return search(searchTerm);
    }
}
