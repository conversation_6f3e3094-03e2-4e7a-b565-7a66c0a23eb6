package com.lenskart.nexs.cid.filter;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import org.apache.logging.log4j.Logger;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.util.StringUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Set;

@Configuration
@Order(Ordered.LOWEST_PRECEDENCE)
public class BasicAuthorizationFilter implements Filter {

    private static final String X_CLIENT_KEY = "x-client-key";
    private static final String CLIENT = "client";

    @CustomLogger
    private Logger logger;

    @Value("#{'${service.allowed.clients}'.split(',')}")
    private Set<String> allowedClients;


    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
            throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) servletRequest;
        HttpServletResponse httpResponse = (HttpServletResponse) servletResponse;
        String client = httpRequest.getHeader(X_CLIENT_KEY);
        if (StringUtils.hasLength(client)) {
            MDC.put(CLIENT, client);
        }
        if (StringUtils.hasLength(client) && !allowedClients.contains(client)) {
            logger.error("invalid request {}", httpRequest.getRequestURI());
            httpResponse.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            return;
        }
        filterChain.doFilter(servletRequest, servletResponse);
    }
}
