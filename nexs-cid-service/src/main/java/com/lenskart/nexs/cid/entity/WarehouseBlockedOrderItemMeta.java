package com.lenskart.nexs.cid.entity;

import com.lenskart.nexs.cid.enums.FulfillableStatus;
import com.lenskart.nexs.commons.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;

@Getter
@Setter
@ToString(callSuper = true)
@Entity
@Table(name = "warehouse_blocked_order_item_meta")
public class WarehouseBlockedOrderItemMeta extends BaseEntity {

    @Column(name = "warehouse_blocked_order_item_id", nullable = false)
    private Long warehouseBlockedOrderItemId;

    @Enumerated(EnumType.STRING)
    @Column(name = "fulfillable_status", nullable = false, length = 20)
    private FulfillableStatus fulfillableStatus;

}
