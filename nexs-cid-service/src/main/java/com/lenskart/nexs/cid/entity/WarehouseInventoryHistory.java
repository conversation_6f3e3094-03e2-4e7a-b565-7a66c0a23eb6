package com.lenskart.nexs.cid.entity;

import com.lenskart.nexs.ims.enums.LocationType;
import com.lenskart.nexs.ims.model.Availability;
import com.lenskart.nexs.ims.model.Condition;
import com.lenskart.nexs.ims.model.Status;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

@Getter
@Setter
@ToString(callSuper = true)
@Entity
@IdClass(HistoryCompositeKey.class)
@Table(name = "warehouse_inventory_history")
public class WarehouseInventoryHistory {

    @Id
    @Column(name = "id", nullable = false)
    private Long id;

    @Id
    @Column(name = "rev", nullable = false)
    private Long rev;

    @Column(name = "rev_type")
    private boolean revType;

    @Column(name = "created_by", nullable = false, length = 100)
    private String createdBy;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    @Column(name = "updated_by", nullable = false, length = 100)
    private String updatedBy;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at", nullable = false)
    private Date updatedAt;

    @Column(name = "pid", nullable = false)
    private Long productId;

    @Column(name = "facility", nullable = false, length = 50)
    private String facility;

    @Column(name = "quantity", nullable = false, length = 50)
    private Integer quantity = 0;

    @Column(name = "blocked_quantity", length = 50)
    private Integer blockedQuantity = 0;

    @Column(name = "buffered_quantity", length = 50)
    private Integer bufferedQuantity = 0;

    @Enumerated(EnumType.STRING)
    @Column(name = "`condition`", nullable = false, length = 50)
    private Condition condition;

    @Enumerated(EnumType.STRING)
    @Column(name = "availability", nullable = false, length = 50)
    private Availability availability;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 50)
    private Status status;

    @Column(name = "supplier", length = 50)
    private String supplier;

    @Column(name = "legal_owner", length = 50)
    private String legalOwner;

    @Column(name = "master_pid", length = 50)
    private Integer masterPid;

    @Column(name = "enabled")
    private Boolean enabled = true;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "reconciliation_event_time")
    private Date reconciliationEventTime;
}
