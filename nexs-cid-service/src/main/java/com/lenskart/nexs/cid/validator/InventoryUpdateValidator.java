package com.lenskart.nexs.cid.validator;

import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.ims.enums.CidInventoryOperation;
import com.lenskart.nexs.ims.request.InventoryUpdateRequest;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class InventoryUpdateValidator {

    public void validate(InventoryUpdateRequest request) throws ApplicationException {

        if (Objects.isNull(request)) {
            throw new ApplicationException("InventoryUpdateRequest cannot be null");
        }

        if (request.getProductId() == null) throw new ApplicationException("product id cannot be null");
        if (request.getFacility() == null) throw new ApplicationException("facility cannot be null");
        if (request.getSupplier() == null) throw new ApplicationException("supplier cannot be null");
        if (request.getUpdatedBy() == null) throw new ApplicationException("updatedBy cannot be null");

        if (Objects.isNull(request.getCidInventoryOperation()) || CidInventoryOperation.ABSOLUTE.equals(request.getCidInventoryOperation())) {
            if (request.getCondition() == null) throw new ApplicationException("condition cannot be null");
            if (request.getAvailability() == null) throw new ApplicationException("availability cannot be null");
            if (request.getStatus() == null) throw new ApplicationException("status cannot be null");
            if (request.getQuantity() == null) throw new ApplicationException("quantity cannot be null");
        } else if (CidInventoryOperation.INV_BLOCK.equals(request.getCidInventoryOperation())) {
            if (request.getEventTime() == null) throw new ApplicationException("event time cannot be null");
        } else {
            if (request.getCondition() == null) throw new ApplicationException("condition cannot be null");
            if (request.getAvailability() == null) throw new ApplicationException("availability cannot be null");
            if (request.getStatus() == null) throw new ApplicationException("status cannot be null");
            if (request.getEventTime() == null) throw new ApplicationException("event time cannot be null");
        }

    }
}
