package com.lenskart.nexs.cid.service.impl;

import com.lenskart.nexs.cid.dto.StorefrontInventoryDto;
import com.lenskart.nexs.cid.entity.StorefrontInventory;
import com.lenskart.nexs.cid.repository.StorefrontInventoryRepository;
import com.lenskart.nexs.cid.service.StorefrontInventoryService;
import com.lenskart.nexs.commons.service.impl.BaseServiceImp;
import org.springframework.stereotype.Service;

@Service
public class StorefrontInventoryServiceImpl extends BaseServiceImp<StorefrontInventoryDto, StorefrontInventory> implements StorefrontInventoryService {


    @Override
    public StorefrontInventoryDto findByProductIdAndLegalOwner(Long pid, String legalOwner) {
        return convertToDto(((StorefrontInventoryRepository)repository).findByProductIdAndLegalOwner(pid,legalOwner),new StorefrontInventoryDto());
    }

}
