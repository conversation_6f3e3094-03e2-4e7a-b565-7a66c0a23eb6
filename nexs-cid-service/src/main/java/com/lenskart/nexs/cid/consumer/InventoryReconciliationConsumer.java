package com.lenskart.nexs.cid.consumer;

import com.lenskart.nexs.cid.constant.KafkaConstants;
import com.lenskart.nexs.cid.constant.MetricConstants;
import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.cid.facade.InventoryReconcileFacade;
import com.lenskart.nexs.cid.request.InventoryReconciliationRequest;
import com.lenskart.nexs.cid.util.ObjectHelper;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.newrelic.api.agent.Trace;
import io.micrometer.core.annotation.Timed;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.util.Objects;

@Component
@ConditionalOnProperty(name = "nexs.cid.consumer.enabled", havingValue = "true")
public class InventoryReconciliationConsumer {
    private final Counter errorCounter  = Metrics.counter(MetricConstants.INVENTORY_RECONCILIATION_CONSUMER);

    @CustomLogger
    private Logger logger;

    @Setter(onMethod__ = {@Autowired})
    private InventoryReconcileFacade inventoryReconcileFacade;

    @Timed
    @Trace(dispatcher = true)
    @Transactional(rollbackFor = Exception.class)
    @KafkaListener(
            topics = KafkaConstants.INVENTORY_RECONCILIATION_TOPIC,
            groupId = KafkaConstants.NEXS_CID_KAFKA_GROUP_ID,
            containerFactory = "scmKafkaListenerContainerFactory"
    )
    public void listen(@Payload String message) throws ApplicationException {
        logger.info("[InventoryReconciliationConsumer] started processing unicom inventory update request with payload: {}", message);
        try {
            InventoryReconciliationRequest inventoryReconciliationRequest = ObjectHelper.readNonNullValue(message, InventoryReconciliationRequest.class);
            logger.info("[InventoryReconciliationConsumer] request payload after parsing {}", inventoryReconciliationRequest);
            if (Objects.isNull(inventoryReconciliationRequest)) {
                throw new ApplicationException("invalid event payload");
            }

            inventoryReconcileFacade.alertAndReconcileInventory(inventoryReconciliationRequest);
        } catch (Exception e) {
            errorCounter.increment();
            String errorMessage = MessageFormat.format("error while processing inventory update for {0}", message);
            logger.error(errorMessage, e);
            throw new ApplicationException(errorMessage, e);
        }
    }
}
