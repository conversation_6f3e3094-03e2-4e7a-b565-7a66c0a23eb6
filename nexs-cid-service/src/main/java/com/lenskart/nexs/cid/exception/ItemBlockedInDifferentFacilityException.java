package com.lenskart.nexs.cid.exception;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class ItemBlockedInDifferentFacilityException extends RuntimeException {

    private final String errorMessage;
    private final String errorCode;
    private Throwable cause;
    private String blockedFacility;

    public ItemBlockedInDifferentFacilityException(String errorCode, String errorMessage) {
        super(errorMessage);
        this.errorMessage = errorMessage;
        this.errorCode = errorCode;
    }

    public ItemBlockedInDifferentFacilityException(String errorCode, String errorMessage, String blockedFacility) {
        super(errorMessage);
        this.errorMessage = errorMessage;
        this.errorCode = errorCode;
        this.blockedFacility = blockedFacility;
    }

    public ItemBlockedInDifferentFacilityException(String errorCode, String errorMessage, Throwable cause) {
        super(errorMessage, cause);
        this.errorMessage = errorMessage;
        this.errorCode = errorCode;
        this.cause = cause;
    }
}
