package com.lenskart.nexs.cid.consumer;

import com.lenskart.nexs.cid.connector.ScmServiceConnector;
import com.lenskart.nexs.cid.constant.KafkaConstants;
import com.lenskart.nexs.cid.constant.MetricConstants;
import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.cid.request.updateinventory.ScmInventoryUpdateRequest;
import com.lenskart.nexs.cid.util.ObjectHelper;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.newrelic.util.NewRelicUtil;
import com.newrelic.api.agent.Trace;
import io.micrometer.core.annotation.Timed;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.util.Objects;


@Component
@ConditionalOnProperty(name = "nexs.cid.consumer.enabled", havingValue = "true")
public class ScmInventoryUpdateConsumer {

    private final Counter errorCounter  = Metrics.counter(MetricConstants.SCM_INVENTORY_UPDATE_CONSUMER, "result", "failure");

    @CustomLogger
    private Logger logger;
    @Setter(onMethod__ = {@Autowired})
    private ScmServiceConnector scmServiceConnector;

    @Timed
    @Trace(dispatcher = true)
    @Transactional(rollbackFor = Exception.class)
    @KafkaListener(
            topics = KafkaConstants.NEXS_CID_SCM_INVENTORY_SYNC_TOPIC,
            groupId = KafkaConstants.NEXS_CID_KAFKA_GROUP_ID,
            containerFactory = "kafkaListenerContainerFactory"
    )
    public void listen(@Payload String payload) throws ApplicationException {
        logger.info("started processing scm inventory update request for payload: {}", payload);
        ScmInventoryUpdateRequest scmInventoryUpdateRequest = null;
        try {
            scmInventoryUpdateRequest = ObjectHelper.readValue(payload, ScmInventoryUpdateRequest.class);
            if (Objects.isNull(scmInventoryUpdateRequest)) {
                throw new ApplicationException("invalid event payload");
            }
            NewRelicUtil.publishRecord(MetricConstants.CID_SCM_INVENTORY_UPDATE_CONSUMER_EVENT, scmInventoryUpdateRequest);
            scmServiceConnector.updateInventoryToScm(scmInventoryUpdateRequest);
        } catch (Exception e) {
            errorCounter.increment();
            String errorMessage = MessageFormat.format("error while processing scm inventory update for {0}", payload);
            logger.error(errorMessage, e);
            NewRelicUtil.publishRecord(MetricConstants.CID_SCM_INVENTORY_UPDATE_CONSUMER_FAILURE_EVENT, scmInventoryUpdateRequest);
            throw new ApplicationException(errorMessage, e);
        }
    }
}
