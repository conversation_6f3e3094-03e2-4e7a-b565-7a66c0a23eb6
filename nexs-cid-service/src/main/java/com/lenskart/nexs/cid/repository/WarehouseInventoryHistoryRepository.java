package com.lenskart.nexs.cid.repository;

import com.lenskart.nexs.cid.entity.WarehouseInventoryHistory;
import com.lenskart.nexs.ims.model.Availability;
import com.lenskart.nexs.ims.model.Condition;
import com.lenskart.nexs.ims.model.Status;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface WarehouseInventoryHistoryRepository extends JpaRepository<WarehouseInventoryHistory, Long> {

    List<WarehouseInventoryHistory> findByProductIdAndConditionAndStatusAndAvailabilityAndFacilityInAndUpdatedAtLessThanEqualOrderByUpdatedAtDesc(Long productId, Condition condition, Status status,
                                                                                                                                               Availability availability, List<String> facility, Date updatedAt);

}
