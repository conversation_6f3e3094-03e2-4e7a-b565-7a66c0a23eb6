package com.lenskart.nexs.cid.producer;

import com.lenskart.nexs.cid.constant.KafkaConstants;
import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.cid.util.ObjectHelper;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.ems.enums.ExceptionType;
import com.lenskart.nexs.ems.model.EmsExceptionEvent;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.UUID;

@Component
public class EMSInventoryEventProducer {

    @CustomLogger
    private Logger logger;
    @Setter(onMethod__ = {@Autowired})
    private KafkaTemplate<String, String> kafkaTemplate;
    @Value("${is.uff.batchProcessing.allowed}")
    @Setter(AccessLevel.NONE)
    private boolean isUffBatchProcessingAllowed;
    @Value("${ems.kafka.uff.event.topic}")
    @Setter(AccessLevel.NONE)
    private String emsEventTopic;
    @Value("${ems.kafka.uff.batch.event.topic}")
    @Setter(AccessLevel.NONE)
    private String emsEventBatchTopic;


    public void sendMessage(EmsExceptionEvent emsEventPayload, Long productId) throws ApplicationException {
        logger.info("publishing inventory event to EMS event {}", emsEventPayload);
        String topic = emsEventTopic;
//        TODO:- change this configuration
        if(isUffBatchProcessingAllowed && emsEventPayload.getExceptionType().equals(ExceptionType.CID_FULFILLABILITY_CHANGE)) {
            emsEventPayload.setExceptionType(ExceptionType.CID_BATCH_FULFILLABILITY_CHANGE);
            topic = emsEventBatchTopic;
        }
        try {
            ProducerRecord<String, String> producerRecord = new ProducerRecord<>(
                    topic,
                    String.valueOf(productId),
                    ObjectHelper.writeValue(emsEventPayload)
            );
            String messageIdempotencyKey = "";
            if(ExceptionType.CID_UFF_PID.equals(emsEventPayload.getExceptionType()))
                messageIdempotencyKey +="ems_inv_update_";
            else if (ExceptionType.CID_FULFILLABILITY_CHANGE.equals(emsEventPayload.getExceptionType()))
                messageIdempotencyKey += "ems_fulfullability_update_";
            else if (ExceptionType.CID_BATCH_FULFILLABILITY_CHANGE.equals(emsEventPayload.getExceptionType()))
                messageIdempotencyKey += "ems_batch_fulfullability_update_";

            messageIdempotencyKey +=  UUID.randomUUID();
            producerRecord.headers()
                    .add(KafkaConstants.MESSAGE_IDEMPOTENCY_KEY, messageIdempotencyKey.getBytes());
            kafkaTemplate.send(producerRecord)
                    .get();
        } catch (Exception exception) {
            String message = MessageFormat.format("error while publishing message to topic: {0} , payload: {1} Error: {2}",
                    topic, emsEventPayload, exception.getMessage());
            logger.error(message, exception);
            throw new ApplicationException(message, exception);
        }
    }
}


