package com.lenskart.nexs.cid.facade;

import com.fasterxml.jackson.core.type.TypeReference;
import com.lenskart.nexs.cid.constant.MetricConstants;
import com.lenskart.nexs.cid.entity.WarehouseBlockedInventoryHistory;
import com.lenskart.nexs.cid.entity.WarehouseInventoryHistory;
import com.lenskart.nexs.cid.repository.WarehouseBlockedInventoryHistoryRepository;
import com.lenskart.nexs.cid.repository.WarehouseInventoryHistoryRepository;
import com.lenskart.nexs.cid.request.InventoryReconciliationRequest;
import com.lenskart.nexs.cid.request.UffRedisPayload;
import com.lenskart.nexs.cid.util.ObjectHelper;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.constants.RedisOps;
import com.lenskart.nexs.ims.model.Availability;
import com.lenskart.nexs.ims.model.Condition;
import com.lenskart.nexs.ims.model.Status;
import com.lenskart.nexs.service.RedisHandler;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.lenskart.nexs.cid.constant.CommonConstants.UFF_NEGATIVE_INVENTORY_FOR_NEXS_AND_UNICOM_CACHE_KEY;
import static com.lenskart.nexs.cid.constant.CommonConstants.UFF_POSITIVE_INVENTORY_FOR_UNICOM_CACHE_KEY;

@Component
public class InventoryReconcileFacade {

    @CustomLogger
    private Logger logger;

    @Setter(onMethod__ = {@Autowired})
    private WarehouseInventoryHistoryRepository warehouseInventoryHistoryRepository;

    @Setter(onMethod__ = {@Autowired})
    private WarehouseBlockedInventoryHistoryRepository warehouseBlockedInventoryHistoryRepository;

    @Value("${nexs.cid.uff.alerts.redis.ttl}")
    private String redisTtl;

    @Setter(onMethod__ = {@Value("${nexs.cid.default.unicom.facility}")})
    private String defaultUnicomFacility;

    @Value("#{'${nexs.available.facilities}'.split(',')}")
    private List<String> nexsFacilities;

    private final Counter uffInventoryAvailableInUnicomCounter = Metrics.counter(MetricConstants.UFF_INVENTORY_AVAILABLE_IN_UNICOM_COUNTER);
    private final Counter uffInventoryNotAvailableInNexsAndUnicomCounter = Metrics.counter(MetricConstants.UFF_INVENTORY_NOT_AVAILABLE_IN_NEXS_AND_UNICOM_COUNTER);

    public void alertAndReconcileInventory(InventoryReconciliationRequest inventoryReconciliationRequest) throws Exception {
        List<WarehouseInventoryHistory> warehouseInventoryHistoryList =
                warehouseInventoryHistoryRepository.findByProductIdAndConditionAndStatusAndAvailabilityAndFacilityInAndUpdatedAtLessThanEqualOrderByUpdatedAtDesc(
                        Long.valueOf(inventoryReconciliationRequest.getProductId()), Condition.GOOD, Status.AVAILABLE,
                        Availability.AVAILABLE, Collections.singletonList(defaultUnicomFacility), inventoryReconciliationRequest.getUnFulfillableTime()
                );

        WarehouseInventoryHistory warehouseInventoryHistory = null;
        if (!CollectionUtils.isEmpty(warehouseInventoryHistoryList)) {
            warehouseInventoryHistory = warehouseInventoryHistoryList.get(0);
        }
        logger.info("[InventoryReconciliationConsumer -> alertAndReconcileInventory] response from warehouseInventoryHistory table for PID {}, facilities {}, unFulfillableTime {} and unFulfillableCount {} is {}",
                inventoryReconciliationRequest.getProductId(), defaultUnicomFacility, inventoryReconciliationRequest.getUnFulfillableTime(), inventoryReconciliationRequest.getUnFulfillableCount(), warehouseInventoryHistory);

        if (Objects.nonNull(warehouseInventoryHistory)
                && warehouseInventoryHistory.getQuantity() > inventoryReconciliationRequest.getUnFulfillableCount()
        ) {
            logger.info("[InventoryReconciliationConsumer -> alertAndReconcileInventory] Inventory is available in CID for PID {} and facility {} but marked unfulfillable in UNICOM", warehouseInventoryHistory.getProductId(), warehouseInventoryHistory.getFacility());
            uffInventoryAvailableInUnicomCounter.increment();
            updateRedisCacheForEmailAlerts(inventoryReconciliationRequest, UFF_POSITIVE_INVENTORY_FOR_UNICOM_CACHE_KEY);
        } else if (Objects.isNull(warehouseInventoryHistory) || warehouseInventoryHistory.getQuantity() <= 0) {
            checkAndAlertUffInNexsAndUnicom(inventoryReconciliationRequest, warehouseInventoryHistory);
        }
    }

    private void checkAndAlertUffInNexsAndUnicom(InventoryReconciliationRequest inventoryReconciliationRequest, WarehouseInventoryHistory warehouseInventoryHistory) throws Exception {
        List<WarehouseInventoryHistory> warehouseInventoryHistoryForNexsFacilityList =
                warehouseInventoryHistoryRepository.findByProductIdAndConditionAndStatusAndAvailabilityAndFacilityInAndUpdatedAtLessThanEqualOrderByUpdatedAtDesc(
                        Long.valueOf(inventoryReconciliationRequest.getProductId()), Condition.GOOD, Status.AVAILABLE,
                        Availability.AVAILABLE, nexsFacilities, inventoryReconciliationRequest.getUnFulfillableTime()
                );

        WarehouseInventoryHistory warehouseInventoryHistoryForNexsFacility = null;
        if (!CollectionUtils.isEmpty(warehouseInventoryHistoryForNexsFacilityList)) {
            warehouseInventoryHistoryForNexsFacility = warehouseInventoryHistoryForNexsFacilityList.get(0);
        }
        logger.info("[InventoryReconciliationConsumer -> alertAndReconcileInventory] response from warehouseInventoryForNexsFacility table for PID {}, facilities {}, unFulfillableTime {} and unFulfillableCount {} is {}",
                inventoryReconciliationRequest.getProductId(), nexsFacilities, inventoryReconciliationRequest.getUnFulfillableTime(), inventoryReconciliationRequest.getUnFulfillableCount(), warehouseInventoryHistoryForNexsFacility);

        if (Objects.nonNull(warehouseInventoryHistoryForNexsFacility)) {
            List<WarehouseBlockedInventoryHistory> warehouseBlockedInventoryHistoryList =
                    warehouseBlockedInventoryHistoryRepository.findByProductIdAndFacilityInAndUpdatedAtLessThanEqualOrderByUpdatedAtDesc(Long.valueOf(inventoryReconciliationRequest.getProductId()), nexsFacilities, inventoryReconciliationRequest.getUnFulfillableTime());

            WarehouseBlockedInventoryHistory warehouseBlockedInventoryHistory = null;
            if (!CollectionUtils.isEmpty(warehouseBlockedInventoryHistoryList)) {
                warehouseBlockedInventoryHistory = warehouseBlockedInventoryHistoryList.get(0);
            }
            logger.info("[InventoryReconciliationConsumer -> alertAndReconcileInventory] response from warehouseBlockedInventoryForNexsFacility table for PID {}, facilities {}, unFulfillableTime {} is {}",
                    inventoryReconciliationRequest.getProductId(), nexsFacilities, inventoryReconciliationRequest.getUnFulfillableTime(), warehouseBlockedInventoryHistory);

            int nexsBlockedCount = Objects.isNull(warehouseBlockedInventoryHistory) ? 0 : warehouseBlockedInventoryHistory.getQuantity();
            if (warehouseInventoryHistoryForNexsFacility.getQuantity() == 0
                    || warehouseInventoryHistoryForNexsFacility.getQuantity() - nexsBlockedCount <= inventoryReconciliationRequest.getUnFulfillableCount()
            ) {
                logger.info("[InventoryReconciliationConsumer -> alertAndReconcileInventory] Inventory is not available in CID for PID {} and facilities NXS1, {} and order marked unfulfillable in UNICOM", warehouseInventoryHistory.getProductId(), warehouseInventoryHistory.getFacility());
                uffInventoryNotAvailableInNexsAndUnicomCounter.increment();
                updateRedisCacheForEmailAlerts(inventoryReconciliationRequest, UFF_NEGATIVE_INVENTORY_FOR_NEXS_AND_UNICOM_CACHE_KEY);
            }
        }
    }

    private void updateRedisCacheForEmailAlerts(InventoryReconciliationRequest inventoryReconciliationRequest, String redisKey) throws Exception {
        List<UffRedisPayload> uffRedisPayloadList = getUffPidListFromRedis(redisKey);
        UffRedisPayload uffRedisPayload = new UffRedisPayload();

        uffRedisPayload.setProductId(Long.valueOf(inventoryReconciliationRequest.getProductId()));
        uffRedisPayload.setIncrementId(inventoryReconciliationRequest.getIncrementId());
        uffRedisPayloadList.add(uffRedisPayload);

        RedisHandler.redisOps(RedisOps.SETVALUETTL, redisKey, ObjectHelper.convertToString(uffRedisPayloadList), Long.valueOf(redisTtl), TimeUnit.HOURS);
    }

    public List<UffRedisPayload> getUffPidListFromRedis(String redisKey) throws Exception {
        List<UffRedisPayload> uffRedisPayloadList = new ArrayList<>();
        Object uffPidSetFromRedis = RedisHandler.redisOps(RedisOps.GET, redisKey);
        if (Objects.nonNull(uffPidSetFromRedis)) {
            logger.info("[InventoryReconciliationConsumer -> updateRedisCacheForEmailAlerts] UFF PIDs list found in cache for key {} is {}", redisKey, uffPidSetFromRedis);
            uffRedisPayloadList.addAll(ObjectHelper.getObjectMapper().readValue(String.valueOf(uffPidSetFromRedis), new TypeReference<List<UffRedisPayload>>() {}));
        } else {
            logger.info("[InventoryReconciliationConsumer -> updateRedisCacheForEmailAlerts] UFF PIDs list not found in cache for key {}", redisKey);
        }
        return uffRedisPayloadList;
    }
}
