package com.lenskart.nexs.cid.service.impl;

import com.lenskart.nexs.cid.dto.WarehouseBlockedInventoryDto;
import com.lenskart.nexs.cid.dto.WarehouseInventoryDto;
import com.lenskart.nexs.cid.entity.WarehouseInventory;
import com.lenskart.nexs.cid.enums.Supplier;
import com.lenskart.nexs.cid.repository.WarehouseInventoryRepository;
import com.lenskart.nexs.cid.service.WarehouseBlockedInventoryService;
import com.lenskart.nexs.cid.service.WarehouseInventoryService;
import com.lenskart.nexs.cid.util.ConsolidatedInvCountUtil;
import com.lenskart.nexs.cid.util.LegalOwnerUtil;
import com.lenskart.nexs.cid.config.CountryFacilityConfig;
import com.lenskart.nexs.cid.response.AvailableInventoryResponse;
import com.lenskart.nexs.commons.service.impl.BaseServiceImp;
import com.lenskart.nexs.ims.enums.LocationType;
import com.lenskart.nexs.ims.model.Availability;
import com.lenskart.nexs.ims.model.Condition;
import com.lenskart.nexs.ims.model.Status;
import com.lenskart.nexs.ims.response.ConsolidatedInvInfo;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.ArrayList;
import java.util.Collections;

@Service
@Setter(onMethod__ = {@Autowired})
public class WarehouseInventoryServiceImpl extends BaseServiceImp<WarehouseInventoryDto, WarehouseInventory> implements WarehouseInventoryService {

    private WarehouseBlockedInventoryService warehouseBlockedInventoryService;

    @Value("${legalOwner.defaultLegalOnwner}")
    @Setter(AccessLevel.NONE)
    private String defaultLegalOwner;

    @Value("${manesar.facility:NXS2}")
    @Setter(AccessLevel.NONE)
    private String manesarFacility;

    @Value("${facility.defaultHubCode:LKH03}")
    @Setter(AccessLevel.NONE)
    private String defaultHubCode;

    @Override
    public Page<WarehouseInventoryDto> getWarehouseInventory(Long productId, Map<String, String> params, int page, int size, String sortBy, String sortOrder, String legalOwner) {
        PageRequest pageRequest = PageRequest.of(page, size, Sort.by(Sort.Direction.valueOf(sortOrder), sortBy));
        WarehouseInventoryDto warehouseInventoryDto = new WarehouseInventoryDto();
        if (params.containsKey("facility") && !StringUtils.isEmpty(params.get("facility"))) {
            warehouseInventoryDto.setFacility(params.get("facility"));
        }
        if (params.containsKey("condition") && !StringUtils.isEmpty(params.get("condition"))) {
            warehouseInventoryDto.setCondition(Condition.valueOf(params.get("condition")));
        }
        if (params.containsKey("availability") && !StringUtils.isEmpty(params.get("availability"))) {
            warehouseInventoryDto.setAvailability(Availability.valueOf(params.get("availability")));
        }
        if (params.containsKey("status") && !StringUtils.isEmpty(params.get("status"))) {
            warehouseInventoryDto.setStatus(Status.valueOf(params.get("status")));
        }
        Page<WarehouseInventory> pageWarehouseInventory = ((WarehouseInventoryRepository) repository).getWarehouseInventory(
                productId, warehouseInventoryDto.getCondition(),
                warehouseInventoryDto.getStatus(), warehouseInventoryDto.getAvailability(),
                warehouseInventoryDto.getFacility(), pageRequest,
                legalOwner
        );
        return pageWarehouseInventory.map(entity -> convertToDto(entity, new WarehouseInventoryDto()));
    }

    @Override
    public ConsolidatedInvInfo getConsolidatedInvInfo(String facilityCode, Integer productId, String legalOwner) {
        ConsolidatedInvInfo consolidatedInvInfo =  new ConsolidatedInvInfo();
        if (LegalOwnerUtil.getFacilityLegalOwnerMap().containsKey(facilityCode)) {
            legalOwner = LegalOwnerUtil.getFacilityLegalOwnerMap().get(facilityCode);
        } else {
            legalOwner = defaultLegalOwner;
        }
        String searchTerms = "productId.eq:" + productId + "___facility.eq:" + facilityCode + "___legalOwner.eq:" + legalOwner;
        List<WarehouseInventoryDto> warehouseInventoryDtos = search(searchTerms);
        WarehouseBlockedInventoryDto warehouseBlockedInventoryDto = warehouseBlockedInventoryService.findByProductIdAndFacilityAndLegalOwner(productId.longValue(), facilityCode, legalOwner);

        consolidatedInvInfo.setTotalInventory(ConsolidatedInvCountUtil.getTotalInventory(warehouseInventoryDtos));
        consolidatedInvInfo.setGrnInventory(ConsolidatedInvCountUtil.getGrnCount(warehouseInventoryDtos));
        consolidatedInvInfo.setAllocatedInventory(ConsolidatedInvCountUtil.getAllocatedInv(warehouseInventoryDtos));
        consolidatedInvInfo.setPendingPutAwayInventory(ConsolidatedInvCountUtil.getPutAwayPendingCount(warehouseInventoryDtos));
        consolidatedInvInfo.setReservedInventory(ConsolidatedInvCountUtil.getReservedAvlWhInv(warehouseInventoryDtos));
        consolidatedInvInfo.setVirtualInventory(ConsolidatedInvCountUtil.getVirtualWhInv(warehouseInventoryDtos));

        long totalAvlWhInv = ConsolidatedInvCountUtil.getTotalAvlWhInv(warehouseInventoryDtos);
        long blockedInvCount = 0L;
        if (warehouseBlockedInventoryDto != null && warehouseBlockedInventoryDto.getQuantity() != null) {
             blockedInvCount = warehouseBlockedInventoryDto.getQuantity().longValue();
        }
        consolidatedInvInfo.setAvailableInventory(totalAvlWhInv - blockedInvCount);
        consolidatedInvInfo.setBlockedInventory(blockedInvCount);
        logger.debug("consolidated inventory info for facilityCode: {} and pid: {} consolidatedInvInfo: {}",
                facilityCode, productId, consolidatedInvInfo);
        return consolidatedInvInfo;
    }


    @Override
    public WarehouseInventoryDto findByProductIdFacilityConditionAvailabilityAndStatusAndLegalOwnerAndLocationType(Long productId, String facility, Condition condition, Availability availability, Status status, String legalOwner, LocationType locationType) {
        String searchTerms = "productId.eq:" + productId
                + "___facility.eq:" + facility
                + "___condition.eq:" + condition
                + "___availability.eq:" + availability
                + "___status.eq:" + status
                + "___legalOwner.eq:" + legalOwner
                + "___locationType.eq:" + locationType;
        logger.info("searchTerms : {}", searchTerms);
        return findBySearchTerms(searchTerms);
    }

    @Override
    public int getBufferedQuantity(Integer productId, String hubCode) {
        logger.info("[getBufferedQuantity] fetch buffered quantity for productId: {} and hubCode: {}", productId, hubCode);
        StringBuilder searchTerms = new StringBuilder("productId.eq:" + productId);
        searchTerms.append("___condition.eq:").append(Condition.GOOD);
        searchTerms.append("___availability.eq:").append(Availability.AVAILABLE);
        searchTerms.append("___status.eq:").append(Status.AVAILABLE);
        searchTerms.append("___locationType.eq:").append(LocationType.DEFAULT);
        if (hubCode.equalsIgnoreCase(defaultHubCode)) {
            searchTerms.append("___facility.eq:").append(manesarFacility);
        } else {
            searchTerms.append("___facility.eq:").append(hubCode);
        }
        logger.info("[getBufferedQuantity] searchTerms : {}", searchTerms);
        List<WarehouseInventoryDto> warehouseInventoryDtos = search(searchTerms.toString());
        if (CollectionUtils.isEmpty(warehouseInventoryDtos)) {
            logger.info("[getBufferedQuantity] warehouseInventoryDtos is empty for productId {} and hubCode {}", productId, hubCode);
            return 0;
        }
        return warehouseInventoryDtos.stream()
                .mapToInt(WarehouseInventoryDto::getBufferedQuantity)
                .sum();
    }

    @Override
    public List<AvailableInventoryResponse> getAvailableInventoryByPidAndCountry(Long productId, String countryCode, boolean considerBuffer) {
        logger.info("[getAvailableInventoryByPidAndCountry] Starting to fetch available inventory for productId: {} and countryCode: {} with considerBuffer: {}", productId, countryCode, considerBuffer);
        
        List<String> facilities = getFacilitiesForCountry(countryCode);
        if (CollectionUtils.isEmpty(facilities)) {
            logger.info("[getAvailableInventoryByPidAndCountry] No facilities found for country {} , returning empty list", countryCode);
            return Collections.emptyList();
        }

        List<AvailableInventoryResponse> availableInventoryResponses = new ArrayList<>();
        
        for (String facility : facilities) {
            logger.info("[getAvailableInventoryByPidAndCountry] Processing facility: {}", facility);
            String legalOwner = getLegalOwnerForFacility(facility);
            int availableQuantity = calculateAvailableQuantityForFacility(productId, facility, legalOwner, considerBuffer);
            
            AvailableInventoryResponse response = createAvailableInventoryResponse(productId, facility, availableQuantity);
            availableInventoryResponses.add(response);
            logger.info("[getAvailableInventoryByPidAndCountry] Added response for facility: {} with quantity: {}", facility, availableQuantity);
        }

        return availableInventoryResponses;
    }

    /**
     * Get facilities for the given country code
     * @param countryCode Country code
     * @return List of facilities for the country
     */
    private List<String> getFacilitiesForCountry(String countryCode) {
        logger.info("[getFacilitiesForCountry] Getting facilities for countryCode: {}", countryCode);
        List<String> facilities = CountryFacilityConfig.getFacilitiesForCountry(countryCode);
        if (CollectionUtils.isEmpty(facilities)) {
            logger.info("[getFacilitiesForCountry] No facilities found for countryCode: {}", countryCode);
        } else {
            logger.info("[getFacilitiesForCountry] Found {} facilities for countryCode: {}", facilities.size(), countryCode);
        }
        return facilities;
    }

    /**
     * Get legal owner for the given facility
     * @param facility Facility code
     * @return Legal owner for the facility
     */
    private String getLegalOwnerForFacility(String facility) {
        String legalOwner = defaultLegalOwner;
        if (LegalOwnerUtil.getFacilityLegalOwnerMap().containsKey(facility)) {
            legalOwner = LegalOwnerUtil.getFacilityLegalOwnerMap().get(facility);
        }
        return legalOwner;
    }

    /**
     * Calculate available quantity for a specific facility
     * @param productId Product ID
     * @param facility Facility code
     * @param legalOwner Legal owner
     * @param considerBuffer Whether to consider buffer in calculation
     * @return Available quantity
     */
    private int calculateAvailableQuantityForFacility(Long productId, String facility, String legalOwner, boolean considerBuffer) {
        logger.info("[calculateAvailableQuantityForFacility] Calculating available quantity for productId: {}, facility: {}, legalOwner: {}, considerBuffer: {}", 
            productId, facility, legalOwner, considerBuffer);
        
        // Search for warehouse inventory with specific criteria
        String searchTerms = "productId.eq:" + productId 
                + "___facility.eq:" + facility 
                + "___condition.eq:" + Condition.GOOD 
                + "___availability.eq:" + Availability.AVAILABLE 
                + "___status.eq:" + Status.AVAILABLE 
                + "___locationType.eq:" + LocationType.DEFAULT 
                + "___legalOwner.eq:" + legalOwner;

        List<WarehouseInventoryDto> warehouseInventoryDtos = search(searchTerms);

        int totalAvailableQuantity = 0;
        int bufferedQuantity = 0;
        
        if (!CollectionUtils.isEmpty(warehouseInventoryDtos)) {
            WarehouseInventoryDto warehouseInventoryDto = warehouseInventoryDtos.get(0);
            totalAvailableQuantity = warehouseInventoryDto.getQuantity() != null ? warehouseInventoryDto.getQuantity() : 0;
            bufferedQuantity = warehouseInventoryDto.getBufferedQuantity() != null ? warehouseInventoryDto.getBufferedQuantity() : 0;
            logger.info("[calculateAvailableQuantityForFacility] Found warehouse inventory - totalAvailableQuantity: {}, bufferedQuantity: {}", 
                totalAvailableQuantity, bufferedQuantity);
        }
        
        // Get blocked inventory for this facility
        WarehouseBlockedInventoryDto warehouseBlockedInventoryDto = warehouseBlockedInventoryService
                .findByProductIdAndFacilityAndLegalOwner(productId, facility, legalOwner);
        
        int blockedQuantity = 0;
        if (warehouseBlockedInventoryDto != null && warehouseBlockedInventoryDto.getQuantity() != null) {
            blockedQuantity = warehouseBlockedInventoryDto.getQuantity();
        }

        int availableQuantity = totalAvailableQuantity - blockedQuantity;

        // If considerBuffer is true, subtract buffered quantity
        if (considerBuffer) {
            availableQuantity = availableQuantity - bufferedQuantity;
        }
        
        return availableQuantity;
    }

    /**
     * Create available inventory response object
     * @param productId Product ID
     * @param facility Facility code
     * @param availableQuantity Available quantity
     * @return AvailableInventoryResponse object
     */
    private AvailableInventoryResponse createAvailableInventoryResponse(Long productId, String facility, int availableQuantity) {
        logger.info("[createAvailableInventoryResponse] Creating response for productId: {}, facility: {}, availableQuantity: {}", 
            productId, facility, availableQuantity);
        AvailableInventoryResponse response = new AvailableInventoryResponse();
        response.setProductId(productId);
        response.setFacility(facility);
        response.setCount(availableQuantity);
        return response;
    }

}
