package com.lenskart.nexs.cid.entity;

import com.lenskart.nexs.cid.enums.WarehouseBlockedOrderItemStatus;
import com.lenskart.nexs.commons.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;

@Getter
@Setter
@ToString(callSuper = true)
@Entity
@Table(name = "warehouse_blocked_order_item")
public class WarehouseBlockedOrderItem extends BaseEntity {

    @Column(name = "order_item_id", nullable = false)
    private Long orderItemId;

    @Column(name = "order_id", nullable = false, length = 50)
    private Long orderId;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private WarehouseBlockedOrderItemStatus warehouseBlockedOrderItemStatus;

    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "warehouse_blocked_inventory_id", nullable = false)
    private WarehouseBlockedInventory warehouseBlockedInventory;

    @Column(name = "entity_type")
    private String entityType;
}
