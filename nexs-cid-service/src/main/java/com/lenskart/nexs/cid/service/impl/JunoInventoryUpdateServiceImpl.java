package com.lenskart.nexs.cid.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.nexs.cid.connector.CatalogOpsConnector;
import com.lenskart.nexs.cid.connector.JunoConnector;
import com.lenskart.nexs.cid.connector.OrderInterceptorConnector;
import com.lenskart.nexs.cid.connector.OrderManagementServiceConnector;
import com.lenskart.nexs.cid.dto.WarehouseBlockedInventoryDto;
import com.lenskart.nexs.cid.dto.WarehouseInventoryDto;
import com.lenskart.nexs.cid.enums.TransactionTypes;
import com.lenskart.nexs.cid.request.juno.JunoInventoryTransaction;
import com.lenskart.nexs.cid.request.juno.JunoInventoryUpdateRequest;
import com.lenskart.nexs.cid.response.MetaDataResponse;
import com.lenskart.nexs.cid.response.UnSynedCountResponse;
import com.lenskart.nexs.cid.service.JunoInventoryUpdateService;
import com.lenskart.nexs.cid.service.WarehouseBlockedInventoryService;
import com.lenskart.nexs.cid.service.WarehouseInventoryService;
import com.lenskart.nexs.cid.util.LegalOwnerUtil;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.ims.enums.LocationType;
import com.lenskart.nexs.ims.request.InventoryUpdateRequest;
import com.lenskart.nexs.ims.request.Product;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Setter(onMethod__ = {@Autowired})
public class JunoInventoryUpdateServiceImpl implements JunoInventoryUpdateService {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    private Logger logger;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    protected WarehouseInventoryService warehouseInventoryService;
    protected WarehouseBlockedInventoryService warehouseBlockedInventoryService;
    protected OrderManagementServiceConnector orderManagementServiceConnector;
    protected OrderInterceptorConnector orderInterceptorConnector;

    protected JunoConnector junoConnector;

    protected CatalogOpsConnector catalogOpsConnector;

    @Value("${facility.defaultHubCode}")
    @Setter(AccessLevel.NONE)
    private String defaultHubCode;

    @Value("#{'${juno.product.classification.notallowed}'.split(',')}")
    @Setter(AccessLevel.NONE)
    private List<Integer> productClassification;

    @Value("#{'${juno.buffer.facilities}'.split(',')}")
    @Setter(AccessLevel.NONE)
    private List<String> bufferFacilities;

    @Value("#{'${juno.excluded.pl.facilities}'.split(',')}")
    @Setter(AccessLevel.NONE)
    private List<String> excludedPlFacility;

    private static final String PID_UNSYNC_COUNT = "PID_UNSYNC_COUNT_";


    public void checkUnSyncedCountAndSendUpdateToJuno(List<InventoryUpdateRequest> inventoryUpdateRequestList) throws Exception {
        try {
            JunoInventoryUpdateRequest junoInventoryUpdateRequest = new JunoInventoryUpdateRequest();
            List<JunoInventoryTransaction> junoInventoryTransactionList = new ArrayList<>();

            Map<String, InventoryUpdateRequest> latestInventoryUpdateRequest = inventoryUpdateRequestList.stream()
                    .collect(Collectors.groupingBy(
                            request -> request.getProductId() + "_" + request.getLegalOwner() + "_" + request.getFacility(),
                            Collectors.collectingAndThen(
                                    Collectors.maxBy(Comparator.comparing(InventoryUpdateRequest::getUpdateTime)),
                                    Optional::get
                            )
                    ));
            List<InventoryUpdateRequest> distinctInventoryUpdateRequestList = new ArrayList<>(latestInventoryUpdateRequest.values());

            for (InventoryUpdateRequest inventoryUpdateRequest : distinctInventoryUpdateRequestList) {
                int unSyncedQuantity;
                Product product = catalogOpsConnector.findByProductId(inventoryUpdateRequest.getProductId());
                if (productClassification.contains(product.getClassification()) && !excludedPlFacility.contains(inventoryUpdateRequest.getFacility())) {
                    continue;
                }
                Integer warehouseQuantity = getWarehouseBasedInventoryCount(inventoryUpdateRequest);
                Integer blockedQuantity = getBlockedCount(inventoryUpdateRequest);
                String hubCode = getHubCode(inventoryUpdateRequest.getFacility());
                logger.info("hubCode {} and defaultHubCode is {}", hubCode, defaultHubCode);
                String redisKey = PID_UNSYNC_COUNT + inventoryUpdateRequest.getProductId() + "_" + hubCode;
                if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(redisKey))) {
                    unSyncedQuantity = Integer.parseInt(Objects.requireNonNull(stringRedisTemplate.opsForValue().get(redisKey)));
                } else {
                    unSyncedQuantity = getUnSyncedOrderCount(Long.valueOf(inventoryUpdateRequest.getProductId()), hubCode, inventoryUpdateRequest.getLegalOwner());
                    stringRedisTemplate.opsForValue().set(redisKey, String.valueOf(unSyncedQuantity), 5, TimeUnit.MINUTES);
                }
                logger.info("[checkUnSyncedCountAndSendUpdateToJuno] for productId {} warehouseQuantity {} and blockedQuantity {} and unSyncedQuantity {}", inventoryUpdateRequest.getProductId(), warehouseQuantity, blockedQuantity, unSyncedQuantity);
                int junoUpdateQuantity = warehouseQuantity - blockedQuantity - unSyncedQuantity;
                buildJunoRequestPayload(inventoryUpdateRequest, junoUpdateQuantity, junoInventoryTransactionList);
            }
            junoInventoryUpdateRequest.setSource("CID");

            junoInventoryUpdateRequest.setTransactions(junoInventoryTransactionList);
            if (!CollectionUtils.isEmpty(junoInventoryUpdateRequest.getTransactions())) {
                junoConnector.updateInventoryToJuno(junoInventoryUpdateRequest);
            }
        } catch (Exception e) {
            logger.error("Issue while processing juno request {} stacktrace is {} and error is {} ", e.getMessage(), e.getStackTrace(), e);
            throw new Exception("Issue while processing juno request" + e);
        }
    }

    private String getHubCode(String facility) {
        if (LegalOwnerUtil.getFacilityHubCodeMap().containsKey(facility)) {
            return LegalOwnerUtil.getFacilityHubCodeMap().get(facility);
        } else {
            return defaultHubCode;
        }
    }

    private void buildJunoRequestPayload(InventoryUpdateRequest request, Integer quantity, List<JunoInventoryTransaction> junoInventoryTransactionList) {
        JunoInventoryTransaction junoInventoryTransaction = new JunoInventoryTransaction();
        junoInventoryTransaction.setTransactionType(TransactionTypes.EXACT.name());
        junoInventoryTransaction.setQuantity(quantity);
        junoInventoryTransaction.setFacilityCode(request.getFacility());
        junoInventoryTransaction.setProductId(request.getProductId());
        junoInventoryTransaction.setLegalOwner(request.getLegalOwner());
        junoInventoryTransaction.setUpdateTime(request.getUpdateTime());
        junoInventoryTransactionList.add(junoInventoryTransaction);
    }

    private int getUnSyncedOrderCount(Long productId, String hubCode, String legalOwner) throws Exception {
        if (defaultHubCode.equals(hubCode)) {
            return 0;
        }
        CompletableFuture<Object> omsUnSyncedQuantity = orderManagementServiceConnector.getOmsUnSyncedQuantity(productId, legalOwner, new Date(), hubCode);
        CompletableFuture<Object> orderInterceptorUnSyncedQuantity = orderInterceptorConnector.getOrderInterceptorUnSyncedQuantity(productId, legalOwner, hubCode);
        try {
            ObjectMapper mapper = new ObjectMapper();
            UnSynedCountResponse unSynedCountOmsResponse = mapper.readValue(omsUnSyncedQuantity.get().toString(), new TypeReference<UnSynedCountResponse>() {
            });
            MetaDataResponse orderInterceptorMeta = mapper.readValue(orderInterceptorUnSyncedQuantity.get().toString(), new TypeReference<MetaDataResponse>() {
            });
            UnSynedCountResponse unSynedCountOrderInterceptorResponse = mapper.convertValue(orderInterceptorMeta.getData(), UnSynedCountResponse.class);
            return unSynedCountOmsResponse.getQuantity() + unSynedCountOrderInterceptorResponse.getQuantity();
        } catch (Exception e) {
            throw new Exception("error while fetching unsyned quantity for productId: " + productId + " error: " + e.getMessage(), e);
        }
    }

    private Integer getBlockedCount(InventoryUpdateRequest request) {
        WarehouseBlockedInventoryDto warehouseBlockedInventoryDto = warehouseBlockedInventoryService.findByProductIdAndFacilityAndLegalOwner(Long.valueOf(request.getProductId()), request.getFacility(), request.getLegalOwner());
        return warehouseBlockedInventoryDto != null ? warehouseBlockedInventoryDto.getQuantity() : 0;
    }

    protected Integer getWarehouseBasedInventoryCount(InventoryUpdateRequest inventoryUpdateRequest) {
        Integer warehouseQuantity = 0;
        String searchTerms = "productId.eq:" + inventoryUpdateRequest.getProductId() + "___condition.eq:" + inventoryUpdateRequest.getCondition()
                + "___availability.eq:" + inventoryUpdateRequest.getAvailability() + "___status.eq:" + inventoryUpdateRequest.getStatus()
                + "___facility.eq:" + inventoryUpdateRequest.getFacility() + "___legalOwner.eq:" + inventoryUpdateRequest.getLegalOwner()
                + "___locationType.eq:" + LocationType.DEFAULT;
        List<WarehouseInventoryDto> warehouseInventoryDtos = warehouseInventoryService.search(searchTerms);
        if (CollectionUtils.isEmpty(warehouseInventoryDtos)) {
            return warehouseQuantity;
        } else {
            if (bufferFacilities.contains(inventoryUpdateRequest.getFacility())) {
                return warehouseInventoryDtos.get(0).getQuantity() - warehouseInventoryDtos.get(0).getBufferedQuantity();
            }
            return warehouseInventoryDtos.get(0).getQuantity();
        }
    }
}
