package com.lenskart.nexs.cid.service.impl;

import com.lenskart.nexs.cid.dto.JunoAuditLogDto;
import com.lenskart.nexs.cid.dto.JunoAuditStatsDto;
import com.lenskart.nexs.cid.entity.JunoAuditLog;
import com.lenskart.nexs.cid.enums.JunoApiStatus;
import com.lenskart.nexs.cid.repository.JunoAuditLogRepository;
import com.lenskart.nexs.cid.request.juno.JunoInventoryTransaction;
import com.lenskart.nexs.cid.request.juno.JunoInventoryUpdateRequest;
import com.lenskart.nexs.cid.service.JunoAuditService;
import com.lenskart.nexs.cid.util.ObjectHelper;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Implementation of JunoAuditService
 */
@Service
@Setter(onMethod__ = {@Autowired})
public class JunoAuditServiceImpl implements JunoAuditService {

    @CustomLogger
    private Logger logger;

    private JunoAuditLogRepository junoAuditLogRepository;

    @Override
    @Transactional
    public JunoAuditLog createAuditLog(String correlationId, JunoInventoryUpdateRequest request, String apiEndpoint) {
        try {
            JunoAuditLog auditLog = new JunoAuditLog();
            auditLog.setCorrelationId(correlationId);
            auditLog.setRequestPayload(ObjectHelper.writeValue(request));
            auditLog.setStatus(JunoApiStatus.PENDING);
            auditLog.setRequestTimestamp(new Date());
            auditLog.setApiEndpoint(apiEndpoint);
            auditLog.setSource(request.getSource());
            auditLog.setRetryCount(0);

            if (!CollectionUtils.isEmpty(request.getTransactions())) {
                auditLog.setTransactionCount(request.getTransactions().size());
                
                // Extract product IDs, facility codes, and legal owners
                List<String> productIds = request.getTransactions().stream()
                    .map(t -> String.valueOf(t.getProductId()))
                    .distinct()
                    .collect(Collectors.toList());
                auditLog.setProductIds(String.join(",", productIds));

                List<String> facilityCodes = request.getTransactions().stream()
                    .map(JunoInventoryTransaction::getFacilityCode)
                    .distinct()
                    .collect(Collectors.toList());
                auditLog.setFacilityCodes(String.join(",", facilityCodes));

                List<String> legalOwners = request.getTransactions().stream()
                    .map(JunoInventoryTransaction::getLegalOwner)
                    .distinct()
                    .collect(Collectors.toList());
                auditLog.setLegalOwners(String.join(",", legalOwners));
            }

            JunoAuditLog savedLog = junoAuditLogRepository.save(auditLog);
            logger.info("[createAuditLog] Created audit log with ID: {} for correlation ID: {}", 
                       savedLog.getId(), correlationId);
            return savedLog;
        } catch (Exception e) {
            logger.error("[createAuditLog] Error creating audit log for correlation ID: {}, error: {}", 
                        correlationId, e.getMessage(), e);
            throw new RuntimeException("Failed to create audit log", e);
        }
    }

    @Override
    @Transactional
    public void updateAuditLogWithResponse(Long auditLogId, String responsePayload, JunoApiStatus status, 
                                          Integer httpStatusCode, Long durationMs) {
        try {
            Optional<JunoAuditLog> optionalLog = junoAuditLogRepository.findById(auditLogId);
            if (optionalLog.isPresent()) {
                JunoAuditLog auditLog = optionalLog.get();
                auditLog.setResponsePayload(responsePayload);
                auditLog.setStatus(status);
                auditLog.setHttpStatusCode(httpStatusCode);
                auditLog.setResponseTimestamp(new Date());
                auditLog.setDurationMs(durationMs);
                
                junoAuditLogRepository.save(auditLog);
                logger.info("[updateAuditLogWithResponse] Updated audit log ID: {} with status: {}", 
                           auditLogId, status);
            } else {
                logger.warn("[updateAuditLogWithResponse] Audit log not found with ID: {}", auditLogId);
            }
        } catch (Exception e) {
            logger.error("[updateAuditLogWithResponse] Error updating audit log ID: {}, error: {}", 
                        auditLogId, e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public void updateAuditLogWithError(Long auditLogId, String errorMessage, String stackTrace, 
                                       JunoApiStatus status, Integer httpStatusCode, Long durationMs) {
        try {
            Optional<JunoAuditLog> optionalLog = junoAuditLogRepository.findById(auditLogId);
            if (optionalLog.isPresent()) {
                JunoAuditLog auditLog = optionalLog.get();
                auditLog.setErrorMessage(errorMessage);
                auditLog.setErrorStackTrace(stackTrace);
                auditLog.setStatus(status);
                auditLog.setHttpStatusCode(httpStatusCode);
                auditLog.setResponseTimestamp(new Date());
                auditLog.setDurationMs(durationMs);
                
                junoAuditLogRepository.save(auditLog);
                logger.info("[updateAuditLogWithError] Updated audit log ID: {} with error status: {}", 
                           auditLogId, status);
            } else {
                logger.warn("[updateAuditLogWithError] Audit log not found with ID: {}", auditLogId);
            }
        } catch (Exception e) {
            logger.error("[updateAuditLogWithError] Error updating audit log ID: {}, error: {}", 
                        auditLogId, e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public void incrementRetryCount(Long auditLogId) {
        try {
            Optional<JunoAuditLog> optionalLog = junoAuditLogRepository.findById(auditLogId);
            if (optionalLog.isPresent()) {
                JunoAuditLog auditLog = optionalLog.get();
                auditLog.setRetryCount(auditLog.getRetryCount() + 1);
                auditLog.setStatus(JunoApiStatus.RETRY);
                
                junoAuditLogRepository.save(auditLog);
                logger.info("[incrementRetryCount] Incremented retry count to {} for audit log ID: {}", 
                           auditLog.getRetryCount(), auditLogId);
            } else {
                logger.warn("[incrementRetryCount] Audit log not found with ID: {}", auditLogId);
            }
        } catch (Exception e) {
            logger.error("[incrementRetryCount] Error incrementing retry count for audit log ID: {}, error: {}", 
                        auditLogId, e.getMessage(), e);
        }
    }

    @Override
    public List<JunoAuditLogDto> findByCorrelationId(String correlationId) {
        List<JunoAuditLog> auditLogs = junoAuditLogRepository.findByCorrelationIdOrderByCreatedAtDesc(correlationId);
        return auditLogs.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public Page<JunoAuditLogDto> findByStatus(JunoApiStatus status, Pageable pageable) {
        Page<JunoAuditLog> auditLogs = junoAuditLogRepository.findByStatusOrderByCreatedAtDesc(status, pageable);
        return auditLogs.map(this::convertToDto);
    }

    @Override
    public Page<JunoAuditLogDto> findByDateRange(Date startDate, Date endDate, Pageable pageable) {
        Page<JunoAuditLog> auditLogs = junoAuditLogRepository.findByCreatedAtBetweenOrderByCreatedAtDesc(startDate, endDate, pageable);
        return auditLogs.map(this::convertToDto);
    }

    @Override
    public Page<JunoAuditLogDto> findByProductId(String productId, Pageable pageable) {
        Page<JunoAuditLog> auditLogs = junoAuditLogRepository.findByProductId(productId, pageable);
        return auditLogs.map(this::convertToDto);
    }

    @Override
    public Page<JunoAuditLogDto> findByFacilityCode(String facilityCode, Pageable pageable) {
        Page<JunoAuditLog> auditLogs = junoAuditLogRepository.findByFacilityCode(facilityCode, pageable);
        return auditLogs.map(this::convertToDto);
    }

    @Override
    public Page<JunoAuditLogDto> findByLegalOwner(String legalOwner, Pageable pageable) {
        Page<JunoAuditLog> auditLogs = junoAuditLogRepository.findByLegalOwner(legalOwner, pageable);
        return auditLogs.map(this::convertToDto);
    }

    @Override
    public List<JunoAuditLogDto> getFailedLogs(Date startDate, Date endDate) {
        List<JunoAuditLog> failedLogs = junoAuditLogRepository.findFailedLogsBetweenDates(startDate, endDate);
        return failedLogs.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<JunoAuditLogDto> getHighRetryLogs(Integer minRetryCount) {
        List<JunoAuditLog> highRetryLogs = junoAuditLogRepository.findHighRetryLogs(minRetryCount);
        return highRetryLogs.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public JunoAuditStatsDto getAuditStats(Date startDate, Date endDate) {
        try {
            JunoAuditStatsDto stats = new JunoAuditStatsDto();

            // Get counts by status
            Long totalRequests = junoAuditLogRepository.countByDateRange(startDate, endDate);
            Long successfulRequests = junoAuditLogRepository.countByStatusAndDateRange(JunoApiStatus.SUCCESS, startDate, endDate);
            Long failedRequests = junoAuditLogRepository.countByStatusAndDateRange(JunoApiStatus.FAILED, startDate, endDate);
            Long timeoutRequests = junoAuditLogRepository.countByStatusAndDateRange(JunoApiStatus.TIMEOUT, startDate, endDate);
            Long retryRequests = junoAuditLogRepository.countByStatusAndDateRange(JunoApiStatus.RETRY, startDate, endDate);

            stats.setTotalRequests(totalRequests);
            stats.setSuccessfulRequests(successfulRequests);
            stats.setFailedRequests(failedRequests);
            stats.setTimeoutRequests(timeoutRequests);
            stats.setRetryRequests(retryRequests);

            // Calculate rates
            if (totalRequests > 0) {
                stats.setSuccessRate((double) successfulRequests / totalRequests * 100);
                stats.setFailureRate((double) failedRequests / totalRequests * 100);
            } else {
                stats.setSuccessRate(0.0);
                stats.setFailureRate(0.0);
            }

            // Get additional stats from database (would need custom queries)
            // For now, setting default values
            stats.setAverageDurationMs(0L);
            stats.setMaxDurationMs(0L);
            stats.setMinDurationMs(0L);
            stats.setMaxRetryCount(0);
            stats.setTotalTransactions(0L);

            return stats;
        } catch (Exception e) {
            logger.error("[getAuditStats] Error calculating audit stats for date range {} to {}, error: {}",
                        startDate, endDate, e.getMessage(), e);
            return new JunoAuditStatsDto();
        }
    }

    private JunoAuditLogDto convertToDto(JunoAuditLog auditLog) {
        JunoAuditLogDto dto = new JunoAuditLogDto();
        BeanUtils.copyProperties(auditLog, dto);
        return dto;
    }
}
