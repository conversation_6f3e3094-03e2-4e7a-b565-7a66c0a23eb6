package com.lenskart.nexs.cid.service.impl;

import com.lenskart.nexs.cid.entity.JunoAuditLog;
import com.lenskart.nexs.cid.enums.JunoApiStatus;
import com.lenskart.nexs.cid.repository.JunoAuditLogRepository;
import com.lenskart.nexs.cid.request.juno.JunoInventoryTransaction;
import com.lenskart.nexs.cid.request.juno.JunoInventoryUpdateRequest;
import com.lenskart.nexs.cid.service.JunoAuditService;
import com.lenskart.nexs.cid.util.ObjectHelper;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * Implementation of JunoAuditService
 */
@Service
@Setter(onMethod__ = {@Autowired})
public class JunoAuditServiceImpl implements JunoAuditService {

    @CustomLogger
    private Logger logger;

    private JunoAuditLogRepository junoAuditLogRepository;

    @Override
    @Transactional
    public void createAuditLogs(String correlationId, JunoInventoryUpdateRequest request, String apiEndpoint) {
        try {
            if (!CollectionUtils.isEmpty(request.getTransactions())) {
                String requestPayload = ObjectHelper.writeValue(request);
                Date requestTimestamp = new Date();

                for (JunoInventoryTransaction transaction : request.getTransactions()) {
                    JunoAuditLog auditLog = new JunoAuditLog();
                    auditLog.setCorrelationId(correlationId);
                    auditLog.setProductId(transaction.getProductId());
                    auditLog.setFacilityCode(transaction.getFacilityCode());
                    auditLog.setQuantity(transaction.getQuantity());
                    auditLog.setTransactionType(transaction.getTransactionType());
                    auditLog.setLegalOwner(transaction.getLegalOwner());
                    auditLog.setUpdateTime(transaction.getUpdateTime());
                    auditLog.setRequestPayload(requestPayload);
                    auditLog.setStatus(JunoApiStatus.PENDING);
                    auditLog.setRequestTimestamp(requestTimestamp);
                    auditLog.setSource(request.getSource());

                    junoAuditLogRepository.save(auditLog);
                }

                logger.info("[createAuditLogs] Created {} audit log entries for correlation ID: {}",
                           request.getTransactions().size(), correlationId);
            }
        } catch (Exception e) {
            logger.error("[createAuditLogs] Error creating audit logs for correlation ID: {}, error: {}",
                        correlationId, e.getMessage(), e);
            // Don't throw exception to avoid breaking the main flow
        }
    }

    @Override
    @Transactional
    public void updateAuditLogsWithResponse(String correlationId, String responsePayload, JunoApiStatus status,
                                          Integer httpStatusCode, Long durationMs) {
        try {
            List<JunoAuditLog> auditLogs = junoAuditLogRepository.findByCorrelationIdOrderByCreatedAtDesc(correlationId);
            if (!auditLogs.isEmpty()) {
                Date responseTimestamp = new Date();
                for (JunoAuditLog auditLog : auditLogs) {
                    auditLog.setResponsePayload(responsePayload);
                    auditLog.setStatus(status);
                    auditLog.setHttpStatusCode(httpStatusCode);
                    auditLog.setResponseTimestamp(responseTimestamp);
                    auditLog.setDurationMs(durationMs);

                    junoAuditLogRepository.save(auditLog);
                }
                logger.info("[updateAuditLogsWithResponse] Updated {} audit logs for correlation ID: {} with status: {}",
                           auditLogs.size(), correlationId, status);
            } else {
                logger.warn("[updateAuditLogsWithResponse] No audit logs found for correlation ID: {}", correlationId);
            }
        } catch (Exception e) {
            logger.error("[updateAuditLogsWithResponse] Error updating audit logs for correlation ID: {}, error: {}",
                        correlationId, e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public void updateAuditLogsWithError(String correlationId, String errorMessage,
                                       JunoApiStatus status, Integer httpStatusCode, Long durationMs) {
        try {
            List<JunoAuditLog> auditLogs = junoAuditLogRepository.findByCorrelationIdOrderByCreatedAtDesc(correlationId);
            if (!auditLogs.isEmpty()) {
                Date responseTimestamp = new Date();
                for (JunoAuditLog auditLog : auditLogs) {
                    auditLog.setErrorMessage(errorMessage);
                    auditLog.setStatus(status);
                    auditLog.setHttpStatusCode(httpStatusCode);
                    auditLog.setResponseTimestamp(responseTimestamp);
                    auditLog.setDurationMs(durationMs);

                    junoAuditLogRepository.save(auditLog);
                }
                logger.info("[updateAuditLogsWithError] Updated {} audit logs for correlation ID: {} with error status: {}",
                           auditLogs.size(), correlationId, status);
            } else {
                logger.warn("[updateAuditLogsWithError] No audit logs found for correlation ID: {}", correlationId);
            }
        } catch (Exception e) {
            logger.error("[updateAuditLogsWithError] Error updating audit logs for correlation ID: {}, error: {}",
                        correlationId, e.getMessage(), e);
        }
    }

}
