package com.lenskart.nexs.cid.connector;


import com.lenskart.nexs.cid.constant.MetricConstants;
import com.lenskart.nexs.unicommerce.config.UnicommerceRestConfig;
import com.lenskart.nexs.unicommerce.constants.UnicomRestConfigConstants;
import com.lenskart.nexs.unicommerce.util.UnicommerceUtils;
import com.unicommerce.uniware.services.GetInventorySnapshotRequest;
import com.unicommerce.uniware.services.GetInventorySnapshotResponse;
import com.unicommerce.uniware.services.Unicommerce;
import io.micrometer.core.annotation.Timed;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;

import javax.xml.ws.BindingProvider;
import java.net.MalformedURLException;
import java.util.List;
import java.util.Map;

@Component
public class UnicomConnector {

    private final Counter errorCounter  = Metrics.counter(MetricConstants.UNICOM_GET_INVENTORY_SNAPSHOT, "result", "failure");

    @Setter(onMethod__ = {@Autowired})
    private UnicommerceRestConfig unicommerceRestConfig;
    @Setter(onMethod__ = {@Autowired})
    private UnicommerceUtils unicommerceUtils;


    @Timed
    public GetInventorySnapshotResponse fetchInventorySnapshot(List<Integer> productIds, String facility) throws MalformedURLException {
        try {
            String wsdl = getWsdl(facility);
            String location = getWsdlLocation(facility) + facility;
            Unicommerce unicommerce = unicommerceUtils.getUnicommerceEndPoint(wsdl);
            ((BindingProvider) unicommerce).getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY,
                    location);
            GetInventorySnapshotRequest getInventorySnapshotRequest = new GetInventorySnapshotRequest();
            GetInventorySnapshotRequest.ItemTypes itemTypes = new GetInventorySnapshotRequest.ItemTypes();
            productIds.forEach(productId -> {
                GetInventorySnapshotRequest.ItemTypes.ItemType itemType = new GetInventorySnapshotRequest.ItemTypes.ItemType();
                itemType.setItemSKU(String.valueOf(productId));
                itemTypes.getItemType().add(itemType);
            });
            getInventorySnapshotRequest.setItemTypes(itemTypes);
            return unicommerce.getInventorySnapshot(getInventorySnapshotRequest);
        } catch (Exception e) {
            errorCounter.increment();
            throw e;
        }
    }

    private String getWsdl(String facilityCode) {
        Map<String, Object> facilityInfo = getFacilityMap(facilityCode);
        return (String) facilityInfo.getOrDefault(UnicomRestConfigConstants.WSDL, null);
    }

    private Map<String, Object> getFacilityMap(String facilityCode) {
        Map<String, Map<String, Object>> unicomFacilityMap = unicommerceRestConfig.getFacility();
        Map<String, Object> facilityInfo = unicomFacilityMap.getOrDefault(facilityCode, null);
        if (facilityInfo == null) {
            throw new ResponseStatusException(HttpStatus.EXPECTATION_FAILED, "Unable to fetch wsdl for given facility : " + facilityCode);
        }
        return facilityInfo;
    }

    private String getWsdlLocation(String facilityCode) {
        Map<String, Object> facilityInfo = getFacilityMap(facilityCode);
        return (String) facilityInfo.getOrDefault(UnicomRestConfigConstants.LOCATION, null);
    }
}
