package com.lenskart.nexs.cid.util;

import com.lenskart.nexs.cid.constant.MetricConstants;
import com.lenskart.nexs.cid.request.FailureMessage;
import com.lenskart.nexs.cid.request.updateinventory.StockAdjustmentResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;


import static com.lenskart.nexs.cid.config.NewRelicConfig.isNewRelicEnabled;
import static com.lenskart.nexs.newrelic.util.NewRelicUtil.publishRecord;

public class NewRelicUtil {

    private NewRelicUtil() {
    }

    public static void publishScmUpdateResponse(String recordName, StockAdjustmentResponse stockAdjustmentResponse) {
        if (stockAdjustmentResponse != null && stockAdjustmentResponse.getReturn() != null && isNewRelicEnabled) {
            publishRecord(recordName, stockAdjustmentResponse.getReturn());
        }
    }

    public static void logSCMFailure(String request, Exception e, Logger log) {
        if(!isNewRelicEnabled)
            return;
        try {
            FailureMessage failureMessage = new FailureMessage();
            String[] pids = StringUtils.substringsBetween(request, ":ProductId>", "</ns2:ProductId>");
            if (Objects.isNull(pids))
                pids = (StringUtils.substringsBetween(request, "<ProductId xsi:type=\"xsd:string\">", "</ProductId>"));
            List<String> pidList = Arrays.asList(pids);
            failureMessage.setPids(pidList);
            String exceptionMessage = e.getMessage();
            if (StringUtils.isEmpty(exceptionMessage))
                failureMessage.setMessage("Unknown exception");
            else
                failureMessage.setMessage(exceptionMessage);
            publishRecord(MetricConstants.NEXS_CID_SCM_FAILURE_PIDS, failureMessage);
        } catch (Exception ex) {
            log.error("Error pushing log failure event to newrelic", ex);
        }
    }

    public static void recordEvent( String eventName, Object object) {
        if(!isNewRelicEnabled)
            return;
        publishRecord( eventName, object);
    }
}