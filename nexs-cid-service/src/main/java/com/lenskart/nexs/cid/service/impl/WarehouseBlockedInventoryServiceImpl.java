package com.lenskart.nexs.cid.service.impl;

import com.lenskart.nexs.cid.dto.WarehouseBlockedInventoryDto;
import com.lenskart.nexs.cid.entity.WarehouseBlockedInventory;
import com.lenskart.nexs.cid.service.WarehouseBlockedInventoryService;
import com.lenskart.nexs.commons.service.impl.BaseServiceImp;
import org.springframework.stereotype.Service;

@Service
public class WarehouseBlockedInventoryServiceImpl extends BaseServiceImp<WarehouseBlockedInventoryDto, WarehouseBlockedInventory> implements WarehouseBlockedInventoryService {

    @Override
    public WarehouseBlockedInventoryDto findByProductIdAndFacilityAndLegalOwner(Long productId, String facility, String legalOwner) {
        String searchTerm = "productId.eq:" + productId + "___facility.eq:" + facility + "___legalOwner.eq:" + legalOwner;
        return  findBySearchTerms(searchTerm);
    }

}
