package com.lenskart.nexs.cid.controller;

import com.lenskart.nexs.cid.dto.WarehouseBlockedInventoryDto;
import com.lenskart.nexs.cid.entity.WarehouseBlockedInventory;
import com.lenskart.nexs.cid.request.StockBlockOrderRequest;
import com.lenskart.nexs.cid.response.StockBlockOrderResponse;
import com.lenskart.nexs.commons.dto.BaseResponseModel;
import com.lenskart.nexs.cid.constant.ControllerUrl;
import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.cid.facade.InventoryBlockFacade;
import com.lenskart.nexs.commonlogger.annotations.RestLogging;
import com.lenskart.nexs.commons.controller.BaseController;
import com.lenskart.nexs.commons.response.CommonResponseBuilder;
import com.lenskart.nexs.commons.response.ResponseDTO;
import com.lenskart.nexs.constants.responseMessage.ResponseCodes;
import com.lenskart.nexs.exception.CustomException;
import com.lenskart.nexs.ims.request.OrderStockCheckRequest;
import com.lenskart.nexs.ims.response.OrderItemStockCheckResponse;
import io.micrometer.core.annotation.Timed;
import lombok.Setter;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.support.atomic.RedisAtomicInteger;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

import static com.lenskart.nexs.cid.constant.CommonConstants.*;

@Timed
@RestController
@RequestMapping(ControllerUrl.BASE_URL_V1 + ControllerUrl.PENDING_ORDER)
public class WarehouseBlockedInventoryController extends BaseController<ResponseDTO, WarehouseBlockedInventoryDto, WarehouseBlockedInventory> {

    @Setter(onMethod__ = {@Autowired})
    private InventoryBlockFacade inventoryBlockFacade;

    @Value("${fail.fast.cid.response}")
    private boolean failFastCidResponse;

    @RestLogging
    @PostMapping(value = ControllerUrl.INVENTORY_CHECK_AND_BLOCK)
    public ResponseEntity<BaseResponseModel> inventoryCheckAndBlock(@RequestBody @Valid OrderStockCheckRequest orderStockCheckRequest) throws ApplicationException {
        String updatedBy = orderStockCheckRequest.getUpdatedBy();
        MDC.put(USER_ID, StringUtils.isEmpty(updatedBy) ? DEFAULT_USER : updatedBy);

        List<OrderItemStockCheckResponse> orderItemStockCheckResponses = new ArrayList<>();
        try {
            inventoryBlockFacade.performInventoryCheckAndBlock(orderStockCheckRequest, orderItemStockCheckResponses);
        } catch (ApplicationException exception) {
            logger.error("[inventoryCheckAndBlock] stock block failed for request {} with exception {}", orderStockCheckRequest, orderItemStockCheckResponses);
            if (failFastCidResponse) {
                throw new ApplicationException("Exception while blocking inventory in cid", exception);
            }
        }

        return CommonResponseBuilder.successResponse(orderItemStockCheckResponses, MESSAGE, ResponseCodes.RESPONSE_SUCCESS);
    }

    @RestLogging
    @PostMapping(value = ControllerUrl.INVENTORY_BLOCK_WRAPPER)
    public ResponseEntity<BaseResponseModel> inventoryBlockWrapper(@RequestBody @Valid OrderStockCheckRequest orderStockCheckRequest) throws ApplicationException {
        String updatedBy = orderStockCheckRequest.getUpdatedBy();
        MDC.put(USER_ID, StringUtils.isEmpty(updatedBy) ? DEFAULT_USER : updatedBy);

        List<OrderItemStockCheckResponse> orderItemStockCheckResponses = new ArrayList<>();
        inventoryBlockFacade.verifyAndBlockInventory(orderStockCheckRequest, orderItemStockCheckResponses);

        return CommonResponseBuilder.successResponse(orderItemStockCheckResponses, MESSAGE, ResponseCodes.RESPONSE_SUCCESS);
    }

    @RestLogging
    @PostMapping(value = ControllerUrl.STOCK_BLOCK_AVAILABLE_INVENTORY)
    public ResponseEntity<BaseResponseModel> stockBlockAvailableInventory(@RequestBody @Valid StockBlockOrderRequest stockBlockOrderRequest) throws ApplicationException {
        String updatedBy = stockBlockOrderRequest.getUpdatedBy();
        MDC.put(USER_ID, StringUtils.isEmpty(updatedBy) ? DEFAULT_USER : updatedBy);

        logger.info("[stockBlockAvailableInventory] request {}", stockBlockOrderRequest);
        StockBlockOrderResponse stockBlockOrderResponse = inventoryBlockFacade.stockBlockAvailableInventory(stockBlockOrderRequest);
        logger.info("[stockBlockAvailableInventory] response {}", stockBlockOrderResponse);
        return CommonResponseBuilder.successResponse(stockBlockOrderResponse, MESSAGE, ResponseCodes.RESPONSE_SUCCESS);
    }

    @RestLogging
    @PostMapping(value = ControllerUrl.RECONCILE_BLOCKED_INVENTORY)
    public ResponseEntity<BaseResponseModel> reconcileBlockedInventory(@RequestBody List<Long> pidList,
                                                                          @RequestParam(name = "facilityCode") String facilityCode,
                                                                          @RequestParam(name = "legalOwner", defaultValue = "LKIN") String legalOwner) throws ApplicationException {
        MDC.put(USER_ID,  RECONCILE_USER);
        logger.info("[reconcileBlockedInventory] request for pidList {} and facility {} and legalOwner {}", pidList, facilityCode, legalOwner);
        inventoryBlockFacade.reconcileBlockedOrderInventory(pidList, facilityCode, legalOwner);
        return CommonResponseBuilder.successResponse(MESSAGE, MESSAGE, ResponseCodes.RESPONSE_SUCCESS);
    }
}
