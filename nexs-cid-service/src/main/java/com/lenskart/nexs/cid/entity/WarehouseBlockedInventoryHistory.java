package com.lenskart.nexs.cid.entity;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

@Getter
@Setter
@ToString(callSuper = true)
@Entity
@IdClass(HistoryCompositeKey.class)
@Table(name = "warehouse_blocked_inventory_history")
public class WarehouseBlockedInventoryHistory {

    @Id
    @Column(name = "id", nullable = false)
    private Long id;

    @Id
    @Column(name = "rev", nullable = false)
    private Long rev;

    @Column(name = "rev_type")
    private boolean revType;

    @Column(name = "created_by", nullable = false, length = 100)
    private String createdBy;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    @Column(name = "updated_by", nullable = false, length = 100)
    private String updatedBy;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at", nullable = false)
    private Date updatedAt;

    @Column(name = "pid", nullable = false)
    private Long productId;

    @Column(name = "facility", nullable = false, length = 50)
    private String facility;

    @Column(name = "quantity", nullable = false, length = 50)
    private Integer quantity;

    @Column(name = "unfulfillable_qty")
    private Integer unfulfillableQty;
}
