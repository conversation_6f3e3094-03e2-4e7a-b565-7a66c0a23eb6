package com.lenskart.nexs.cid.controller;

import com.lenskart.nexs.cid.constant.ControllerUrl;
import com.lenskart.nexs.cid.dto.JunoAuditLogDto;
import com.lenskart.nexs.cid.dto.JunoAuditStatsDto;
import com.lenskart.nexs.cid.enums.JunoApiStatus;
import com.lenskart.nexs.cid.service.JunoAuditService;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * REST Controller for Juno audit log operations
 */
@RestController
@RequestMapping(ControllerUrl.BASE_URL_V1 + "/juno-audit")
@Setter(onMethod__ = {@Autowired})
public class JunoAuditController {

    @CustomLogger
    private Logger logger;

    private JunoAuditService junoAuditService;

    /**
     * Get audit logs by correlation ID
     */
    @GetMapping("/correlation/{correlationId}")
    public ResponseEntity<List<JunoAuditLogDto>> getAuditLogsByCorrelationId(
            @PathVariable String correlationId) {
        try {
            logger.info("[getAuditLogsByCorrelationId] Fetching audit logs for correlation ID: {}", correlationId);
            List<JunoAuditLogDto> auditLogs = junoAuditService.findByCorrelationId(correlationId);
            return ResponseEntity.ok(auditLogs);
        } catch (Exception e) {
            logger.error("[getAuditLogsByCorrelationId] Error fetching audit logs for correlation ID: {}, error: {}", 
                        correlationId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get audit logs by status with pagination
     */
    @GetMapping("/status/{status}")
    public ResponseEntity<Page<JunoAuditLogDto>> getAuditLogsByStatus(
            @PathVariable JunoApiStatus status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            logger.info("[getAuditLogsByStatus] Fetching audit logs for status: {}, page: {}, size: {}", 
                       status, page, size);
            Pageable pageable = PageRequest.of(page, size);
            Page<JunoAuditLogDto> auditLogs = junoAuditService.findByStatus(status, pageable);
            return ResponseEntity.ok(auditLogs);
        } catch (Exception e) {
            logger.error("[getAuditLogsByStatus] Error fetching audit logs for status: {}, error: {}", 
                        status, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get audit logs by date range with pagination
     */
    @GetMapping("/date-range")
    public ResponseEntity<Page<JunoAuditLogDto>> getAuditLogsByDateRange(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endDate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            logger.info("[getAuditLogsByDateRange] Fetching audit logs for date range: {} to {}, page: {}, size: {}", 
                       startDate, endDate, page, size);
            Pageable pageable = PageRequest.of(page, size);
            Page<JunoAuditLogDto> auditLogs = junoAuditService.findByDateRange(startDate, endDate, pageable);
            return ResponseEntity.ok(auditLogs);
        } catch (Exception e) {
            logger.error("[getAuditLogsByDateRange] Error fetching audit logs for date range: {} to {}, error: {}", 
                        startDate, endDate, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get audit logs by product ID with pagination
     */
    @GetMapping("/product/{productId}")
    public ResponseEntity<Page<JunoAuditLogDto>> getAuditLogsByProductId(
            @PathVariable String productId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            logger.info("[getAuditLogsByProductId] Fetching audit logs for product ID: {}, page: {}, size: {}", 
                       productId, page, size);
            Pageable pageable = PageRequest.of(page, size);
            Page<JunoAuditLogDto> auditLogs = junoAuditService.findByProductId(productId, pageable);
            return ResponseEntity.ok(auditLogs);
        } catch (Exception e) {
            logger.error("[getAuditLogsByProductId] Error fetching audit logs for product ID: {}, error: {}", 
                        productId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get audit logs by facility code with pagination
     */
    @GetMapping("/facility/{facilityCode}")
    public ResponseEntity<Page<JunoAuditLogDto>> getAuditLogsByFacilityCode(
            @PathVariable String facilityCode,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            logger.info("[getAuditLogsByFacilityCode] Fetching audit logs for facility code: {}, page: {}, size: {}", 
                       facilityCode, page, size);
            Pageable pageable = PageRequest.of(page, size);
            Page<JunoAuditLogDto> auditLogs = junoAuditService.findByFacilityCode(facilityCode, pageable);
            return ResponseEntity.ok(auditLogs);
        } catch (Exception e) {
            logger.error("[getAuditLogsByFacilityCode] Error fetching audit logs for facility code: {}, error: {}", 
                        facilityCode, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get audit logs by legal owner with pagination
     */
    @GetMapping("/legal-owner/{legalOwner}")
    public ResponseEntity<Page<JunoAuditLogDto>> getAuditLogsByLegalOwner(
            @PathVariable String legalOwner,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            logger.info("[getAuditLogsByLegalOwner] Fetching audit logs for legal owner: {}, page: {}, size: {}", 
                       legalOwner, page, size);
            Pageable pageable = PageRequest.of(page, size);
            Page<JunoAuditLogDto> auditLogs = junoAuditService.findByLegalOwner(legalOwner, pageable);
            return ResponseEntity.ok(auditLogs);
        } catch (Exception e) {
            logger.error("[getAuditLogsByLegalOwner] Error fetching audit logs for legal owner: {}, error: {}", 
                        legalOwner, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get failed audit logs within a date range
     */
    @GetMapping("/failed")
    public ResponseEntity<List<JunoAuditLogDto>> getFailedLogs(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endDate) {
        try {
            logger.info("[getFailedLogs] Fetching failed audit logs for date range: {} to {}", startDate, endDate);
            List<JunoAuditLogDto> failedLogs = junoAuditService.getFailedLogs(startDate, endDate);
            return ResponseEntity.ok(failedLogs);
        } catch (Exception e) {
            logger.error("[getFailedLogs] Error fetching failed audit logs for date range: {} to {}, error: {}", 
                        startDate, endDate, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get audit statistics for a date range
     */
    @GetMapping("/stats")
    public ResponseEntity<JunoAuditStatsDto> getAuditStats(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endDate) {
        try {
            logger.info("[getAuditStats] Fetching audit stats for date range: {} to {}", startDate, endDate);
            JunoAuditStatsDto stats = junoAuditService.getAuditStats(startDate, endDate);
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            logger.error("[getAuditStats] Error fetching audit stats for date range: {} to {}, error: {}", 
                        startDate, endDate, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get logs with high retry count
     */
    @GetMapping("/high-retry")
    public ResponseEntity<List<JunoAuditLogDto>> getHighRetryLogs(
            @RequestParam(defaultValue = "3") Integer minRetryCount) {
        try {
            logger.info("[getHighRetryLogs] Fetching high retry logs with min retry count: {}", minRetryCount);
            List<JunoAuditLogDto> highRetryLogs = junoAuditService.getHighRetryLogs(minRetryCount);
            return ResponseEntity.ok(highRetryLogs);
        } catch (Exception e) {
            logger.error("[getHighRetryLogs] Error fetching high retry logs with min retry count: {}, error: {}", 
                        minRetryCount, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
