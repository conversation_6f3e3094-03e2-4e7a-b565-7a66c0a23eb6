package com.lenskart.nexs.cid.connector;

import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.common.http.RestUtils;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.Duration;
import java.util.Date;
import java.util.concurrent.CompletableFuture;
@Configuration
public class OrderManagementServiceConnector {
    @CustomLogger
    private Logger logger;

    @Value("${oms.base.url}")
    private String omsBaseUrl;

    @Value("${oms.connection.timeout}")
    private int omsConnectionTimeout;

    @Value("${oms.read.timeout}")
    private int omsReadTimeout;

    @Value("${oms.unsynced.count.url}")
    private String unSyncedCountUrl;

    @Async
    public CompletableFuture<Object> getOmsUnSyncedQuantity(Long productId, String legalOwner, Date requestTime, String hubCode) throws Exception {
        AsyncResult<Object> result = new AsyncResult<>("success");
        RestTemplate template = RestUtils.getRestTemplate(Duration.ofMillis(omsConnectionTimeout), Duration.ofMillis(omsReadTimeout));
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> httpEntity = new HttpEntity(httpHeaders);
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(omsBaseUrl + unSyncedCountUrl)
                .queryParam("productId", productId).queryParam("legalOwner", legalOwner)
                .queryParam("hubCode", hubCode).queryParam("requestTime", (requestTime.toInstant().toEpochMilli() * 1000));
        logger.info("[getOmsUnSyncedQuantity] Request for OMS for unsynced count  : " + builder.toUriString());
        ResponseEntity<String> responseEntity = template.exchange(builder.toUriString(), HttpMethod.GET, httpEntity, String.class, new Object[]{null});
        logger.info("[getOmsUnSyncedQuantity] Response for OMS for unsynced count : " + responseEntity.getBody());
        if (responseEntity.getStatusCode() != HttpStatus.OK && responseEntity.getBody() == null) {
            return AsyncResult.forExecutionException(
                    new ApplicationException("error while getOmsUnSyncedQuantity in oms for productId: " + productId, null)
            ).completable();
        } else {
            result = new AsyncResult<>(responseEntity.getBody());
        }
        return result.completable();
    }
}
