package com.lenskart.nexs.cid.controller;

import com.lenskart.nexs.cid.facade.InventoryCheckFacade;
import com.lenskart.nexs.cid.request.InventoryAvailabilityCountRR;
import com.lenskart.nexs.cid.request.StockReleaseWrapper;
import com.lenskart.nexs.cid.response.BufferQuantityResponse;
import com.lenskart.nexs.cid.response.FetchInventoryItem;
import com.lenskart.nexs.cid.response.FetchInventoryResponse;
import com.lenskart.nexs.commons.dto.BaseResponseModel;
import com.lenskart.nexs.cid.constant.CommonConstants;
import com.lenskart.nexs.cid.constant.ControllerUrl;
import com.lenskart.nexs.cid.dto.WarehouseInventoryDto;
import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.cid.response.WarehouseInventoryResponse;
import com.lenskart.nexs.cid.service.WarehouseInventoryService;
import com.lenskart.nexs.commonlogger.annotations.RestLogging;
import com.lenskart.nexs.commons.response.CommonResponseBuilder;
import com.lenskart.nexs.constants.responseMessage.ResponseCodes;
import com.lenskart.nexs.ims.request.InventoryUpdateRequestWrapper;
import com.lenskart.nexs.ims.request.OrderStockCheckRequest;
import com.lenskart.nexs.ims.response.ConsolidatedInvInfo;
import com.lenskart.nexs.ims.response.OrderItemStockCheckResponse;
import com.lenskart.nexs.ims.response.StockCheckResponse;
import io.micrometer.core.annotation.Timed;
import lombok.Setter;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Timed
@RestController
@RequestMapping(ControllerUrl.BASE_URL_V1 + ControllerUrl.WAREHOUSE_INVENTORY)
public class WarehouseInventoryControllerV1 extends WarehouseInventoryController {

    @Setter(onMethod__ = {@Autowired})
    protected InventoryCheckFacade inventoryCheckFacade;

    @RestLogging
    @GetMapping(value = ControllerUrl.FIND_BY_PRODUCT_ID + "/{productId}")
    public ResponseEntity<BaseResponseModel> getWarehouseInventory(@PathVariable(value = "productId") Long productId,
                                                                   @RequestParam Map<String, String> params,
                                                                   @RequestParam(name = "page", defaultValue = "0", required = false) int page,
                                                                   @RequestParam(name = "size", defaultValue = "50", required = false) int size,
                                                                   @RequestParam(name = "sortBy", defaultValue = "id", required = false) String sortBy,
                                                                   @RequestParam(name = "sortOrder", defaultValue = "DESC", required = false) String sortOrder,
                                                                   @RequestParam(name = "legalOwner",defaultValue = "DESC", required = false ) String legalOwner) {
        logger.info("[getWarehouseInventory] fetch warehouse inventory details request {}", params);
        Page<WarehouseInventoryDto> pageWarehouseInventoryDtos = ((WarehouseInventoryService) service)
                .getWarehouseInventory(productId ,params, page, size, sortBy, sortOrder,legalOwner);
        WarehouseInventoryResponse warehouseInventoryResponse = new WarehouseInventoryResponse();
        warehouseInventoryResponse.setWarehouseInventoryDtoList(pageWarehouseInventoryDtos.toList());
        warehouseInventoryResponse.setTotalCount(pageWarehouseInventoryDtos.getTotalElements());
        return CommonResponseBuilder.successResponse(
                warehouseInventoryResponse, CommonConstants.GET_INVENTORY_SUCCESS_MESSAGE, ResponseCodes.RESPONSE_SUCCESS
        );
    }

    @RestLogging
    @GetMapping(value = ControllerUrl.GET_CONSOLIDATED_INVENTORY_INFO)
    public ResponseEntity<BaseResponseModel> getConsolidatedInvInfo(@RequestHeader("facility-code") String facilityCode,
                                                                    @PathVariable("pid") Integer pid,
                                                                    @RequestParam(value = "legalOwner", required = false,defaultValue = "LKIN") String legalOwner) {
        logger.debug("[getConsolidatedInvInfo] get consolidated inventory info for facilityCode: {} and pid: {}", facilityCode, pid);
        ConsolidatedInvInfo consolidatedInvInfo = ((WarehouseInventoryService) service).getConsolidatedInvInfo(facilityCode, pid, legalOwner);
        return CommonResponseBuilder.successResponse(consolidatedInvInfo, CommonConstants.MESSAGE, ResponseCodes.RESPONSE_SUCCESS);
    }

    @RestLogging
    @PostMapping(value = ControllerUrl.UPDATE_CID_INVENTORY_SYNC)
    public ResponseEntity<BaseResponseModel> updateInventory(@Valid @RequestBody InventoryUpdateRequestWrapper  request) throws ApplicationException {
        MDC.put(CommonConstants.USER_ID, request.getUpdatedBy());
        inventoryUpdateFacade.updateInventory(request);
        return CommonResponseBuilder.successResponse(null, CommonConstants.MESSAGE, ResponseCodes.RESPONSE_SUCCESS);
    }

    @RestLogging
    @PostMapping(value = ControllerUrl.GET_INVENTORY_AVAILABILITY)
    public ResponseEntity<BaseResponseModel> getInventoryAvailability(@RequestBody @Valid OrderStockCheckRequest orderStockCheckRequest) throws ApplicationException {
        MDC.put(CommonConstants.USER_ID, orderStockCheckRequest.getUpdatedBy());
        List<OrderItemStockCheckResponse> orderItemStockCheckResponses = new ArrayList<>();
        List<FetchInventoryResponse> fetchInventoryResponseList = inventoryCheckFacade.fetchAvailableInventory(orderStockCheckRequest);
        transformResponse(orderItemStockCheckResponses, fetchInventoryResponseList);
        return CommonResponseBuilder.successResponse(orderItemStockCheckResponses, CommonConstants.MESSAGE, ResponseCodes.RESPONSE_SUCCESS);
    }

    @RestLogging
    @PostMapping(value = ControllerUrl.GET_INVENTORY_AVAILABILITY_COUNT)
    public ResponseEntity<BaseResponseModel> getInventoryAvailabilityCount(@RequestBody @Valid InventoryAvailabilityCountRR inventoryAvailabilityCountRR) throws ApplicationException {
        MDC.put(CommonConstants.USER_ID, inventoryAvailabilityCountRR.getRequestedBy());
        InventoryAvailabilityCountRR inventoryAvailabilityResponse = inventoryCheckFacade.fetchAvailableInventoryCount(inventoryAvailabilityCountRR);
        return CommonResponseBuilder.successResponse(inventoryAvailabilityResponse, CommonConstants.MESSAGE, ResponseCodes.RESPONSE_SUCCESS);
    }

    private void transformResponse(List<OrderItemStockCheckResponse> orderItemStockCheckResponses, List<FetchInventoryResponse> fetchInventoryResponseList) {
        for(FetchInventoryResponse fetchInventoryResponse: fetchInventoryResponseList){
            OrderItemStockCheckResponse orderItemStockCheckResponse = new OrderItemStockCheckResponse();
            orderItemStockCheckResponse.setOrderId(fetchInventoryResponse.getOrderId());
            List<StockCheckResponse> stockCheckResponseList = new ArrayList<>();
            for (FetchInventoryItem fetchInventoryItem : fetchInventoryResponse.getFetchInventoryItemList()) {
                stockCheckResponseList.add(StockCheckResponse.builder()
                        .pid(fetchInventoryItem.getPid())
                        .fulfillable(fetchInventoryItem.isLoyaltyPid() || fetchInventoryItem.getQuantity() >= 0)
                        .httpStatus(HttpStatus.OK)
                        .build());
            }
            orderItemStockCheckResponse.setStockCheckList(stockCheckResponseList);
            orderItemStockCheckResponses.add(orderItemStockCheckResponse);
        }
    }

    @RestLogging
    @PostMapping(value = ControllerUrl.INVENTORY_RELEASE)
    public ResponseEntity<BaseResponseModel> inventoryRelease(@Valid @RequestBody StockReleaseWrapper stockReleaseWrapper) throws ApplicationException {
        MDC.put(CommonConstants.USER_ID, stockReleaseWrapper.getUpdatedBy());
        inventoryUpdateFacade.releaseInventory(stockReleaseWrapper);
        return CommonResponseBuilder.successResponse(null, CommonConstants.MESSAGE, ResponseCodes.RESPONSE_SUCCESS);
    }

    @RestLogging
    @GetMapping(value = ControllerUrl.GET_BUFFER_QTY)
    public ResponseEntity<BaseResponseModel> getBufferQuantity(@PathVariable(value = "productId") Integer productId,
                                                               @PathVariable(value = "hubCode") String hubCode) {
        logger.info("[getBufferedQuantity] fetch buffered quantity for productId: {} and hubCode: {}", productId, hubCode);
        int bufferedQuantity = ((WarehouseInventoryService) service).getBufferedQuantity(productId, hubCode);
        logger.info("[getBufferedQuantity] fetch buffered quantity for productId: {} and hubCode: {} is {}", productId, hubCode, bufferedQuantity);
        BufferQuantityResponse bufferQuantityResponse = new BufferQuantityResponse(Long.valueOf(productId), hubCode, bufferedQuantity);
        return CommonResponseBuilder.successResponse(bufferQuantityResponse, CommonConstants.MESSAGE, ResponseCodes.RESPONSE_SUCCESS);
    }
}
