package com.lenskart.nexs.cid.util;

import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.lenskart.nexs.cid.request.updateinventory.StockAdjustment;
import com.lenskart.nexs.cid.request.updateinventory.StockAdjustmentResponse;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamReader;
import java.io.IOException;
import java.io.StringReader;
import java.io.StringWriter;

public class ConverterUtil {

    private static String STOCK_ADJUSTMENT_RESPONSE = "StockAdjustmentResponse";
    private static String SOAP_ENVELOPE_BODY_PREFIX = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\">\n" +
            "    <soapenv:Body>\n";
    private static String SOAP_ENVELOPE_BODY_SUFFIX = "</soapenv:Body>\n" +
            "</soapenv:Envelope>";
    private static String XML_TAG = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>";
    private static String PRODUCT_ID_ARR_TAG = "<ProductIdArr>";
    private static String PRODUCT_ID_ARR_WITH_ARRAY_TYPE="<ProductIdArr xsi:type=\"SOAP-ENC:Array\" SOAP-ENC:arrayType=\"unnamed_struct_use_soapval[2]\">";

    private ConverterUtil() {}

    public static String convertStockAdjustmentRequestToXml(Object source) {
        String result;
        StringWriter sw = new StringWriter();
        try {
            JAXBContext context = JAXBContext.newInstance(StockAdjustment.class);
            Marshaller marshaller = context.createMarshaller();
            marshaller.marshal(source, sw);
            result = sw.toString();
        } catch (JAXBException e) {
            throw new RuntimeException(e);
        }
        result = result.replace(XML_TAG, "");
        result = SOAP_ENVELOPE_BODY_PREFIX + result + SOAP_ENVELOPE_BODY_SUFFIX;
        result=result.replaceFirst(PRODUCT_ID_ARR_TAG,PRODUCT_ID_ARR_WITH_ARRAY_TYPE);
        return result;
    }

    public static StockAdjustmentResponse convertXmlResponseToStockAdjustmentModel(String response) throws XMLStreamException, IOException {
        XmlMapper xmlMapper = ObjectHelper.getXmlMapper();
        XMLInputFactory f = XMLInputFactory.newFactory();
        XMLStreamReader sr = f.createXMLStreamReader(new StringReader(response));
        while (sr.hasNext()) {
            int type = sr.next();
            if (type == XMLStreamReader.START_ELEMENT && STOCK_ADJUSTMENT_RESPONSE.equals(sr.getLocalName())) {
                StockAdjustmentResponse stockAdjustmentResponse = xmlMapper.readValue(sr, StockAdjustmentResponse.class);
                return stockAdjustmentResponse;
            }
        }
        return null;
    }
}



