package com.lenskart.nexs.cid.producer;

import com.lenskart.nexs.cid.constant.KafkaConstants;
import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.cid.request.updateinventory.ScmInventoryUpdateRequest;
import com.lenskart.nexs.cid.util.ObjectHelper;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.ims.request.InventoryUpdateRequest;
import lombok.Setter;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.internals.RecordHeader;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Component
public class ScmInventoryUpdateProducer {

    @CustomLogger
    private Logger logger;
    @Setter(onMethod__ = {@Autowired})
    private KafkaTemplate<String, String> kafkaTemplate;

    public void sendMessage(ScmInventoryUpdateRequest inventoryUpdateRequest) throws ApplicationException {
        logger.info("publishing scm inventory update event {}", inventoryUpdateRequest);
        try {
            ProducerRecord<String, String> producerRecord = new ProducerRecord<>(
                    KafkaConstants.NEXS_CID_SCM_INVENTORY_SYNC_TOPIC,
                    String.valueOf(inventoryUpdateRequest.getProductId()),
                    ObjectHelper.writeValue(inventoryUpdateRequest)
            );
            String messageIdempotencyKey  = "scm_inv_update_" + UUID.randomUUID();
            producerRecord.headers()
                    .add(KafkaConstants.MESSAGE_IDEMPOTENCY_KEY, messageIdempotencyKey.getBytes());
            kafkaTemplate.send(producerRecord)
                    .get();
        } catch (Exception exception) {
            String message = MessageFormat.format("error while publishing message to topic: {0} , payload: {1} Error: {2}",
                    KafkaConstants.NEXS_CID_SCM_INVENTORY_SYNC_TOPIC, inventoryUpdateRequest, exception.getMessage());
            logger.error(message, exception);
            throw new ApplicationException(message, exception);
        }
    }
}
