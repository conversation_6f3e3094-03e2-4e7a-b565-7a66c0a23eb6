package com.lenskart.nexs.cid.service.impl;

import com.lenskart.nexs.cid.entity.AuditHistoryEntity;
import com.lenskart.nexs.cid.repository.AuditHistoryRepository;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AuditHistoryService {

    @CustomLogger
    private Logger logger;

    @Setter(onMethod__ = {@Autowired})
    private AuditHistoryRepository auditHistoryRepository;

    public void addDocument(AuditHistoryEntity auditHistoryEntity) {
        logger.info("Adding Elastic document for Audit History {}", auditHistoryEntity);
        auditHistoryRepository.saveWithoutRefresh(auditHistoryEntity);
    }

}
