package com.lenskart.nexs.cid.producer;

import com.lenskart.nexs.cid.constant.KafkaConstants;
import com.lenskart.nexs.cid.entity.AuditHistoryEntity;
import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.cid.util.ObjectHelper;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;

@Component
public class AuditHistoryProducer {

    @CustomLogger
    private Logger logger;

    @Setter(onMethod__ = {@Autowired})
    private KafkaTemplate<String, String> kafkaTemplate;

    public void sendMessage(AuditHistoryEntity historyDocument) throws ApplicationException {
        logger.info("publishing inventory update event {}", historyDocument);
        try {
            //kafkaTemplate.send(KafkaConstants.NEXS_CID_HISTORY_SYNC_TOPIC, ObjectHelper.writeValue(historyDocument));
        } catch (Exception exception) {
            String message = MessageFormat.format("error while publishing message to topic: {0} , payload: {1} Error: {2}",
                    KafkaConstants.NEXS_CID_HISTORY_SYNC_TOPIC, historyDocument, exception.getMessage());
            logger.error(message, exception);
            throw new ApplicationException(message, exception);
        }
    }
}
