package com.lenskart.nexs.cid.service.impl;

import com.lenskart.nexs.cid.dto.WarehouseBlockedInventoryDto;
import com.lenskart.nexs.cid.dto.WarehouseBlockedOrderItemDto;
import com.lenskart.nexs.cid.entity.WarehouseBlockedInventory;
import com.lenskart.nexs.cid.entity.WarehouseBlockedOrderItem;
import com.lenskart.nexs.cid.service.WarehouseBlockedOrderItemService;
import com.lenskart.nexs.cid.service.WarehouseBlockedInventoryService;
import com.lenskart.nexs.commons.service.impl.BaseServiceImp;
import lombok.Setter;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class WarehouseBlockedOrderItemServiceImpl extends BaseServiceImp<WarehouseBlockedOrderItemDto, WarehouseBlockedOrderItem> implements WarehouseBlockedOrderItemService {

    @Setter(onMethod__ = {@Autowired})
    private WarehouseBlockedInventoryService warehouseBlockedInventoryService;

    public Map<Integer, WarehouseBlockedOrderItemDto> getExistingWarehouseBlockedInventoryMapForRequest(Integer orderId) {
        Map<Integer, WarehouseBlockedOrderItemDto> warehouseBlockedOrderItemMap = new HashMap<>();
        List<WarehouseBlockedOrderItemDto> warehouseBlockedOrderItemDtoList = search("orderId.eq:" + orderId);

        for (WarehouseBlockedOrderItemDto warehouseBlockedOrderItemDto : warehouseBlockedOrderItemDtoList) {
            warehouseBlockedOrderItemMap.put(Math.toIntExact(warehouseBlockedOrderItemDto.getOrderItemId()), warehouseBlockedOrderItemDto);
        }
        return warehouseBlockedOrderItemMap;
    }

    public WarehouseBlockedOrderItem convertToEntity(WarehouseBlockedOrderItemDto dto, WarehouseBlockedOrderItem entity){
        if (dto == null) {
            return null;
        } else {
            BeanUtils.copyProperties(dto, entity, getNullPropertyNames(dto));
            WarehouseBlockedInventory warehouseBlockedInventory = getWarehouseBlockedInventory(dto.getWarehouseBlockedInventoryDto());
            entity.setWarehouseBlockedInventory(warehouseBlockedInventory);
            return entity;
        }
    }

    public WarehouseBlockedOrderItemDto convertToDto(WarehouseBlockedOrderItem entity, WarehouseBlockedOrderItemDto dto) {
        if (entity == null) {
            return null;
        } else {
            BeanUtils.copyProperties(entity, dto);
            WarehouseBlockedInventoryDto warehouseBlockedInventoryDto = getWarehouseBlockedInventoryDto(entity.getWarehouseBlockedInventory());
            dto.setWarehouseBlockedInventoryDto(warehouseBlockedInventoryDto);
            return dto;
        }
    }

    private WarehouseBlockedInventoryDto getWarehouseBlockedInventoryDto(WarehouseBlockedInventory entity) {
        WarehouseBlockedInventoryDto warehouseBlockedInventoryDto = new WarehouseBlockedInventoryDto();
        warehouseBlockedInventoryService.convertToDto(entity, warehouseBlockedInventoryDto);
        return warehouseBlockedInventoryDto;
    }

    private WarehouseBlockedInventory getWarehouseBlockedInventory(WarehouseBlockedInventoryDto warehouseBlockedInventoryDto) {
        WarehouseBlockedInventory warehouseBlockedInventory = new WarehouseBlockedInventory();
        warehouseBlockedInventoryService.convertToEntity(warehouseBlockedInventoryDto, warehouseBlockedInventory);
        return warehouseBlockedInventory;
    }

    @Override
    public Map<Integer, WarehouseBlockedOrderItemDto> getExistingWarehouseBlockedInventoryMapForRequestWithLegalOwner(Integer orderId, String legalOwner) {
        Map<Integer, WarehouseBlockedOrderItemDto> warehouseBlockedOrderItemMap = new HashMap<>();
        List<WarehouseBlockedOrderItemDto> warehouseBlockedOrderItemDtoList = search("orderId.eq:" + orderId + "___legalOwner.eq:" + legalOwner);
        return warehouseBlockedOrderItemDtoList.stream().collect(Collectors.toMap(
                warehouseBlockedOrderItemDto-> Math.toIntExact(warehouseBlockedOrderItemDto.getOrderItemId()), Function.identity()
        ));
    }
}