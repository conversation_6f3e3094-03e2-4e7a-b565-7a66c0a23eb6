package com.lenskart.nexs.cid.service;

import com.lenskart.nexs.cid.dto.WarehouseBlockedInventoryDto;
import com.lenskart.nexs.cid.entity.WarehouseBlockedInventory;
import com.lenskart.nexs.commons.service.BaseService;
import org.springframework.transaction.annotation.Transactional;

@Transactional(readOnly = true)
public interface WarehouseBlockedInventoryService extends BaseService<WarehouseBlockedInventoryDto, WarehouseBlockedInventory> {
    WarehouseBlockedInventoryDto findByProductIdAndFacilityAndLegalOwner(Long productId,String facility,String legalOwner);
}
