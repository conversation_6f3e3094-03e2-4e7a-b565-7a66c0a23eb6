package com.lenskart.nexs.cid.exception;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class UffException extends Exception {
    private String errorMessage;
    private Throwable cause;

    public UffException(String errorMessage) {
        super(errorMessage);
        this.errorMessage = errorMessage;
    }

    public UffException(String errorMessage, Throwable cause) {
        super(errorMessage, cause);
        this.errorMessage = errorMessage;
        this.cause = cause;
    }
}
