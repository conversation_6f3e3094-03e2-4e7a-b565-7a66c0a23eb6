package com.lenskart.nexs.cid.strategy.impl;

import com.lenskart.nexs.cid.constant.CommonConstants;
import com.lenskart.nexs.cid.dto.WarehouseBlockedInventoryDto;
import com.lenskart.nexs.cid.dto.WarehouseInventoryDto;
import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.cid.response.WarehouseInventoryCountResponse;
import com.lenskart.nexs.cid.response.WarehouseInventoryResponse;
import com.lenskart.nexs.cid.strategy.BaseInventoryUpdateStrategy;
import com.lenskart.nexs.ims.enums.CidInventoryOperation;
import com.lenskart.nexs.ims.request.InventoryUpdateRequest;
import io.netty.util.internal.StringUtil;
import lombok.AccessLevel;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class BlockInventoryUpdateStrategy extends BaseInventoryUpdateStrategy {

    @Override
    public CidInventoryOperation supportedInventoryOperation() {
        return CidInventoryOperation.INV_BLOCK;
    }

    @Override
    public void execute(InventoryUpdateRequest request) throws ApplicationException {
        logger.info("[BlockInventoryUpdateStrategy] processing inventory for operation {} with request {}", CidInventoryOperation.INV_BLOCK, request);
        WarehouseInventoryCountResponse warehouseInventoryCountResponse = getTotalWarehouseInventory(request);
        logger.info("[BlockInventoryUpdateStrategy] totalWhInventoryCount is {}", warehouseInventoryCountResponse.getTotalWhInventoryCount());
        updateStorefrontInventory(request, warehouseInventoryCountResponse.getTotalWhInventoryCount(), warehouseInventoryCountResponse.getBufferedWhInventoryCount());
        sendInventoryUpdateToSCM(request, warehouseInventoryCountResponse.getTotalWhInventoryCount(), warehouseInventoryCountResponse.getBufferedWhInventoryCount());
        sendInventoryUpdateToJuno(request);
        WarehouseInventoryDto persistedWhInvDto = warehouseInventoryService.findByProductIdFacilityConditionAvailabilityAndStatusAndLegalOwnerAndLocationType(
                request.getProductId().longValue(),
                request.getFacility(),
                request.getCondition(),
                request.getAvailability(),
                request.getStatus(),
                request.getLegalOwner(),
                request.getLocationType()
        );
        updateBlockedInventoryDtoAndSendEvent(request, persistedWhInvDto);
    }

}
