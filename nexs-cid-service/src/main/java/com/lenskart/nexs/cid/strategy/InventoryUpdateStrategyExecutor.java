package com.lenskart.nexs.cid.strategy;

import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.ims.enums.CidInventoryOperation;
import com.lenskart.nexs.ims.request.InventoryUpdateRequest;
import lombok.AllArgsConstructor;

import java.util.Map;

@AllArgsConstructor
public class InventoryUpdateStrategyExecutor {

    private final Map<CidInventoryOperation, BaseInventoryUpdateStrategy> executors;

    public void doExecute(InventoryUpdateRequest request) throws ApplicationException {
        if (!executors.containsKey(request.getCidInventoryOperation())) {
            throw new ApplicationException("invalid CID inventory operation:" + request.getCidInventoryOperation());
        }
        executors.get(request.getCidInventoryOperation())
                .doExecute(request);
    }

}
