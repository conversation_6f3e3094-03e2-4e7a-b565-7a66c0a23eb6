package com.lenskart.nexs.cid.strategy;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

@Configuration
public class InventoryUpdateStrategyExecutorConfiguration {

    @Bean
    public InventoryUpdateStrategyExecutor inventoryUpdateStrategyExecutor(List<BaseInventoryUpdateStrategy> updateStrategies) {
        return new InventoryUpdateStrategyExecutor(
                updateStrategies.stream()
                        .collect(Collectors.toMap(BaseInventoryUpdateStrategy::supportedInventoryOperation, Function.identity()))
        );
    }

}
