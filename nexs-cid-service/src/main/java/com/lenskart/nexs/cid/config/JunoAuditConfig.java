package com.lenskart.nexs.cid.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration class for Juno audit functionality
 */
@Configuration
@ConditionalOnProperty(name = "juno.audit.enabled", havingValue = "true", matchIfMissing = true)
public class JunoAuditConfig {
    
    // This configuration class ensures that Juno audit functionality is enabled by default
    // but can be disabled by setting juno.audit.enabled=false in application properties
    
}
