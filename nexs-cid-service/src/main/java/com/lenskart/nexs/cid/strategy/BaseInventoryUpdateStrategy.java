package com.lenskart.nexs.cid.strategy;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lenskart.nexs.cid.constant.CommonConstants;
import com.lenskart.nexs.cid.constant.KafkaConstants;
import com.lenskart.nexs.cid.dto.StorefrontInventoryDto;
import com.lenskart.nexs.cid.dto.WarehouseBlockedInventoryDto;
import com.lenskart.nexs.cid.dto.WarehouseInventoryDto;
import com.lenskart.nexs.cid.entity.AuditHistoryEntity;
import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.cid.facade.InventoryUpdateFacade;
import com.lenskart.nexs.cid.mapper.InvUpdateRequestToWhInvDtoMapper;
import com.lenskart.nexs.cid.producer.AuditHistoryProducer;
import com.lenskart.nexs.cid.producer.EMSInventoryEventProducer;
import com.lenskart.nexs.cid.producer.JunoInventoryUpdateProducer;
import com.lenskart.nexs.cid.producer.ScmInventoryUpdateProducer;
import com.lenskart.nexs.cid.request.updateinventory.ScmInventoryUpdateRequest;
import com.lenskart.nexs.cid.response.WarehouseInventoryCountResponse;
import com.lenskart.nexs.cid.service.WarehouseBlockedInventoryService;
import com.lenskart.nexs.cid.service.StorefrontInventoryService;
import com.lenskart.nexs.cid.service.WarehouseInventoryService;
import com.lenskart.nexs.cid.util.ObjectHelper;
import com.lenskart.nexs.cid.validator.InventoryUpdateValidator;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.ems.enums.ExceptionType;
import com.lenskart.nexs.ems.enums.Source;
import com.lenskart.nexs.ems.model.EmsExceptionEvent;
import com.lenskart.nexs.ems.model.request.EmsInventoryUpdateRequest;
import com.lenskart.nexs.ims.enums.CidInventoryOperation;
import com.lenskart.nexs.ims.enums.LocationType;
import com.lenskart.nexs.ims.model.Availability;
import com.lenskart.nexs.ims.model.Condition;
import com.lenskart.nexs.ims.model.Status;
import com.lenskart.nexs.ims.request.InventoryUpdateRequest;
import io.micrometer.core.instrument.Counter;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Logger;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.cloud.sleuth.Span;
import org.springframework.cloud.sleuth.Tracer;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Date;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Setter(onMethod__ = {@Autowired})
public abstract class BaseInventoryUpdateStrategy {


    private static final DateFormat DATE_FORMAT = new SimpleDateFormat(CommonConstants.DATE_PATTERN);

    @CustomLogger
    @Setter(AccessLevel.NONE)
    protected Logger logger;
    @Setter(AccessLevel.NONE)
    private Pattern pattern;

    @Value("#{'${nexs.available.facilities}'.split(',')}")
    @Setter(AccessLevel.NONE)
    private List<String> nexsFacilities;

    @Value("#{'${block.juno.inventory.update.facilities}'.split(',')}")
    @Setter(AccessLevel.NONE)
    private List<String> blockJunoInventoryUpdateFacilities;

    @Value("${legalOwner.defaultLegalOnwner}")
    @Setter(AccessLevel.NONE)
    private String defaultLegalOnwner;

    @Value("${is.uff.tabulation.allowed}")
    @Setter(AccessLevel.NONE)
    private boolean isUffTabulationAllowed;

    @Value("#{'${block.manesar.juno.inventory.update}'.split(',')}")
    @Setter(AccessLevel.NONE)
    private List<String> blockManesarJunoUpdate;

    protected AuditHistoryProducer auditHistoryProducer;
    protected InventoryUpdateValidator inventoryUpdateValidator;
    protected InvUpdateRequestToWhInvDtoMapper invUpdateRequestToWhInvDtoMapper;
    protected WarehouseInventoryService warehouseInventoryService;
    protected StorefrontInventoryService storefrontInventoryService;
    protected WarehouseBlockedInventoryService warehouseBlockedInventoryService;
    protected Tracer tracer;
    private EMSInventoryEventProducer emsInventoryEventProducer;
    protected ScmInventoryUpdateProducer scmInventoryUpdateProducer;
    protected JunoInventoryUpdateProducer junoInventoryUpdateProducer;
    protected InventoryUpdateFacade inventoryUpdateFacade;


    protected abstract CidInventoryOperation supportedInventoryOperation();

    protected abstract void execute(InventoryUpdateRequest inventoryUpdateRequest) throws ApplicationException;

    protected void doExecute(InventoryUpdateRequest request) throws ApplicationException {
        logger.info("inside {} update strategy for {}", supportedInventoryOperation(), request);
        inventoryUpdateValidator.validate(request);
        execute(request);
    }

    protected WarehouseInventoryDto updateWarehouseInventory(WarehouseInventoryDto persistedWhInvDto,
                                                             InventoryUpdateRequest request, Integer quantity) throws ApplicationException {
        WarehouseInventoryDto requestWhInvDto = invUpdateRequestToWhInvDtoMapper.map(request);
        if (persistedWhInvDto == null) {
            persistedWhInvDto = warehouseInventoryService.findByProductIdFacilityConditionAvailabilityAndStatusAndLegalOwnerAndLocationType(
                    requestWhInvDto.getProductId(),
                    requestWhInvDto.getFacility(),
                    requestWhInvDto.getCondition(),
                    requestWhInvDto.getAvailability(),
                    requestWhInvDto.getStatus(),
                    request.getLegalOwner(),
                    request.getLocationType()
            );
        }
        String oldDataJson = null;
        if (Objects.isNull(persistedWhInvDto)) {
            if (requestWhInvDto.getUpdatedBy() == null) {
                requestWhInvDto.setCreatedBy(MDC.get(CommonConstants.USER_ID));
                requestWhInvDto.setUpdatedBy(MDC.get(CommonConstants.USER_ID));
            } else {
                requestWhInvDto.setCreatedBy(requestWhInvDto.getUpdatedBy());
            }
            if (Objects.nonNull(request.getReconciliationEventTime())) {
                requestWhInvDto.setReconciliationEventTime(request.getReconciliationEventTime());
            }
            requestWhInvDto.setQuantity(quantity);
            logger.info("Before saving warehouse inventory {}", requestWhInvDto);
            persistedWhInvDto = warehouseInventoryService.save(requestWhInvDto);
        } else {
            persistedWhInvDto.setLegalOwner(request.getLegalOwner());
            oldDataJson = ObjectHelper.writeValue(persistedWhInvDto);
            persistedWhInvDto.setUpdatedBy(MDC.get(CommonConstants.USER_ID));
            if (Objects.nonNull(request.getReconciliationEventTime())) {
                persistedWhInvDto.setReconciliationEventTime(request.getReconciliationEventTime());
            }
            persistedWhInvDto.setQuantity(quantity);
            persistedWhInvDto = warehouseInventoryService.update(persistedWhInvDto, persistedWhInvDto.getId());
        }
        AuditHistoryEntity auditHistoryEntity = getAuditHistoryEntity(persistedWhInvDto, request, oldDataJson);
        auditHistoryProducer.sendMessage(auditHistoryEntity);
        return persistedWhInvDto;
    }

    private AuditHistoryEntity getAuditHistoryEntity(WarehouseInventoryDto persistedWhInvDto, InventoryUpdateRequest request, String oldDataJson) throws ApplicationException {
        AuditHistoryEntity auditHistoryEntity = new AuditHistoryEntity();
        try {
            String requestDataJson = ObjectHelper.writeValue(request);
            String newDataJson = ObjectHelper.writeValue(persistedWhInvDto);
            Span span = tracer.currentSpan();
            auditHistoryEntity.setEntityName(CommonConstants.WAREHOUSE_INVENTORY);
            auditHistoryEntity.setRequestDataJson(requestDataJson);
            auditHistoryEntity.setOperation(String.valueOf(request.getCidInventoryOperation()));
            auditHistoryEntity.setEventTime(DATE_FORMAT.format(new Date()));
            auditHistoryEntity.setOldDataJson(oldDataJson);
            auditHistoryEntity.setNewDataJson(newDataJson);
            auditHistoryEntity.setEntityId(persistedWhInvDto.getId());
            auditHistoryEntity.setTraceId(span.context().traceId());
        } catch (Exception exception) {
            logger.error("Error during setting auditHistoryEntity");
            throw new ApplicationException("Issue while setting auditHistoryEntity", exception);
        }
        return auditHistoryEntity;
    }

    protected WarehouseInventoryCountResponse getTotalWarehouseInventory(InventoryUpdateRequest request) {
        StringBuilder searchTermsBuilder = new StringBuilder("productId.eq:")
                .append(request.getProductId())
                .append("___condition.eq:")
                .append(request.getCondition())
                .append("___availability.eq:")
                .append(request.getAvailability())
                .append("___status.eq:")
                .append(request.getStatus())
                .append("___legalOwner.eq:")
                .append(request.getLegalOwner())
                .append("___locationType.eq:")
                .append(request.getLocationType())
                .append("___facility.in:")
                .append(String.join(",", blockJunoInventoryUpdateFacilities));
        logger.info("[getTotalWarehouseInventory] searchTerm : {}", searchTermsBuilder);

        String searchTerms = searchTermsBuilder.toString();
        List<WarehouseInventoryDto> warehouseInventoryDtos = warehouseInventoryService.search(searchTerms);
        logger.debug("[getTotalWarehouseInventory] warehouseInventoryDtos {}", warehouseInventoryDtos);
        int totalWarehouseInventory = 0;
        int bufferedQuantity = 0;
        if (!CollectionUtils.isEmpty(warehouseInventoryDtos)) {
            totalWarehouseInventory = warehouseInventoryDtos.stream()
                    .map(WarehouseInventoryDto::getQuantity)
                    .reduce(Integer::sum)
                    .orElse(0);
        }
        if (!CollectionUtils.isEmpty(warehouseInventoryDtos)) {
            bufferedQuantity = warehouseInventoryDtos.stream()
                    .map(WarehouseInventoryDto::getBufferedQuantity)
                    .reduce(Integer::sum)
                    .orElse(0);
        }
        return new WarehouseInventoryCountResponse(totalWarehouseInventory, bufferedQuantity);
    }

    protected void updateStorefrontInventory(InventoryUpdateRequest inventoryUpdateRequest, Integer totalWhInventoryCount, Integer bufferedQuantity) {
        StorefrontInventoryDto storefrontInventoryDto = storefrontInventoryService.findByProductIdAndLegalOwner(inventoryUpdateRequest.getProductId().longValue(), inventoryUpdateRequest.getLegalOwner());
        if (storefrontInventoryDto == null) {
            storefrontInventoryDto = new StorefrontInventoryDto();
            storefrontInventoryDto.setProductId(inventoryUpdateRequest.getProductId().longValue());
            storefrontInventoryDto.setCondition(Condition.GOOD);
            storefrontInventoryDto.setAvailability(Availability.AVAILABLE);
            storefrontInventoryDto.setStatus(Status.AVAILABLE);
            storefrontInventoryDto.setQuantity(totalWhInventoryCount);
            storefrontInventoryDto.setBufferedQuantity(bufferedQuantity);
            storefrontInventoryDto.setCreatedBy(inventoryUpdateRequest.getUpdatedBy());
            storefrontInventoryDto.setUpdatedBy(inventoryUpdateRequest.getUpdatedBy());
            storefrontInventoryDto.setLegalOwner(inventoryUpdateRequest.getLegalOwner());

            logger.debug("[updateStorefrontInventory] going to update store front inventory {}", storefrontInventoryDto);
            storefrontInventoryService.save(storefrontInventoryDto);
        } else {
            storefrontInventoryDto.setQuantity(totalWhInventoryCount);
            storefrontInventoryDto.setBufferedQuantity(bufferedQuantity);
            storefrontInventoryDto.setCreatedBy(inventoryUpdateRequest.getUpdatedBy());
            storefrontInventoryDto.setLegalOwner(inventoryUpdateRequest.getLegalOwner());

            logger.debug("[updateStorefrontInventory] going to update store front inventory {}", storefrontInventoryDto);
            storefrontInventoryService.update(storefrontInventoryDto, storefrontInventoryDto.getId());
        }
    }

    protected void sendInventoryUpdateToSCM(InventoryUpdateRequest request, Integer totalWhInventoryCount, Integer bufferedQuantity) throws ApplicationException {
        logger.debug("[sendInventoryUpdateToSCM] checking if allowedInventorySyncToScm for request {}", request);
        if (allowedInventorySyncToScm(request)) {
            List<WarehouseBlockedInventoryDto> filteredWarehouseBlockedInventoryDtos = warehouseBlockedInventoryService.search(
                    "productId.eq:" + request.getProductId() +
                            "___facility.in:" + String.join(",", blockJunoInventoryUpdateFacilities) +
                            "___legalOwner.eq:" + request.getLegalOwner()
            );
            int blockedCount = 0;
            if (!CollectionUtils.isEmpty(filteredWarehouseBlockedInventoryDtos)) {
                blockedCount = filteredWarehouseBlockedInventoryDtos.stream()
                        .map(WarehouseBlockedInventoryDto::getQuantity)
                        .filter(Objects::nonNull)
                        .reduce(0, Integer::sum);
            }
            int sellableInventory = totalWhInventoryCount - blockedCount - bufferedQuantity;
            ScmInventoryUpdateRequest scmInventoryUpdateRequest = new ScmInventoryUpdateRequest(
                    request.getProductId(),
                    sellableInventory,
                    request.getEventTime(),
                    request.getLegalOwner()
            );
            logger.debug("[sendInventoryUpdateToSCM] going to push msg {} to topic {}", scmInventoryUpdateRequest, KafkaConstants.NEXS_CID_SCM_INVENTORY_SYNC_TOPIC);
            scmInventoryUpdateProducer.sendMessage(scmInventoryUpdateRequest);
        }
    }

    protected boolean allowedInventorySyncToScm(InventoryUpdateRequest inventoryUpdateRequest) {
        return isGoodAvailableAvailableInventoryUpdateEvent(inventoryUpdateRequest) && inventoryUpdateRequest.getLegalOwner().equals(defaultLegalOnwner) && blockJunoInventoryUpdateFacilities.contains(inventoryUpdateRequest.getFacility());
    }

    protected boolean isGoodAvailableAvailableInventoryUpdateEvent(InventoryUpdateRequest inventoryUpdateRequest) {
        return Condition.GOOD.equals(inventoryUpdateRequest.getCondition())
                && Availability.AVAILABLE.equals(inventoryUpdateRequest.getAvailability())
                && Status.AVAILABLE.equals(inventoryUpdateRequest.getStatus())
                && LocationType.DEFAULT.equals(inventoryUpdateRequest.getLocationType());
    }

    protected boolean isStaleEvent(InventoryUpdateRequest request, WarehouseInventoryDto persistedWhInvDto, Counter errorCounter) throws ApplicationException {
        if (!request.getUpdatedBy().equalsIgnoreCase(CommonConstants.SCRIPT_USER)) {
            if (Objects.nonNull(persistedWhInvDto) && Objects.nonNull(persistedWhInvDto.getReconciliationEventTime())
                    && request.getEventTime().before(persistedWhInvDto.getReconciliationEventTime())) {
                errorCounter.increment();
                logger.error("skipping inventory update due to stale event. request event time: {} last event time: {} request: {}",
                        request.getEventTime(), persistedWhInvDto.getReconciliationEventTime(), request);
                return true;
            }
        } else if (CommonConstants.SCRIPT_USER.equalsIgnoreCase(request.getUpdatedBy())
                && Objects.nonNull(persistedWhInvDto) && Objects.nonNull(persistedWhInvDto.getUpdatedAt())
                && request.getReconciliationEventTime().before(persistedWhInvDto.getUpdatedAt())
        ) {
            errorCounter.increment();
            logger.error("skipping script inventory update due to stale event. request event time: {} last event time: {} request: {}",
                    request.getReconciliationEventTime(), persistedWhInvDto.getUpdatedAt(), request);
            throw new ApplicationException("stale script inventory update for pid: " + request.getProductId() + " facility: " + request.getFacility());
        }
        return false;
    }

    protected void triggerEmsInventoryUpdateIfRequired(WarehouseInventoryDto persistedWhInvDto) throws ApplicationException {
        try {
            if (nexsFacilities.contains(persistedWhInvDto.getFacility())
                    && persistedWhInvDto.getQuantity() <= 0
                    && nonPLPid(persistedWhInvDto.getProductId())
                    && anyItemBlockedForPidAndFacility(persistedWhInvDto.getProductId(), persistedWhInvDto.getFacility(), persistedWhInvDto.getLegalOwner())
            ) {
                logger.info("[triggerEmsInventoryUpdateIfRequired] Going to push EMS inventory UFF event for {}", persistedWhInvDto);
                EmsExceptionEvent emsEventPayload = populateEmsEventPayload(persistedWhInvDto);
                publishInventoryEventToEMS(emsEventPayload, persistedWhInvDto.getProductId());
                logger.info("[triggerEmsInventoryUpdateIfRequired] Successfully published inventory UFF event to EMS {}", emsEventPayload);
            }
        } catch (Exception exception) {
            logger.error("[triggerEmsInventoryUpdateIfRequired] publish inventory UFF event to EMS with payload {} failed with exception " + exception, persistedWhInvDto);
            throw new ApplicationException(exception.getMessage());
        }
    }

    private boolean anyItemBlockedForPidAndFacility(Long productId, String facility, String legalOwner) {
        WarehouseBlockedInventoryDto warehouseBlockedInventoryDto = warehouseBlockedInventoryService.findByProductIdAndFacilityAndLegalOwner(productId, facility, legalOwner);
        if (Objects.nonNull(warehouseBlockedInventoryDto)) {
            return warehouseBlockedInventoryDto.getQuantity() > 0;
        }

        return false;
    }

    private void publishInventoryEventToEMS(EmsExceptionEvent emsEventPayload, Long productId) throws ApplicationException {
        emsInventoryEventProducer.sendMessage(emsEventPayload, productId);
    }

    private EmsExceptionEvent populateEmsEventPayload(WarehouseInventoryDto persistedWhInvDto) throws JsonProcessingException {
        EmsInventoryUpdateRequest emsInventoryUpdateRequest = new EmsInventoryUpdateRequest();
        emsInventoryUpdateRequest.setPid(persistedWhInvDto.getProductId());
        emsInventoryUpdateRequest.setFacility(persistedWhInvDto.getFacility());
        emsInventoryUpdateRequest.setLegalOwner(null);
        emsInventoryUpdateRequest.setAvailableQuantity(persistedWhInvDto.getQuantity());
        emsInventoryUpdateRequest.setBlockedQuantity(0);
        emsInventoryUpdateRequest.setRequestedAt(new Date());

        EmsExceptionEvent emsEventPayload = new EmsExceptionEvent();
        emsEventPayload.setMeta(ObjectHelper.getObjectMapper().writeValueAsString(emsInventoryUpdateRequest));
        emsEventPayload.setExceptionType(ExceptionType.CID_UFF_PID);
        emsEventPayload.setSource(Source.CID);
        emsEventPayload.setCreatedAt(new Date());

        return emsEventPayload;
    }

    private boolean nonPLPid(Long productId) {
        if (productId.toString().length() < 8
                || (productId > 90000000 && productId < 99999999)
        ) {
            return true;
        }

        return false;
    }

    protected void sendInventoryUpdateToJuno(InventoryUpdateRequest request) {
        try {
            logger.info("[sendInventoryUpdateToJuno] disabled facilities are {} and request facility is {}", blockJunoInventoryUpdateFacilities, request.getFacility());
            if (!StringUtils.isEmpty(request.getFacility()) && !(blockJunoInventoryUpdateFacilities.contains(request.getFacility()) || blockManesarJunoUpdate.contains(request.getFacility()))) {
                logger.info("[sendInventoryUpdateToJuno] is enabled for facility {}", request.getFacility());
                junoInventoryUpdateProducer.sendMessage(request);
            }
        } catch (Exception e) {
            logger.error("Issue while [sendInventoryUpdateToJuno] " + e);
            //throw new ApplicationException("Issue while sending Juno payload" + e);
        }
    }

    protected void updateBlockedInventoryDtoAndSendEvent(InventoryUpdateRequest request, WarehouseInventoryDto persistedWhInvDto) {
            if(isUffTabulationAllowed) {
                logger.info("[{}, updateBlockedInventoryDtoAndSendEvent] the persistedWhInventory is {} for request {}", this.getClass().getSimpleName(),
                        persistedWhInvDto, request);
                WarehouseBlockedInventoryDto warehouseBlockedInventoryDto = warehouseBlockedInventoryService
                        .findByProductIdAndFacilityAndLegalOwner(request.getProductId().longValue(), request.getFacility(), request.getLegalOwner());
                logger.info("[{}, updateBlockedInventoryDtoAndSendEvent] the warehouseblocked inventoryDTO is {} for request {}", this.getClass().getSimpleName(),
                        warehouseBlockedInventoryDto, request);
                int existingUnfulfillabe;
                if (!Objects.isNull(warehouseBlockedInventoryDto) && !request.isUffUpdateNotRequired()) {
                    logger.info("[{}, updateBlockedInventoryDtoAndSendEvent] For the request {} the warehouseBlockedInventoryDto is {}",
                            this.getClass().getSimpleName(), request, warehouseBlockedInventoryDto);
                    existingUnfulfillabe = Objects.isNull(warehouseBlockedInventoryDto.getUnfulfillableQty()) ? 0 : warehouseBlockedInventoryDto.getUnfulfillableQty();
                    int totalGAA = null == persistedWhInvDto ? 0 : persistedWhInvDto.getQuantity();
                    int newUffQty = Math.max(0, warehouseBlockedInventoryDto.getQuantity() - totalGAA);
                    warehouseBlockedInventoryDto.setUnfulfillableQty(newUffQty);
                    warehouseBlockedInventoryService.update(warehouseBlockedInventoryDto, warehouseBlockedInventoryDto.getId());
                    logger.info("[{}, updateBlockedInventoryDtoAndSendEvent] For the request {} the existing uff quantity is {} and the uff quantity is {}", this.getClass().getSimpleName(), request, existingUnfulfillabe, warehouseBlockedInventoryDto.getQuantity());
                    if (existingUnfulfillabe != newUffQty) {
                        String eventType = existingUnfulfillabe < newUffQty ? CommonConstants.INCREMENT : CommonConstants.DECREMENT;
                        String legalOwner = null == persistedWhInvDto ? warehouseBlockedInventoryDto.getLegalOwner() : persistedWhInvDto.getLegalOwner();
                        inventoryUpdateFacade.sendFulfillablityEventToEms(warehouseBlockedInventoryDto, legalOwner, existingUnfulfillabe, eventType);
                    }
                }
            }
    }
}
