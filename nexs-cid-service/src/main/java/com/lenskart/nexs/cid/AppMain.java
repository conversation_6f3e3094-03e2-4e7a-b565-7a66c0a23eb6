package com.lenskart.nexs.cid;

import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;


@SpringBootApplication(scanBasePackages = {"com.lenskart.nexs.*"})
@EnableJpaRepositories(basePackages = "com.lenskart.nexs.cid.*")
@EnableTransactionManagement
@EnableRetry
@EnableScheduling
@EnableSchedulerLock(defaultLockAtMostFor = "PT30S")
public class AppMain {

    public static void main(String[] args) {
        SpringApplication.run(AppMain.class, args);
        System.out.println("=============== CID Application Started ====================");
    }
}

