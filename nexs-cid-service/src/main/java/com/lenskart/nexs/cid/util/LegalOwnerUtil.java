package com.lenskart.nexs.cid.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class LegalOwnerUtil {

    @Value("${legal_owner.facility.mapping}")
    private String legalOwnerFacilityJson;

    @Value("${facility.legal_owner.mapping}")
    private String facilityLegalOwnerJson;

    @Value("${facility.hub_code.mapping}")
    private String facilityHubCodeJson;

    private static Map<String, List<String>> legalOwnerFacilityMap;
    private static Map<String, String> facilityLegalOwnerMap;

    private static Map<String, String> facilityHubCodeMap;

    private static final Logger logger = LoggerFactory.getLogger(ObjectHelper.class);

    @PostConstruct
    private void init() {
        try {
            logger.info("legalOwnerFacilityJson value {}", legalOwnerFacilityJson);
            legalOwnerFacilityMap = ObjectHelper.getObjectMapper().readValue(legalOwnerFacilityJson, new TypeReference<HashMap<String, List<String>>>(){});

            logger.info("facilityLegalOwnerJson value {}", facilityLegalOwnerJson);
            facilityLegalOwnerMap = ObjectHelper.getObjectMapper().readValue(facilityLegalOwnerJson, new TypeReference<HashMap<String, String>>(){});
            facilityHubCodeMap = ObjectHelper.getObjectMapper().readValue(facilityHubCodeJson, new TypeReference<HashMap<String, String>>(){});
        } catch (JsonMappingException e) {
            throw new RuntimeException(e);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static Map<String, List<String>> getLegalOwnerFacilityMap() {
        return legalOwnerFacilityMap;
    }

    public static Map<String, String> getFacilityLegalOwnerMap() {
        return facilityLegalOwnerMap;
    }

    public static Map<String, String> getFacilityHubCodeMap() {
        return facilityHubCodeMap;
    }


}
