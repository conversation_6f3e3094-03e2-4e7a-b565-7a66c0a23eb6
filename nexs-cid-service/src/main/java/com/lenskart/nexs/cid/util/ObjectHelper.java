package com.lenskart.nexs.cid.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.module.jaxb.JaxbAnnotationModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

public class ObjectHelper {
    private static final Logger logger = LoggerFactory.getLogger(ObjectHelper.class);

    private static ObjectMapper objectMapper = new ObjectMapper();

    private static ObjectMapper objectMapperNonNull = new ObjectMapper();

    private static XmlMapper xmlMapper = new XmlMapper();
    private ObjectHelper() {}
    static {
        objectMapperNonNull.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    public static ObjectMapper getObjectMapper() {
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper;
    }

    public static <T> T readValue(String data, Class<T> clazz) {
        try {
            return objectMapper.readValue(data, clazz);
        } catch (IOException ex) {
            logger.error("ObjectHelper: could not convert object from " + data + " to " + clazz.toString(), ex);
            return null;
        }
    }

    public static <T> T readNonNullValue(String data, Class<T> clazz) {
        try {
            return objectMapperNonNull.readValue(data, clazz);
        } catch (IOException ex) {
            logger.error("ObjectHelper: could not convert object from " + data + " to " + clazz.toString(), ex);
            return null;
        }
    }

    public static <T> String writeValue(T data) {
        try {
            return objectMapper.writeValueAsString(data);
        } catch (IOException e) {
            logger.error("ObjectHelper: could not convert data " + data.getClass().toString() + " to string", e);
            return null;
        }
    }

    public static <T> String writeNonNullValue(T data) {
        try {
            return objectMapperNonNull.writeValueAsString(data);
        } catch (IOException e) {
            logger.error("ObjectHelper: could not convert data " + data.getClass().toString() + " to string", e);
            return null;
        }
    }

    public static <T> T convertNonNullValue(Object obj, Class<T> c) {
        return objectMapperNonNull.convertValue(obj, c);
    }

    public static XmlMapper getXmlMapper() {
        xmlMapper.registerModule(new JaxbAnnotationModule());
        xmlMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        return xmlMapper;
    }

    public static String convertToString(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (IOException e) {

        }
        return null;
    }
}
