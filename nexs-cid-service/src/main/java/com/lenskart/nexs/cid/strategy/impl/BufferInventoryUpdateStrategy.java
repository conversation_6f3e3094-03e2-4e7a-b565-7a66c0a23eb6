package com.lenskart.nexs.cid.strategy.impl;

import com.lenskart.nexs.cid.dto.WarehouseInventoryDto;
import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.cid.response.WarehouseInventoryCountResponse;
import com.lenskart.nexs.cid.strategy.BaseInventoryUpdateStrategy;
import com.lenskart.nexs.ims.enums.CidInventoryOperation;
import com.lenskart.nexs.ims.model.Condition;
import com.lenskart.nexs.ims.model.Availability;
import com.lenskart.nexs.ims.model.Status;
import com.lenskart.nexs.ims.request.InventoryUpdateRequest;
import org.springframework.stereotype.Component;

@Component
public class BufferInventoryUpdateStrategy extends BaseInventoryUpdateStrategy {
    @Override
    public CidInventoryOperation supportedInventoryOperation() {
        return CidInventoryOperation.BUFFER_UPDATE;
    }

    @Override
    public void execute(InventoryUpdateRequest request) throws ApplicationException {
        // Only update buffer for GOOD+AVAILABLE+AVAILABLE
        WarehouseInventoryDto persistedWhInvDto = warehouseInventoryService.findByProductIdFacilityConditionAvailabilityAndStatusAndLegalOwnerAndLocationType(
                request.getProductId().longValue(),
                request.getFacility(),
                Condition.GOOD,
                Availability.AVAILABLE,
                Status.AVAILABLE,
                request.getLegalOwner(),
                request.getLocationType()
        );
        if (persistedWhInvDto == null) {
            throw new ApplicationException("No warehouse inventory found for buffer update with productId=" + request.getProductId() + ", facility=" + request.getFacility());
        }
        persistedWhInvDto.setBufferedQuantity(request.getBufferQuantity());
        persistedWhInvDto.setUpdatedBy(request.getUpdatedBy());
        warehouseInventoryService.update(persistedWhInvDto, persistedWhInvDto.getId());

        // Trigger downstream events as in absolute update
        WarehouseInventoryCountResponse warehouseInventoryCountResponse = getTotalWarehouseInventory(request);
        int bufferedQuantity = warehouseInventoryCountResponse.getBufferedWhInventoryCount();
        updateStorefrontInventory(request, warehouseInventoryCountResponse.getTotalWhInventoryCount(), bufferedQuantity);
        sendInventoryUpdateToSCM(request, warehouseInventoryCountResponse.getTotalWhInventoryCount(), bufferedQuantity);
        sendInventoryUpdateToJuno(request);
        updateBlockedInventoryDtoAndSendEvent(request, persistedWhInvDto);
    }
} 