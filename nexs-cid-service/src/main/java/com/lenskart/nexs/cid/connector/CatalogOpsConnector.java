package com.lenskart.nexs.cid.connector;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.lenskart.nexs.cid.util.ObjectHelper;
import com.lenskart.nexs.common.http.RestUtils;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.constants.RedisOps;
import com.lenskart.nexs.ims.request.Product;
import com.lenskart.nexs.service.RedisHandler;
import io.micrometer.core.annotation.Timed;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.*;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Configuration
public class CatalogOpsConnector {

    @CustomLogger
    private Logger logger;

    @Value("${nexs.shipping.catalog.redis.ttl}")
    private String redisTtl;

    @Value("${nexs.shipping.catalog.timeout}")
    private int timeout;

    @Value("${nexs.shipping.catalog.ops.host}")
    private String catalogOpsHost;

    @Value("${nexs.catalog.ops.product.byClassification.url}")
    private String fetchProductByClassificationUrl;

    @Value("${nexs.shipping.catalog.ops.product.url}")
    private String fetchProductDetailsUrl;

    private static final String CATALOG_PRODUCT_BY_CLASSIFICATION_CACHE_KEY = "NEXS_CATALOG_PRODUCT_BY_CLASSIFICATION";

    private static final String CATALOG_PRODUCT_RESPONSE_CACHE_KEY = "NEXS_CID_SHIPPING_CATALOG_PRODUCT";

    @Timed
    public List<Product> findByClassification(List<String> catalogClassifications) throws Exception {
        List<Product> productList = new ArrayList<>();
        getProductListFromRedis(catalogClassifications, productList);

        if (!CollectionUtils.isEmpty(productList)) {
            return productList;
        }

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> httpEntity = new HttpEntity(catalogClassifications, httpHeaders);
        RestTemplate restTemplate = RestUtils.getRestTemplate(Duration.ofMillis(timeout), Duration.ofMillis(timeout));
        ResponseEntity<String> responseEntity = restTemplate.exchange(catalogOpsHost + fetchProductByClassificationUrl, HttpMethod.POST, httpEntity, String.class, (Object) null);
        logger.info("[findByClassification] response from catalogOps is {}", responseEntity);

        if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
            JsonNode jsonNode = ObjectHelper.getObjectMapper().readTree(responseEntity.getBody());
            List<Product> products = ObjectHelper.getObjectMapper().convertValue(jsonNode, new TypeReference<List<Product>>() {});

            Map<Integer, List<Product>> productsGroupByClassification = products.stream().collect(Collectors.groupingBy(product -> product.getClassification()));
            for (Map.Entry<Integer, List<Product>> entry : productsGroupByClassification.entrySet()) {
                RedisHandler.redisOps(RedisOps.SETVALUETTL, CATALOG_PRODUCT_BY_CLASSIFICATION_CACHE_KEY + entry.getKey(), ObjectHelper.convertToString(entry.getValue()), Long.valueOf(redisTtl), TimeUnit.HOURS);
                logger.info("[findByClassification] product list by classification {} stored in redis with key {}", entry.getKey(), CATALOG_PRODUCT_BY_CLASSIFICATION_CACHE_KEY + entry.getKey());
            }

            return products;
        } else {
            logger.error("[findByClassification] Fetch products list by classification from catalog failed with status {}", responseEntity.getStatusCode());
            throw new Exception("Fetch products list by classification from catalog failed, response is " + responseEntity.getStatusCodeValue());
        }
    }

    private void getProductListFromRedis(List<String> catalogClassifications, List<Product> productList) throws Exception {
        for (String classification : catalogClassifications) {
            Object productListObj = RedisHandler.redisOps(RedisOps.GET, CATALOG_PRODUCT_BY_CLASSIFICATION_CACHE_KEY + classification);
            if (Objects.nonNull(productListObj)) {
                logger.info("[findByClassification] Product list found in cache for classification {}", classification);
                productList.addAll(ObjectHelper.getObjectMapper().readValue(String.valueOf(productListObj), new TypeReference<List<Product>>() {}));
            } else {
                logger.info("[findByClassification] Product list not found in cache for classification {}", classification);
                productList.clear();
                break;
            }
        }
    }

    @Timed
    public Product findByProductId(Integer productId) throws Exception {
        Object productDetails = RedisHandler.redisOps(RedisOps.GET, CATALOG_PRODUCT_RESPONSE_CACHE_KEY + productId);
        if (Objects.nonNull(productDetails)) {
            Product product = ObjectHelper.getObjectMapper().readValue(String.valueOf(productDetails), Product.class);
            logger.info("[findByProductId] Product is {} and save product is {}", product, productDetails);
            return product;
        }
        logger.error("Product details not found in cache for pid " +productId);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> httpEntity = new HttpEntity(httpHeaders);
        RestTemplate restTemplate = RestUtils.getRestTemplate(Duration.ofMillis(timeout), Duration.ofMillis(timeout));
        ResponseEntity<String> responseEntity = restTemplate.exchange(catalogOpsHost + fetchProductDetailsUrl + productId, HttpMethod.GET, httpEntity, String.class, (Object) null);
        if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
            Product product = ObjectHelper.getObjectMapper().readValue(responseEntity.getBody(), new TypeReference<Product>() {
            });
            RedisHandler.redisOps(RedisOps.SETVALUETTL, CATALOG_PRODUCT_RESPONSE_CACHE_KEY + productId, responseEntity.getBody(), Long.valueOf(redisTtl), TimeUnit.HOURS);
            return product;
        } else {
            throw new Exception("Fetch product details from catalog failed ,response is " + responseEntity.getStatusCodeValue());
        }

    }
}
