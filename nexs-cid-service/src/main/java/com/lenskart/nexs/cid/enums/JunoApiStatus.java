package com.lenskart.nexs.cid.enums;

/**
 * Enum to represent the status of Juno API calls
 */
public enum JunoApiStatus {
    SUCCESS("SUCCESS"),
    FAILED("FAILED"),
    TIMEOUT("TIMEOUT"),
    RETRY("RETRY"),
    PENDING("PENDING");

    private final String status;

    JunoApiStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    @Override
    public String toString() {
        return status;
    }
}
