package com.lenskart.nexs.cid.controller;

import com.lenskart.nexs.cid.constant.CommonConstants;
import com.lenskart.nexs.cid.facade.UnicomInventoryUpdateFacade;
import com.lenskart.nexs.cid.request.updateinventory.StockAdjustment;
import com.lenskart.nexs.cid.request.updateinventory.StockAdjustmentResponse;
import lombok.Setter;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ws.server.endpoint.annotation.Endpoint;
import org.springframework.ws.server.endpoint.annotation.PayloadRoot;
import org.springframework.ws.server.endpoint.annotation.RequestPayload;
import org.springframework.ws.server.endpoint.annotation.ResponsePayload;

@Endpoint
public class UnicomInventoryUpdateWsController {

    private static final String NAMESPACE_URI = "http://tempuri.org";

    @Setter(onMethod__ = {@Autowired})
    private UnicomInventoryUpdateFacade unicomInventoryUpdateFacade;

    @PayloadRoot(namespace = NAMESPACE_URI, localPart = "StockAdjustment")
    @ResponsePayload
    public StockAdjustmentResponse updateInventory(@RequestPayload StockAdjustment stockAdjustmentRequest) throws Exception {
        if (MDC.get(CommonConstants.USER_ID) == null)
            MDC.put(CommonConstants.USER_ID, CommonConstants.UNICOM_USER);
        return unicomInventoryUpdateFacade.updateInventory(stockAdjustmentRequest);
    }

}
