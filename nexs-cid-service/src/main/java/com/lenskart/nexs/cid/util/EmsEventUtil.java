package com.lenskart.nexs.cid.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lenskart.nexs.cid.dto.WarehouseBlockedInventoryDto;
import com.lenskart.nexs.cid.util.ObjectHelper;
import com.lenskart.nexs.ems.enums.ExceptionType;
import com.lenskart.nexs.ems.enums.Source;
import com.lenskart.nexs.ems.model.EmsExceptionEvent;
import com.lenskart.nexs.ems.model.request.EmsFullfillablityEventRequest;

import java.util.Date;

public class EmsEventUtil {
    public static EmsExceptionEvent populateEmsEventPayload(WarehouseBlockedInventoryDto warehouseBlockedInventoryDto, String type, String legalOwner) throws JsonProcessingException {
        EmsFullfillablityEventRequest emsFullfillablityEventRequest = new EmsFullfillablityEventRequest();
        emsFullfillablityEventRequest.setPid(warehouseBlockedInventoryDto.getProductId());
        emsFullfillablityEventRequest.setCount(warehouseBlockedInventoryDto.getUnfulfillableQty());
        emsFullfillablityEventRequest.setType(type);
        emsFullfillablityEventRequest.setFacility(warehouseBlockedInventoryDto.getFacility());
        emsFullfillablityEventRequest.setLegalOwner(legalOwner);
        emsFullfillablityEventRequest.setRequestedAt(new Date());

        EmsExceptionEvent emsEventPayload = new EmsExceptionEvent();
        emsEventPayload.setMeta(ObjectHelper.getObjectMapper().writeValueAsString(emsFullfillablityEventRequest));
        emsEventPayload.setExceptionType(ExceptionType.CID_FULFILLABILITY_CHANGE);
        emsEventPayload.setSource(Source.CID);
        emsEventPayload.setCreatedAt(new Date());

        return emsEventPayload;
    }

}