package com.lenskart.nexs.cid.config;

import com.lenskart.nexs.cid.constant.ControllerUrl;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.ws.config.annotation.EnableWs;
import org.springframework.ws.config.annotation.WsConfigurerAdapter;
import org.springframework.ws.transport.http.MessageDispatcherServlet;
import org.springframework.ws.wsdl.wsdl11.DefaultWsdl11Definition;
import org.springframework.xml.xsd.SimpleXsdSchema;
import org.springframework.xml.xsd.XsdSchema;

@EnableWs
@Configuration
public class WebServiceConfig extends WsConfigurerAdapter {

    private static final String UPDATE_INVENTORY_REQUEST= "nexs.cid.targetsource";

    @Setter(onMethod__ = {@Autowired})
    private ApplicationPropertyConfig applicationPropertyConfig;

    @Bean
    public ServletRegistrationBean messageDispatcherServlet(ApplicationContext context) {
        MessageDispatcherServlet servlet = new MessageDispatcherServlet();
        servlet.setApplicationContext(context);
        servlet.setTransformWsdlLocations(true);
        return new ServletRegistrationBean(servlet,
                ControllerUrl.BASE_URL+
                        ControllerUrl.UPDATE_CID_INVENTORY_SOAP+"/*");
    }

    @Bean
    public XsdSchema cidSchema() {
        return new SimpleXsdSchema(new ClassPathResource("CID.xsd"));
    }

    @Bean
    public DefaultWsdl11Definition defaultWsdl11Definition(XsdSchema cidSchema) {
        DefaultWsdl11Definition definition = new DefaultWsdl11Definition();
        definition.setSchema(cidSchema);
        definition.setLocationUri(ControllerUrl.BASE_URL+ControllerUrl.UPDATE_CID_INVENTORY_SOAP);
        definition.setPortTypeName("CIDAdaptorPort");
        definition.setTargetNamespace(applicationPropertyConfig.getString(UPDATE_INVENTORY_REQUEST));
        return definition;
    }
}
