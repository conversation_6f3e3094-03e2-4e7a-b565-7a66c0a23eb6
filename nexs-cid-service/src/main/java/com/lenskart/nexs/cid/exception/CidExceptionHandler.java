package com.lenskart.nexs.cid.exception;

import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.commons.codes.Status;
import com.lenskart.nexs.commons.dto.BaseMetaModel;
import com.lenskart.nexs.commons.dto.BaseResponseModel;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

@ControllerAdvice
public class CidExceptionHandler {

    private static final String EXCEPTION_OCCURRED_ERROR_MESSAGE = "error message: ";

    @CustomLogger
    private Logger logger;

    @ResponseBody
    @ExceptionHandler(value = {ItemBlockedInDifferentFacilityException.class})
    public ResponseEntity<BaseResponseModel> handleException(ItemBlockedInDifferentFacilityException exception) {
        logger.error(EXCEPTION_OCCURRED_ERROR_MESSAGE + exception.getMessage(), exception);
        return failureResponse(exception.getMessage(), exception.getErrorCode(), exception.getBlockedFacility());
    }

    @ResponseBody
    @ExceptionHandler(value = {OrderPartiallyReleasedException.class})
    public ResponseEntity<BaseResponseModel> handleOrderPartiallyReleasedException(OrderPartiallyReleasedException exception) {
        logger.error(EXCEPTION_OCCURRED_ERROR_MESSAGE + exception.getMessage(), exception);
        return failureResponse(exception.getMessage(), exception.getErrorCode(), exception.getBlockedFacility());
    }

    private ResponseEntity<BaseResponseModel> failureResponse(String message, String errorCode, String blockedFacility) {
        BaseResponseModel baseResponseModel = createResponseModel(message, errorCode);
        baseResponseModel.setData(blockedFacility);
        return new ResponseEntity<>(baseResponseModel, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    private BaseResponseModel createResponseModel(String message, String errorCode) {
        BaseResponseModel baseResponseModel = new BaseResponseModel();
        BaseMetaModel baseMetaModel = new BaseMetaModel();
        baseMetaModel.setDisplayMessage(message);
        baseMetaModel.setCode(errorCode);
        baseMetaModel.setMessage(Status.ERROR.label);
        baseResponseModel.setMeta(baseMetaModel);
        return baseResponseModel;
    }
}
