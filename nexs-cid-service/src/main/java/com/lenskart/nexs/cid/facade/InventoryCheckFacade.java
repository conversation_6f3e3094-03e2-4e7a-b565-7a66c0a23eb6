package com.lenskart.nexs.cid.facade;

import com.lenskart.nexs.cid.connector.CatalogOpsConnector;
import com.lenskart.nexs.cid.dto.WarehouseBlockedInventoryDto;
import com.lenskart.nexs.cid.dto.WarehouseInventoryDto;
import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.cid.request.InventoryAvailabilityCountRR;
import com.lenskart.nexs.cid.request.InventoryItemCountRR;
import com.lenskart.nexs.cid.response.FetchInventoryItem;
import com.lenskart.nexs.cid.response.FetchInventoryResponse;
import com.lenskart.nexs.cid.service.WarehouseBlockedInventoryService;
import com.lenskart.nexs.cid.service.WarehouseInventoryService;
import com.lenskart.nexs.cid.util.LegalOwnerUtil;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.ims.connector.FMSConnector;
import com.lenskart.nexs.ims.enums.LocationType;
import com.lenskart.nexs.ims.model.Availability;
import com.lenskart.nexs.ims.model.Condition;
import com.lenskart.nexs.ims.model.Status;
import com.lenskart.nexs.ims.request.OrderItemStockCheck;
import com.lenskart.nexs.ims.request.OrderStockCheckRequest;
import com.lenskart.nexs.ims.request.Product;
import com.lenskart.nexs.ims.request.StockCheck;
import com.lenskart.nexs.ims.response.FacilityDetails;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;

@Component
public class InventoryCheckFacade {

    @CustomLogger
    private Logger logger;

    @Setter(onMethod__ = {@Autowired})
    private CatalogOpsConnector catalogOpsConnector;

    @Setter(onMethod__ = {@Autowired})
    private WarehouseInventoryService warehouseInventoryService;

    @Setter(onMethod__ = {@Autowired})
    private WarehouseBlockedInventoryService warehouseBlockedInventoryService;

    @Setter(onMethod__ = {@Autowired})
    private FMSConnector fmsConnector;

    @Value("#{'${nexs.loyalty.classification.list}'.split(',')}")
    private List<String> catalogClassifications;

    @Value("${legalOwner.defaultLegalOnwner}")
    private String defaultLegalOwner;

    @Value("${fms-connector.base.url}")
    private static String fmsConnectorBaseUrl;

    @Value("${fms-connector.facility.details.url}")
    private static String fmsConnectorFacilityDetailsUrl;

    @Value("#{'${nexs.carry.bag.pid.list}'.split(',')}")
    private Set<Integer> carryBagPidSet;

    public List<FetchInventoryResponse> fetchAvailableInventory(OrderStockCheckRequest orderStockCheckRequest) throws ApplicationException {
        logger.debug("[fetchInventory] orderStockCheckRequest is {}", orderStockCheckRequest);
        List<FetchInventoryResponse> fetchInventoryResponseList = new ArrayList<>();
        for (OrderItemStockCheck orderItemStockCheck : orderStockCheckRequest.getOrderItemStockCheckList()) {
            FetchInventoryResponse fetchInventoryResponse = new FetchInventoryResponse();
            List<FetchInventoryItem> fetchInventoryItemList = new ArrayList<>();
            Integer orderId = orderItemStockCheck.getOrderId();
            fetchInventoryResponse.setOrderId(orderId);
            for (StockCheck stockCheck : orderItemStockCheck.getStockCheckList()) {
                defaultLegalOwnerSetter(stockCheck);
                performStockCheck(stockCheck, fetchInventoryItemList, orderId);
            }
            fetchInventoryResponse.setFetchInventoryItemList(fetchInventoryItemList);
            fetchInventoryResponseList.add(fetchInventoryResponse);
        }
        return fetchInventoryResponseList;
    }

    private void performStockCheck(StockCheck stockCheck, List<FetchInventoryItem> fetchInventoryItemList, Integer orderId) throws ApplicationException {
        logger.debug("[performStockCheck] perform stock check for stock check request {} and order id {}", stockCheck, orderId);
        if (isLoyaltyPid(stockCheck.getPid())) {
            logger.info("[performStockCheck -> isLoyaltyPid] request PID {} is a loyalty PID", stockCheck.getPid());
            fetchInventoryItemList.add(FetchInventoryItem.builder()
                    .pid(stockCheck.getPid())
                    .isLoyaltyPid(true)
                    .facility(stockCheck.getFacility())
                    .quantity(0).build());
            return;
        }
        checkAvailableInventory(stockCheck, fetchInventoryItemList);
    }

    private void checkAvailableInventory(StockCheck stockCheck, List<FetchInventoryItem> fetchInventoryItemList) {
        WarehouseInventoryDto warehouseInventory = warehouseInventoryService.findByProductIdFacilityConditionAvailabilityAndStatusAndLegalOwnerAndLocationType(
                Long.valueOf(stockCheck.getPid()), stockCheck.getFacility(),
                Condition.GOOD, Availability.AVAILABLE, Status.AVAILABLE,
                stockCheck.getLegalOwner(), LocationType.DEFAULT
        );

        long totalAvailableQuantityAgainstPidAndFacility = warehouseInventory != null ? warehouseInventory.getQuantity() : 0;
        WarehouseBlockedInventoryDto warehouseBlockedInventoryDto = warehouseBlockedInventoryService.findByProductIdAndFacilityAndLegalOwner(
                Long.valueOf(stockCheck.getPid()), stockCheck.getFacility(), stockCheck.getLegalOwner()
        );

        long totalBlockedQuantityAgainstPidAndFacility = warehouseBlockedInventoryDto != null ? warehouseBlockedInventoryDto.getQuantity() : 0;
        logger.info("[checkAvailableInventory] total available quantity is {} and total blocked quantity is {} for pid {} and facility {}", totalAvailableQuantityAgainstPidAndFacility, totalBlockedQuantityAgainstPidAndFacility, stockCheck.getPid(), stockCheck.getFacility());

        fetchInventoryItemList.add(FetchInventoryItem.builder()
                .pid(stockCheck.getPid())
                .isLoyaltyPid(false)
                .quantity(totalAvailableQuantityAgainstPidAndFacility - totalBlockedQuantityAgainstPidAndFacility - stockCheck.getOrderItems().size())
                .facility(stockCheck.getFacility()).build());
    }

    private boolean isLoyaltyPid(Integer productId) throws ApplicationException {
        try {
            Set<Long> loyaltyPidSet = new HashSet<>();
            enrichLoyaltyProducts(loyaltyPidSet);
            if (loyaltyPidSet.contains(Long.valueOf(productId)) || carryBagPidSet.contains(productId)) {
                return true;
            }
            logger.debug("[performStockCheck -> isLoyaltyPid] request PID {} is not part of loyalty PIDs List {}", productId, loyaltyPidSet);
            return false;
        } catch (Exception ex) {
            logger.error("[performStockCheck -> isLoyaltyPid] Loyalty PID check failed for pid " + productId, ex);
            throw new ApplicationException(ex.getMessage(), ex);
        }
    }

    private void enrichLoyaltyProducts(Set<Long> loyaltyPidSet) throws Exception {
        logger.debug("[performStockCheck -> setLoyaltyProducts] going to fetch products list by classifications {}", catalogClassifications);

        List<Product> productListByClassifications = catalogOpsConnector.findByClassification(catalogClassifications);
        for (Product product : productListByClassifications) {
            loyaltyPidSet.add(product.getProductId());
        }
        logger.debug("[performStockCheck -> setLoyaltyProducts] list of loyalty PIDs is {}", loyaltyPidSet);
    }

    private void defaultLegalOwnerSetter(StockCheck stockCheck) {
        if (LegalOwnerUtil.getFacilityLegalOwnerMap().containsKey(stockCheck.getFacility())) {
            stockCheck.setLegalOwner(LegalOwnerUtil.getFacilityLegalOwnerMap().get(stockCheck.getFacility()));
        } else {
            try {
                FacilityDetails facilityDetails = fmsConnector.fetchLegalOwner(stockCheck.getFacility(), fmsConnectorBaseUrl + fmsConnectorFacilityDetailsUrl);
                if (!Objects.isNull(facilityDetails)) {
                    stockCheck.setLegalOwner(facilityDetails.getLegalOwner());
                } else {
                    stockCheck.setLegalOwner(defaultLegalOwner);
                }
            } catch (Exception e) {
                stockCheck.setLegalOwner(defaultLegalOwner);
            }
        }
    }

    public InventoryAvailabilityCountRR fetchAvailableInventoryCount(InventoryAvailabilityCountRR inventoryAvailabilityCountRR) throws ApplicationException {
        logger.debug("[fetchAvailableInventoryCount] fetchAvailableInventoryCount is {}", inventoryAvailabilityCountRR);
        for (InventoryItemCountRR item : inventoryAvailabilityCountRR.getItems()) {
            performInventoryAvailabilityCountCheck(item, inventoryAvailabilityCountRR.getFacility(), inventoryAvailabilityCountRR.getLegalOwner());
        }
        return inventoryAvailabilityCountRR;
    }

    private void performInventoryAvailabilityCountCheck(InventoryItemCountRR item, String facility, String legalOwner) throws ApplicationException {
        if (!isLoyaltyPid(item.getProductId()) || !carryBagPidSet.contains(item.getProductId())) {
            WarehouseInventoryDto warehouseInventory = warehouseInventoryService.findByProductIdFacilityConditionAvailabilityAndStatusAndLegalOwnerAndLocationType(
                    Long.valueOf(item.getProductId()), facility, Condition.GOOD, Availability.AVAILABLE, Status.AVAILABLE, legalOwner, LocationType.DEFAULT);

            long totalAvailableQuantityAgainstPidAndFacility = warehouseInventory != null ? warehouseInventory.getQuantity() : 0;
            WarehouseBlockedInventoryDto warehouseBlockedInventoryDto = warehouseBlockedInventoryService.findByProductIdAndFacilityAndLegalOwner(
                    Long.valueOf(item.getProductId()), facility, legalOwner);

            long totalBlockedQuantityAgainstPidAndFacility = warehouseBlockedInventoryDto != null && warehouseBlockedInventoryDto.getQuantity() > 0 ? warehouseBlockedInventoryDto.getQuantity() : 0;
            logger.info("[checkAvailableInventory] total available quantity is {} and total blocked quantity is {} for pid {} and facility {}", totalAvailableQuantityAgainstPidAndFacility, totalBlockedQuantityAgainstPidAndFacility, item.getProductId(), facility);

            Long totalQuantity = totalAvailableQuantityAgainstPidAndFacility - totalBlockedQuantityAgainstPidAndFacility;
            Long fulfillableQty = totalQuantity >= item.getRequestedQty() ? item.getRequestedQty().longValue() : totalQuantity;
            Long unFulfillableQty = totalQuantity >= item.getRequestedQty() ? 0 : item.getRequestedQty().longValue() - totalQuantity;

            item.setTotalAvailableQty(totalQuantity);
            item.setFulfillableQty(fulfillableQty);
            item.setUnFulfillableQty(unFulfillableQty);
        }
    }

}
