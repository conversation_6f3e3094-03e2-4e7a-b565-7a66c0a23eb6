package com.lenskart.nexs.cid.util;

import com.lenskart.nexs.cid.constant.CommonConstants;
import com.lenskart.nexs.cid.dto.WarehouseBlockedInventoryDto;
import com.lenskart.nexs.cid.dto.WarehouseBlockedOrderItemDto;
import com.lenskart.nexs.cid.enums.WarehouseBlockedOrderItemStatus;
import com.lenskart.nexs.cid.enums.Supplier;
import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.cid.response.StockBlockOrderItemResponse;
import com.lenskart.nexs.ims.connector.FMSConnector;
import com.lenskart.nexs.ims.enums.CidInventoryOperation;
import com.lenskart.nexs.ims.model.Availability;
import com.lenskart.nexs.ims.model.Condition;
import com.lenskart.nexs.ims.model.Status;
import com.lenskart.nexs.ims.request.InventoryUpdateRequest;
import com.lenskart.nexs.ims.request.StockCheck;
import com.lenskart.nexs.ims.response.FacilityDetails;
import com.lenskart.nexs.ims.response.StockCheckResponse;
import lombok.Setter;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class WarehouseBlockedInventoryUtil {

    @Value("${legalOwner.defaultLegalOnwner}")
    private static String defaultLegalOwner;

    @Value("${fms-connector.base.url}")
    private static String fmsConnectorBaseUrl;

    @Value("${fms-connector.facility.details.url}")
    private static String fmsConnectorFacilityDetailsUrl;

    @Setter(onMethod__ = {@Autowired})
    private static FMSConnector fmsConnector;

    public static WarehouseBlockedInventoryDto getWarehouseBlockedInventoryDto(String facility, Integer pid, int quantity,  long totalAvailableQuantityAgainstPidAndFacility) {
        WarehouseBlockedInventoryDto warehouseBlockedInventoryDto = new WarehouseBlockedInventoryDto();

        warehouseBlockedInventoryDto.setQuantity(quantity);
        warehouseBlockedInventoryDto.setFacility(facility);
        warehouseBlockedInventoryDto.setProductId(Long.valueOf(pid));
        warehouseBlockedInventoryDto.setCreatedBy(MDC.get(CommonConstants.USER_ID));
        warehouseBlockedInventoryDto.setUpdatedBy(MDC.get(CommonConstants.USER_ID));
        warehouseBlockedInventoryDto.setUnfulfillableQty((int) Math.max(0, quantity-totalAvailableQuantityAgainstPidAndFacility));
        return warehouseBlockedInventoryDto;
    }

    public static WarehouseBlockedOrderItemDto getWarehouseBlockedOrderItemDto(Integer orderItemId, Integer orderId, WarehouseBlockedInventoryDto warehouseBlockedInventoryDto, WarehouseBlockedOrderItemStatus status) {
        WarehouseBlockedOrderItemDto warehouseBlockedOrderItemDto = new WarehouseBlockedOrderItemDto();

        warehouseBlockedOrderItemDto.setOrderId(Long.valueOf(orderId));
        warehouseBlockedOrderItemDto.setOrderItemId(Long.valueOf(orderItemId));
        warehouseBlockedOrderItemDto.setWarehouseBlockedOrderItemStatus(status);
        warehouseBlockedOrderItemDto.setWarehouseBlockedInventoryDto(warehouseBlockedInventoryDto);
        warehouseBlockedOrderItemDto.setCreatedBy(MDC.get(CommonConstants.USER_ID));
        warehouseBlockedOrderItemDto.setUpdatedBy(MDC.get(CommonConstants.USER_ID));
        return warehouseBlockedOrderItemDto;
    }

    public static void populateInventoryUpdateRequest(Integer pid, String facility, List<InventoryUpdateRequest> inventoryUpdateRequestList, CidInventoryOperation cidInventoryOperation, String legalOwner) {
        InventoryUpdateRequest inventoryUpdateRequest = new InventoryUpdateRequest();
        inventoryUpdateRequest.setProductId(pid);
        inventoryUpdateRequest.setFacility(facility);
        inventoryUpdateRequest.setCondition(Condition.GOOD);
        inventoryUpdateRequest.setAvailability(Availability.AVAILABLE);
        inventoryUpdateRequest.setStatus(Status.AVAILABLE);
        inventoryUpdateRequest.setCidInventoryOperation(cidInventoryOperation);
        inventoryUpdateRequest.setUpdatedBy(MDC.get(CommonConstants.USER_ID));
        inventoryUpdateRequest.setSupplier(Supplier.NEXS.name());
        inventoryUpdateRequest.setLegalOwner(legalOwner);
        inventoryUpdateRequest.setEventTime(new Date());

        inventoryUpdateRequestList.add(inventoryUpdateRequest);
    }
    public static WarehouseBlockedInventoryDto getWarehouseBlockedInventoryDtoWithLegalOwner(String facility, Integer pid, int quantity,String legalOwner){ // , long totalAvailableQuantityAgainstPidAndFacility) {
        WarehouseBlockedInventoryDto warehouseBlockedInventoryDto = new WarehouseBlockedInventoryDto();

        warehouseBlockedInventoryDto.setQuantity(quantity);
        warehouseBlockedInventoryDto.setFacility(facility);
        warehouseBlockedInventoryDto.setProductId(Long.valueOf(pid));
        warehouseBlockedInventoryDto.setCreatedBy(MDC.get(CommonConstants.USER_ID));
        warehouseBlockedInventoryDto.setUpdatedBy(MDC.get(CommonConstants.USER_ID));
        warehouseBlockedInventoryDto.setLegalOwner(legalOwner);
//        warehouseBlockedInventoryDto.setUnfulfillableQty((int) Math.max(0, quantity-totalAvailableQuantityAgainstPidAndFacility));
        return warehouseBlockedInventoryDto;
    }

    public static void populateInventoryUpdateRequestWithLegalOwner(Integer pid, String facility, List<InventoryUpdateRequest> inventoryUpdateRequestList, CidInventoryOperation cidInventoryOperation,String legalOwner) {
        InventoryUpdateRequest inventoryUpdateRequest = new InventoryUpdateRequest();
        inventoryUpdateRequest.setProductId(pid);
        inventoryUpdateRequest.setFacility(facility);
        inventoryUpdateRequest.setCondition(Condition.GOOD);
        inventoryUpdateRequest.setAvailability(Availability.AVAILABLE);
        inventoryUpdateRequest.setStatus(Status.AVAILABLE);
        inventoryUpdateRequest.setCidInventoryOperation(cidInventoryOperation);
        inventoryUpdateRequest.setUpdatedBy(MDC.get(CommonConstants.USER_ID));
        inventoryUpdateRequest.setSupplier(Supplier.NEXS.name());
        inventoryUpdateRequest.setEventTime(new Date());
        inventoryUpdateRequest.setLegalOwner(legalOwner);

        inventoryUpdateRequestList.add(inventoryUpdateRequest);
    }

    public static void populateStockBlockResponseList(Long pid, List<Integer> orderItems, boolean fulfillable, List<StockBlockOrderItemResponse> stockBlockResponseList) {
        stockBlockResponseList.addAll(orderItems.stream().map(orderItemId ->
                new StockBlockOrderItemResponse(pid, orderItemId, fulfillable)).collect(Collectors.toList()));
    }

    public static void populateStockCheckResponse(StockCheck stockCheckRequest, List<StockCheckResponse> stockCheckResponses, boolean fulfillable) {
        stockCheckResponses.add(StockCheckResponse.builder()
                .fulfillable(fulfillable)
                .pid(stockCheckRequest.getPid())
                .httpStatus(HttpStatus.OK).build());
    }

    public static void setDefaultLegalOwnerBasedOnFacility(StockCheck stockCheck, List<StockCheckResponse> stockCheckResponses) throws ApplicationException {
        try {
            if (!StringUtils.hasLength(stockCheck.getLegalOwner())) {
                if (LegalOwnerUtil.getFacilityLegalOwnerMap().containsKey(stockCheck.getFacility())) {
                    stockCheck.setLegalOwner(LegalOwnerUtil.getFacilityLegalOwnerMap().get(stockCheck.getFacility()));
                } else {
                    try {
                        FacilityDetails facilityDetails = fmsConnector.fetchLegalOwner(stockCheck.getFacility(), fmsConnectorBaseUrl + fmsConnectorFacilityDetailsUrl);
                        if (!Objects.isNull(facilityDetails)) {
                            stockCheck.setLegalOwner(facilityDetails.getLegalOwner());
                        } else {
                            stockCheck.setLegalOwner(defaultLegalOwner);
                        }
                    } catch (Exception e) {
                        stockCheck.setLegalOwner(defaultLegalOwner);
                    }
                }
            }
        } catch (Exception e) {
            populateStockCheckResponse(stockCheck, stockCheckResponses, false);
            throw new ApplicationException("stock block failed for the " + stockCheck.getPid() + " while setting the default legalOwner");
        }
    }
}
