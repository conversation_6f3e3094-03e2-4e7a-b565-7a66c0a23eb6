package com.lenskart.nexs.cid.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import com.lenskart.nexs.cid.constant.CommonConstants;
import com.lenskart.nexs.ims.enums.CidInventoryOperation;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;

@Getter
@Setter
@ToString
@org.springframework.data.elasticsearch.annotations.Document(indexName = "audit-history")
@JsonIgnoreProperties(ignoreUnknown = true)
public class AuditHistoryEntity {

    @Id
    private String id;

    @Field("entityId")
    private Long entityId;

    @Field("entityName")
    private String entityName;

    @Field("operation")
    private String operation;

    @Field("traceId")
    private String traceId;

    @Field(value = "eventTime", type = FieldType.Date, format = DateFormat.custom, pattern = CommonConstants.ES_DATE_PATTERN)
    private String eventTime;

    @Field("requestData")
    private String requestDataJson;

    @Field("oldData")
    private String oldDataJson;

    @Field("newData")
    private String newDataJson;

}
