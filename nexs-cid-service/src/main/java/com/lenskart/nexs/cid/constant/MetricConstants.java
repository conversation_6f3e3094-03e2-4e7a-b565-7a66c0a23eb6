package com.lenskart.nexs.cid.constant;

public final class MetricConstants {

    private MetricConstants() {}

    public static final String SCM_INVENTORY_UPDATE_CONSUMER = "scm_inventory_update_consumer";
    public static final String JUNO_INVENTORY_UPDATE_CONSUMER = "juno_inventory_update_consumer";
    public static final String INVENTORY_UPDATE_CONSUMER = "inventory_update_consumer";
    public static final String INVENTORY_UPDATE_CONSUMER_P1 = "inventory_update_consumer_p1";
    public static final String INVENTORY_HISTORY_UPDATE_CONSUMER = "inventory_history_update_consumer";
    public static final String UNICOM_GET_INVENTORY_SNAPSHOT = "unicom_get_inventory_snapshot";
    public static final String SCM_INVENTORY_UPDATE = "scm_inventory_update";
    public static final String SAVE_ERROR_EVENT = "save_error_event";
    public static final String STALE_INVENTORY_UPDATE = "stale_inventory_update";
    public static final String INVENTORY_BLOCK_UPDATE = "inventory_block_update";
    public static final String INVENTORY_RELEASE_UPDATE = "inventory_release_update";
    public static final String INVENTORY_RECONCILIATION_CONSUMER = "inventory_reconciliation_consumer";
    public static final String UFF_INVENTORY_AVAILABLE_IN_UNICOM_COUNTER = "uff_inventory_available_in_unicom_counter";
    public static final String UFF_INVENTORY_NOT_AVAILABLE_IN_NEXS_AND_UNICOM_COUNTER = "uff_inventory_not_available_in_nexs_unicom_counter";


    // New Relic Constants
    public static final String SCM_UPDATE_RESPONSE_RECORD = "SCM_UPDATE_RESPONSE_RECORD";
    public static final String CID_SCM_INVENTORY_UPDATE_CONSUMER_EVENT = "CID_SCM_INVENTORY_UPDATE_CONSUMER_EVENT";
    public static final String CID_SCM_INVENTORY_UPDATE_CONSUMER_FAILURE_EVENT = "CID_SCM_INVENTORY_UPDATE_CONSUMER_FAILURE_EVENT";
    public static final String CID_INVENTORY_UPDATE_CONSUMER_FAILURE_EVENT = "CID_INVENTORY_UPDATE_CONSUMER_FAILURE_EVENT";
    public static final String CID_INVENTORY_HISTORY_UPDATE_CONSUMER_FAILURE_EVENT = "CID_INVENTORY_HISTORY_UPDATE_CONSUMER_FAILURE_EVENT";
    public static final String CID_INVENTORY_UPDATE_CONSUMER_EVENT = "CID_INVENTORY_UPDATE_CONSUMER_EVENT";
    public static final String NEXS_CID_SCM_FAILURE_PIDS = "NEXS_CID_SCM_FAILURE_PIDS";
    public static final String STOCK_CHECK_EVENT = "STOCK_CHECK_EVENT";
    public static final String PRODUCT_NOT_FULFILLABLE = "PRODUCT_NOT_FULFILLABLE";
}
