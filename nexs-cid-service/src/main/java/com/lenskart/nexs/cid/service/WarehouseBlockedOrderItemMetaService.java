package com.lenskart.nexs.cid.service;

import com.lenskart.nexs.cid.dto.WarehouseBlockedOrderItemMetaDto;
import com.lenskart.nexs.cid.entity.WarehouseBlockedOrderItemMeta;
import com.lenskart.nexs.commons.service.BaseService;

import java.util.List;

public interface WarehouseBlockedOrderItemMetaService extends BaseService<WarehouseBlockedOrderItemMetaDto, WarehouseBlockedOrderItemMeta> {

    WarehouseBlockedOrderItemMetaDto findByOrderItemId(Long orderItemId);
    List<WarehouseBlockedOrderItemMetaDto> findAllByOrderItemId(String orderItemIds);
}
