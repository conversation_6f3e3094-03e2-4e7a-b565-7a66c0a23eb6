package com.lenskart.nexs.cid.strategy.impl;

import com.lenskart.nexs.cid.dto.WarehouseInventoryDto;
import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.cid.response.WarehouseInventoryCountResponse;
import com.lenskart.nexs.cid.strategy.BaseInventoryUpdateStrategy;
import com.lenskart.nexs.ims.enums.CidInventoryOperation;
import com.lenskart.nexs.ims.request.InventoryUpdateRequest;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class ReconcileInventoryUpdateStrategy extends BaseInventoryUpdateStrategy {

    @Override
    public CidInventoryOperation supportedInventoryOperation() {
        return CidInventoryOperation.RECONCILE;
    }

    @Value("${legalOwner.uaeLegalOwner}")
    private String uaeLegalOwner;

    @Override
    public void execute(InventoryUpdateRequest request) throws ApplicationException {
        String requestFacility = request.getFacility();

        /** Ignoring requested facility for fetching AVAILABLE count from warehouse inventory - to get the upfront count even for the requested facility */
        request.setFacility(null);
        WarehouseInventoryCountResponse warehouseInventoryCountResponse = getTotalWarehouseInventory(request);
        request.setFacility(requestFacility);

        logger.info("processing instant update where productId: {} facility: {} condition: {} " +
                        "availability: {}, status: {} instant count: {} totalWhInventoryCount: {}",
                request.getProductId(), request.getFacility(), request.getCondition(), request.getAvailability(),
                request.getStatus(), request.getQuantity(), warehouseInventoryCountResponse.getTotalWhInventoryCount());
        updateStorefrontInventory(request, warehouseInventoryCountResponse.getTotalWhInventoryCount(), warehouseInventoryCountResponse.getBufferedWhInventoryCount());
        sendInventoryUpdateToSCM(request, warehouseInventoryCountResponse.getTotalWhInventoryCount(), warehouseInventoryCountResponse.getBufferedWhInventoryCount());
        sendInventoryUpdateToJuno(request);
        WarehouseInventoryDto persistedWhInvDto = warehouseInventoryService.findByProductIdFacilityConditionAvailabilityAndStatusAndLegalOwnerAndLocationType(
                request.getProductId().longValue(),
                request.getFacility(),
                request.getCondition(),
                request.getAvailability(),
                request.getStatus(),
                request.getLegalOwner(),
                request.getLocationType()
        );
        updateBlockedInventoryDtoAndSendEvent(request,persistedWhInvDto);

    }

}
