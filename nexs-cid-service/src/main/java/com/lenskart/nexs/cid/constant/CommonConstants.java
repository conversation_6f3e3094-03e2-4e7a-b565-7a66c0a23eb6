package com.lenskart.nexs.cid.constant;

public final class CommonConstants {

    private CommonConstants() {}

    public static final String GET_INVENTORY_SUCCESS_MESSAGE = "Warehouse Inventory fetched successfully";
    public static final String USER_ID = "USER_ID";
    public static final String UNICOM_USER = "UNICOM_SYS";
    public static final String IMS_USER = "IMS_SYS";
    public static final String MESSAGE = "Success";
    public static final String DEFAULT_USER = "system";
    public static final String WAREHOUSE_INVENTORY = "WarehouseInventory";
    public static final String ES_DATE_PATTERN = "uuuu-MM-dd HH:mm:ss";
    public static final String DATE_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final String SCRIPT_USER = "script";
    public static final String AUTH_APP_ID = "X-Lenskart-App-Id";
    public static final String AUTH_API_KEY = "X-Lenskart-API-Key";
    public static final String UFF_POSITIVE_INVENTORY_FOR_UNICOM_CACHE_KEY = "UFF_POSITIVE_INVENTORY_FOR_UNICOM_1";
    public static final String UFF_NEGATIVE_INVENTORY_FOR_NEXS_AND_UNICOM_CACHE_KEY = "UFF_NEGATIVE_INVENTORY_FOR_NEXS_AND_UNICOM_1";
    public static final String RECONCILE_USER = "reconcileUser";
    public static final String DEFAULT_ENTITY_TTYPE = "ORDER";
    public static final String INCREMENT = "increment";
    public static final String DECREMENT = "decrement";
}