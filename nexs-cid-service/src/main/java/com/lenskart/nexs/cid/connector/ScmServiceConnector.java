package com.lenskart.nexs.cid.connector;

import com.lenskart.nexs.cid.constant.MetricConstants;
import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.cid.request.updateinventory.ScmInventoryUpdateRequest;
import com.lenskart.nexs.cid.util.NewRelicUtil;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.ims.request.InventoryUpdateRequest;
import io.micrometer.core.annotation.Timed;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.Date;

@Component
public class ScmServiceConnector {

    private final Counter errorCounter = Metrics.counter(MetricConstants.SCM_INVENTORY_UPDATE, "result", "failure");

    @CustomLogger
    private Logger logger;

    @Value("${vsm.inventory.web.wsdl.url}")
    private String vsmWsdlUrl;

    @Value("${nexs.cid.vsm.timeout}")
    private int vsmTimeout;


    String request = "<soapenv:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" " +
            "xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" " +
            "xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" " +
            "xmlns:urn=\"urn:UpdateShippingPackageWSDL\">\n" +
            "   <soapenv:Header/>\n" +
            "   <soapenv:Body>\n" +
            "      <urn:StockAdjustment soapenv:encodingStyle=\"http://schemas.xmlsoap.org/soap/encoding/\">\n" +
            "         <ProductIdArr xsi:type=\"SOAP-ENC:Array\" SOAP-ENC:arrayType=\"unnamed_struct_use_soapval[2]\">\n" +
            "            <item>\n" +
            "            <ProductId xsi:type=\"xsd:string\">PID</ProductId>\n" +
            "            <Qty xsi:type=\"xsd:string\">QTY</Qty>\n" +
            "            <UpdateTime xsi:type=\"xsd:string\">UPDATETIME</UpdateTime>\n" +
            "            </item>\n" +
            "         </ProductIdArr>\n" +
            "      </urn:StockAdjustment>\n" +
            "   </soapenv:Body>\n" +
            "</soapenv:Envelope>";

    public void updateInventoryToScm(ScmInventoryUpdateRequest scmInventoryUpdateRequest) throws ApplicationException {
        if (scmInventoryUpdateRequest.getEventTime() == null) {
            scmInventoryUpdateRequest.setEventTime(new Date());
        }
        long updateTime = scmInventoryUpdateRequest.getEventTime().getTime() / 1000L;
        String newRequest = request.replace("PID", "" + scmInventoryUpdateRequest.getProductId())
                .replace("QTY", "" + scmInventoryUpdateRequest.getQuantity())
                .replace("UPDATETIME", "" + updateTime);
        try {
            makeVsmCall(newRequest);
        } catch (Exception ex) {
            logger.error("[updateInventoryToScm] exception Occured : productId: {} quantity : {} cause : {}"
                    , scmInventoryUpdateRequest.getProductId(), scmInventoryUpdateRequest.getQuantity(), ex.getMessage());
            throw new ApplicationException(ex.getMessage(), ex);
        }
    }

    @Timed
    public String makeVsmCall(String request) throws ApplicationException {
        logger.info("Request for SCM update is {}", request);
        String response;
        RestTemplate template = new RestTemplate();
        SimpleClientHttpRequestFactory rf = new SimpleClientHttpRequestFactory();
        rf.setReadTimeout(vsmTimeout);
        rf.setConnectTimeout(vsmTimeout);
        template.setRequestFactory(rf);
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.TEXT_XML);
            HttpEntity<String> httpEntity = new HttpEntity(request, httpHeaders);
            ResponseEntity<String> responseEntity = template.exchange(vsmWsdlUrl, HttpMethod.POST,
                    httpEntity, String.class, (Object) null);
            response = responseEntity.getStatusCode() == HttpStatus.OK &&
                    responseEntity.getBody() != null ? responseEntity.getBody() : null;
            logger.info("Response from SCM , code is  {} and response is {}",
                    responseEntity.getStatusCodeValue(), responseEntity.getBody());
            if (!StringUtils.hasLength(response)) {
                errorCounter.increment();
                throw new ApplicationException("null response from vms inventory update call");
            }
        } catch (Exception e) {
            errorCounter.increment();
            logger.error("[makeVsmCall] exception occurred for request: {} cause: {}", request, e.getMessage());
            NewRelicUtil.logSCMFailure(request, e, logger);
            throw e;
        }
        return response;
    }
}
