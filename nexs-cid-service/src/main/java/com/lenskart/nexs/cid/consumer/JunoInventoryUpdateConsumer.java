package com.lenskart.nexs.cid.consumer;

import com.lenskart.nexs.cid.constant.KafkaConstants;
import com.lenskart.nexs.cid.constant.MetricConstants;
import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.cid.util.ObjectHelper;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.ims.request.InventoryUpdateRequest;
import com.newrelic.api.agent.Trace;
import io.micrometer.core.annotation.Timed;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.listener.BatchListenerFailedException;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import com.lenskart.nexs.cid.service.JunoInventoryUpdateService;

import java.text.MessageFormat;
import java.util.*;


import java.util.Objects;
@Component
@ConditionalOnProperty(name = "nexs.cid.consumer.enabled", havingValue = "true")
public class JunoInventoryUpdateConsumer {
    private final Counter errorCounter  = Metrics.counter(MetricConstants.JUNO_INVENTORY_UPDATE_CONSUMER, "result", "failure");

    @CustomLogger
    private Logger logger;
    @Setter(onMethod__ = {@Autowired})
    private JunoInventoryUpdateService junoInventoryUpdateService;

    @Value("${juno.inventory.update.enable}")
    private boolean junoInventoryUpdateEnable;

    @Timed
    @Trace(dispatcher = true)
    @Transactional(rollbackFor = Exception.class)
    @KafkaListener(
            topics = KafkaConstants.NEXS_CID_JUNO_INVENTORY_SYNC_TOPIC,
            groupId = KafkaConstants.NEXS_CID_JUNO_KAFKA_GROUP_ID,
            containerFactory = "kafkaListenerBatchContainerFactory"
    )
    public void listen(@Payload List<String> payload) throws Exception {
        logger.info("started processing juno inventory update request for payload: {}", payload);
        List<InventoryUpdateRequest>  inventoryUpdateRequestList = new ArrayList<>();
        try {
            for (String request : payload) {
                InventoryUpdateRequest inventoryUpdateRequest = new InventoryUpdateRequest();
                inventoryUpdateRequest = ObjectHelper.readValue(request, InventoryUpdateRequest.class);
                if (Objects.isNull(inventoryUpdateRequest)) {
                    throw new ApplicationException("invalid event payload");
                }

                inventoryUpdateRequestList.add(inventoryUpdateRequest);
            }

            if (junoInventoryUpdateEnable) {
                junoInventoryUpdateService.checkUnSyncedCountAndSendUpdateToJuno(inventoryUpdateRequestList);
            }
        }catch(Exception e){
                errorCounter.increment();
                String errorMessage = MessageFormat.format("error while processing juno inventory update for {0}", payload);
                logger.error(errorMessage, e);
                throw new ApplicationException(errorMessage, e);
        }
    }
}