package com.lenskart.nexs.cid.entity;

import com.lenskart.nexs.commons.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.util.LinkedHashSet;
import java.util.Set;

@Getter
@Setter
@ToString(callSuper = true)
@Entity
@Audited
@AuditOverride(forClass = BaseEntity.class)
@Table(name = "warehouse_blocked_inventory")
public class WarehouseBlockedInventory extends BaseEntity {

    @Column(name = "pid", nullable = false)
    private Long productId;

    @Column(name = "facility", nullable = false, length = 50)
    private String facility;

    @Column(name = "quantity", nullable = false, length = 50)
    private Integer quantity;

    @Column(name = "legal_owner",length = 50)
    private String legalOwner;
    @Column(name = "unfulfillable_qty")
    private Integer unfulfillableQty;

}
