package com.lenskart.nexs.cid.mapper;


import com.lenskart.nexs.cid.dto.WarehouseInventoryDto;
import com.lenskart.nexs.ims.request.InventoryUpdateRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface InvUpdateRequestToWhInvDtoMapper {

    @Mapping(source = "eventTime", target = "reconciliationEventTime")
    WarehouseInventoryDto map(InventoryUpdateRequest inventoryUpdateRequest);

}
