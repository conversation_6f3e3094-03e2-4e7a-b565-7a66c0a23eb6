package com.lenskart.nexs.cid.entity;

import com.lenskart.nexs.commons.entity.BaseEntity;
import com.lenskart.nexs.ims.model.Availability;
import com.lenskart.nexs.ims.model.Condition;
import com.lenskart.nexs.ims.model.Status;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;

@Getter
@Setter
@ToString(callSuper = true)
@Entity
@Table(name = "storefront_inventory")
public class StorefrontInventory extends BaseEntity {

    @Column(name = "pid", nullable = false)
    private Long productId;

    @Column(name = "quantity", nullable = false, length = 50)
    private Integer quantity;

    @Column(name = "buffered_quantity", length = 50)
    private Integer bufferedQuantity;

    @Enumerated(EnumType.STRING)
    @Column(name = "`condition`", nullable = false, length = 50)
    private Condition condition;

    @Enumerated(EnumType.STRING)
    @Column(name = "availability", nullable = false, length = 50)
    private Availability availability;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 50)
    private Status status;
    @Column(name ="legal_owner",length=8,nullable = false)
    private String legalOwner;
}
