package com.lenskart.nexs.cid.repository.impl;

import com.lenskart.nexs.cid.repository.CustomElasticsearchRepository;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.IndexQuery;
import org.springframework.data.elasticsearch.core.query.IndexQueryBuilder;

public class CustomElasticsearchRepositoryImpl<T> implements CustomElasticsearchRepository<T> {

    private final ElasticsearchOperations operations;

    public CustomElasticsearchRepositoryImpl(ElasticsearchOperations operations) {
        this.operations = operations;
    }

    @Override
    public T saveWithoutRefresh(T entity) {
        IndexQuery query = new IndexQueryBuilder().withObject(entity).build();
        operations.index(query, getIndexCoordinates(entity));
        return entity;
    }

    private IndexCoordinates getIndexCoordinates(T entity) {
        return operations.getElasticsearchConverter().getMappingContext()
                .getRequiredPersistentEntity(entity.getClass())
                .getIndexCoordinates();
    }
}
