package com.lenskart.nexs.cid.repository;

import com.lenskart.nexs.cid.entity.JunoAuditLog;
import com.lenskart.nexs.cid.enums.JunoApiStatus;
import com.lenskart.nexs.commons.repository.BaseRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * Repository for JunoAuditLog entity
 */
@Repository
public interface JunoAuditLogRepository extends BaseRepository<JunoAuditLog> {

    /**
     * Find audit logs by correlation ID
     */
    List<JunoAuditLog> findByCorrelationIdOrderByCreatedAtDesc(String correlationId);

    /**
     * Find audit logs by product ID
     */
    List<JunoAuditLog> findByProductIdOrderByCreatedAtDesc(Integer productId);

    /**
     * Find audit logs by facility code
     */
    List<JunoAuditLog> findByFacilityCodeOrderByCreatedAtDesc(String facilityCode);

    /**
     * Find audit logs by legal owner
     */
    List<JunoAuditLog> findByLegalOwnerOrderByCreatedAtDesc(String legalOwner);

    /**
     * Find failed audit logs within a time range
     */
    @Query("SELECT j FROM JunoAuditLog j WHERE j.status = 'FAILED' AND j.createdAt BETWEEN :startDate AND :endDate ORDER BY j.createdAt DESC")
    List<JunoAuditLog> findFailedLogsBetweenDates(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
}
