package com.lenskart.nexs.cid.repository;

import com.lenskart.nexs.cid.entity.JunoAuditLog;
import com.lenskart.nexs.cid.enums.JunoApiStatus;
import com.lenskart.nexs.commons.repository.BaseRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * Repository for JunoAuditLog entity
 */
@Repository
public interface JunoAuditLogRepository extends BaseRepository<JunoAuditLog> {

    /**
     * Find audit logs by correlation ID
     */
    List<JunoAuditLog> findByCorrelationIdOrderByCreatedAtDesc(String correlationId);

    /**
     * Find audit logs by status
     */
    Page<JunoAuditLog> findByStatusOrderByCreatedAtDesc(JunoApiStatus status, Pageable pageable);

    /**
     * Find audit logs by date range
     */
    Page<JunoAuditLog> findByCreatedAtBetweenOrderByCreatedAtDesc(Date startDate, Date endDate, Pageable pageable);

    /**
     * Find audit logs by product ID (searches in productIds field)
     */
    @Query("SELECT j FROM JunoAuditLog j WHERE j.productIds LIKE %:productId% ORDER BY j.createdAt DESC")
    Page<JunoAuditLog> findByProductId(@Param("productId") String productId, Pageable pageable);

    /**
     * Find audit logs by facility code (searches in facilityCodes field)
     */
    @Query("SELECT j FROM JunoAuditLog j WHERE j.facilityCodes LIKE %:facilityCode% ORDER BY j.createdAt DESC")
    Page<JunoAuditLog> findByFacilityCode(@Param("facilityCode") String facilityCode, Pageable pageable);

    /**
     * Find audit logs by legal owner (searches in legalOwners field)
     */
    @Query("SELECT j FROM JunoAuditLog j WHERE j.legalOwners LIKE %:legalOwner% ORDER BY j.createdAt DESC")
    Page<JunoAuditLog> findByLegalOwner(@Param("legalOwner") String legalOwner, Pageable pageable);

    /**
     * Find failed audit logs within a time range
     */
    @Query("SELECT j FROM JunoAuditLog j WHERE j.status = 'FAILED' AND j.createdAt BETWEEN :startDate AND :endDate ORDER BY j.createdAt DESC")
    List<JunoAuditLog> findFailedLogsBetweenDates(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * Count logs by status within a date range
     */
    @Query("SELECT COUNT(j) FROM JunoAuditLog j WHERE (:status IS NULL OR j.status = :status) AND j.createdAt BETWEEN :startDate AND :endDate")
    Long countByStatusAndDateRange(@Param("status") JunoApiStatus status, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * Count all logs within a date range
     */
    @Query("SELECT COUNT(j) FROM JunoAuditLog j WHERE j.createdAt BETWEEN :startDate AND :endDate")
    Long countByDateRange(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * Find logs with high retry count
     */
    @Query("SELECT j FROM JunoAuditLog j WHERE j.retryCount >= :minRetryCount ORDER BY j.retryCount DESC, j.createdAt DESC")
    List<JunoAuditLog> findHighRetryLogs(@Param("minRetryCount") Integer minRetryCount);
}
