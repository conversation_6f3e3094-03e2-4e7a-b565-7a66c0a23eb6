package com.lenskart.nexs.cid.repository;

import com.lenskart.nexs.cid.entity.JunoAuditLog;
import com.lenskart.nexs.commons.repository.BaseRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository for JunoAuditLog entity
 */
@Repository
public interface JunoAuditLogRepository extends BaseRepository<JunoAuditLog> {

    /**
     * Find audit logs by correlation ID
     * Used by JunoAuditService to update all records for a correlation ID
     */
    List<JunoAuditLog> findByCorrelationIdOrderByCreatedAtDesc(String correlationId);
}
