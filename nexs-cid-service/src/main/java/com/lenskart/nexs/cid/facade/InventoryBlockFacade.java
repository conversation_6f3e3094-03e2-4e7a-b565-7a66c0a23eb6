package com.lenskart.nexs.cid.facade;

import com.lenskart.nexs.cid.connector.CatalogOpsConnector;
import com.lenskart.nexs.cid.constant.CommonConstants;
import com.lenskart.nexs.cid.constant.MetricConstants;
import com.lenskart.nexs.cid.dto.WarehouseBlockedOrderItemMetaDto;
import com.lenskart.nexs.cid.dto.WarehouseBlockedInventoryDto;
import com.lenskart.nexs.cid.dto.WarehouseBlockedOrderItemDto;
import com.lenskart.nexs.cid.enums.FulfillableStatus;
import com.lenskart.nexs.cid.enums.WarehouseBlockedOrderItemStatus;
import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.cid.exception.ItemBlockedInDifferentFacilityException;
import com.lenskart.nexs.cid.exception.OrderPartiallyReleasedException;
import com.lenskart.nexs.cid.exception.UffException;
import com.lenskart.nexs.cid.request.InventoryBlockRequestWrapper;
import com.lenskart.nexs.cid.request.StockBlockOrderItemRequest;
import com.lenskart.nexs.cid.request.StockBlockOrderRequest;
import com.lenskart.nexs.cid.response.StockBlockOrderItemResponse;
import com.lenskart.nexs.cid.response.StockBlockOrderResponse;
import com.lenskart.nexs.cid.service.WarehouseBlockedInventoryService;
import com.lenskart.nexs.cid.service.WarehouseBlockedOrderItemMetaService;
import com.lenskart.nexs.cid.service.WarehouseBlockedOrderItemService;
import com.lenskart.nexs.cid.util.WarehouseBlockedInventoryUtil;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.ims.request.*;
import com.lenskart.nexs.ims.response.OrderItemStockCheckResponse;
import com.lenskart.nexs.ims.response.StockCheckResponse;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicInteger;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.lenskart.nexs.cid.util.WarehouseBlockedInventoryUtil.*;

@Component
public class InventoryBlockFacade {

    private final Counter errorCounter  = Metrics.counter(MetricConstants.INVENTORY_BLOCK_UPDATE, "result", "failure");

    @CustomLogger
    private Logger logger;

    @Setter(onMethod__ = {@Autowired})
    private StringRedisTemplate stringRedisTemplate;

    @Setter(onMethod__ = {@Autowired})
    private CatalogOpsConnector catalogOpsConnector;
    @Setter(onMethod__ = {@Autowired})
    private InventoryOptimisticRetryFacade inventoryOptimisticRetryFacade;

    @Setter(onMethod__ = {@Autowired})
    private InventoryBlockRetryFacade inventoryBlockRetryFacade;

    @Setter(onMethod__ = {@Autowired})
    private WarehouseBlockedOrderItemService warehouseBlockedOrderItemService;

    @Setter(onMethod__ = {@Autowired})
    private WarehouseBlockedInventoryService warehouseBlockedInventoryService;

    @Setter(onMethod__ = {@Autowired})
    private WarehouseBlockedOrderItemMetaService warehouseBlockedOrderItemMetaService;

    @Value("#{'${nexs.loyalty.classification.list}'.split(',')}")
    private List<String> catalogClassifications;

    @Value("${nexs.cid.allowed.order.item.count}")
    private int allowedOrderItemCount;

    @Value("${nexs.cid.distributed.order.processing.limit}")
    private int distributedOrderLimit;

    @Value("${fail.fast.cid.response}")
    private boolean failFastCidResponse;

    @Value("${fail.fast.bulk.cid.response}")
    private boolean failFastBulkCidResponse;

    @Value("${different.facility.response.enable}")
    private boolean differentFacilityResponseEnable;

    @Value("${bulk.different.facility.response.enable}")
    private boolean bulkDifferentFacilityResponseEnable;

    @Value("${bulk.different.facility.uff.exception.enabled:true}")
    private boolean bulkDifferentFacilityUffExceptionEnabled;

    @Value("${partial.release.check.while.block.enabled:true}")
    private boolean partialReleaseCheckWhileBlockEnabled;

    @Value("#{'${nexs.carry.bag.pid.list}'.split(',')}")
    private Set<Integer> carryBagPidSet;

    private static final String DISTRIBUTED_ORDER_PROCESSING = "DISTRIBUTED_ORDER_PROCESSING";

    public void performInventoryCheckAndBlock(OrderStockCheckRequest orderStockCheckRequest, List<OrderItemStockCheckResponse> orderItemStockCheckResponses) throws ApplicationException {
        if (orderStockCheckRequest.getOrderItemStockCheckList().size() > 1) {
            logger.error("[performStockBlockOperation] Bad Request, multiple shipments are there for update", orderStockCheckRequest);
            errorCounter.increment();
            throw new ApplicationException("Bad Request, multiple shipments are there for update. We don't allow multiple shipments update at once.");
        }
        logger.info("[{}, performInventoryCheckAndBlock] The request received is {} and the isUnfulfillableBlockingAllowed is {}",this.getClass().getSimpleName(),orderStockCheckRequest, orderStockCheckRequest.isUnfulfillableBlockingAllowed());
        performInventoryBlockAtOrderLevel(orderItemStockCheckResponses, orderStockCheckRequest.getOrderItemStockCheckList().get(0), orderStockCheckRequest.isUnfulfillableBlockingAllowed());
    }

    private void performInventoryBlockAtOrderLevel(List<OrderItemStockCheckResponse> orderItemStockCheckResponses, OrderItemStockCheck orderItemStockCheck, boolean isUnfulfillableBlockingAllowed) throws ApplicationException {
        List<StockCheckResponse> stockCheckResponses = new ArrayList<>();
        OrderItemStockCheckResponse orderItemStockCheckResponse = OrderItemStockCheckResponse.builder()
                .orderId(orderItemStockCheck.getOrderId()).build();
        Map<Integer, WarehouseBlockedOrderItemDto> existingOrderItemMapAtOrderLevel =
                warehouseBlockedOrderItemService.getExistingWarehouseBlockedInventoryMapForRequest(orderItemStockCheck.getOrderId());

        try {
            if (partialReleaseCheckWhileBlockEnabled) {
                validateExistingOrderItemMapAtItemLevel(existingOrderItemMapAtOrderLevel, orderItemStockCheck);
            }
            List<InventoryBlockRequestWrapper> inventoryBlockRequestAtOrderLevel = getInventoryBlockRequestAtOrderLevel(orderItemStockCheck, stockCheckResponses, existingOrderItemMapAtOrderLevel, isUnfulfillableBlockingAllowed);
            inventoryOptimisticRetryFacade.verifyAvailableInventoryAndBlockStock(inventoryBlockRequestAtOrderLevel, stockCheckResponses, isUnfulfillableBlockingAllowed, orderItemStockCheck.getStockCheckList().get(0).getEntityType());
        } catch (UffException uffException){
            logger.error("[performInventoryBlockAtOrderLevel] uff check failed for request {}", orderItemStockCheck, uffException);
        } catch (Exception e) {
            logger.error("[performInventoryBlockAtOrderLevel] Stock Check failed for request {}", orderItemStockCheck, e);
            errorCounter.increment();
            if (failFastCidResponse) {
                throw new ApplicationException("Exception while blocking inventory in cid", e);
            }
        }

        orderItemStockCheckResponse.setStockCheckList(stockCheckResponses);
        orderItemStockCheckResponses.add(orderItemStockCheckResponse);
    }

    private void validateExistingOrderItemMapAtItemLevel(Map<Integer, WarehouseBlockedOrderItemDto> existingOrderItemMapAtOrderLevel, OrderItemStockCheck orderItemStockCheck) throws OrderPartiallyReleasedException {
        List<Integer> orderItemList = orderItemStockCheck.getStockCheckList().stream()
                .flatMap(stockCheck -> stockCheck.getOrderItems().stream())
                .collect(Collectors.toList());
        String requestFacility = orderItemStockCheck.getStockCheckList().get(0).getFacility();
        Set<String> distinctStatus = new HashSet<>();
        String blockedFacility = null;
        for (Map.Entry<Integer, WarehouseBlockedOrderItemDto> entry : existingOrderItemMapAtOrderLevel.entrySet()) {
            if (orderItemList.contains(entry.getKey()) && !requestFacility.equalsIgnoreCase(entry.getValue().getWarehouseBlockedInventoryDto().getFacility())) {
                if (WarehouseBlockedOrderItemStatus.BLOCKED.equals(entry.getValue().getWarehouseBlockedOrderItemStatus())) {
                    blockedFacility = entry.getValue().getWarehouseBlockedInventoryDto().getFacility();
                }
                distinctStatus.add(entry.getValue().getWarehouseBlockedOrderItemStatus().name());
            }
        }
        if (!CollectionUtils.isEmpty(distinctStatus) && distinctStatus.size() > 1) {
            logger.error("Item is already blocked in other facility for request {}", orderItemStockCheck);
            throw new OrderPartiallyReleasedException("ORDER_PARTIAL_RELEASED_IN_DIFFERENT_FACILITY", "Order partially released in other facility", blockedFacility);
        }
    }

    private List<InventoryBlockRequestWrapper> getInventoryBlockRequestAtOrderLevel(OrderItemStockCheck orderItemStockCheck, List<StockCheckResponse> stockCheckResponses, Map<Integer, WarehouseBlockedOrderItemDto> existingOrderItemMapAtOrderLevel, boolean isUnfulfillableBlockingAllowed) throws ApplicationException {
        List<InventoryBlockRequestWrapper> inventoryBlockRequestAtOrderLevel = new ArrayList<>();
        for (StockCheck stockCheckRequest : orderItemStockCheck.getStockCheckList()) {
            WarehouseBlockedInventoryUtil.setDefaultLegalOwnerBasedOnFacility(stockCheckRequest, stockCheckResponses);
            InventoryBlockRequestWrapper inventoryBlockRequestAtPidLevel = validateAndFetchPidLevelRequest(orderItemStockCheck.getOrderId(), stockCheckRequest, existingOrderItemMapAtOrderLevel, stockCheckResponses, isUnfulfillableBlockingAllowed);
            if (inventoryBlockRequestAtPidLevel != null) {
                inventoryBlockRequestAtOrderLevel.add(inventoryBlockRequestAtPidLevel);
            }
        }
        return inventoryBlockRequestAtOrderLevel;
    }

    private InventoryBlockRequestWrapper validateAndFetchPidLevelRequest(Integer orderId, StockCheck stockCheckRequest, Map<Integer, WarehouseBlockedOrderItemDto> existingOrderItemMapAtOrderLevel, List<StockCheckResponse> stockCheckResponses,boolean isUnfulfillableBlockingAllowed) throws ApplicationException {
        try {
            if (checkForLoyaltyPid(stockCheckRequest, stockCheckResponses)) {
                return null;
            }

            Map<Integer, WarehouseBlockedOrderItemDto> itemsToUpdate = fetchOrderItemsListToUpdate(existingOrderItemMapAtOrderLevel, stockCheckRequest.getOrderItems(), orderId);
            if (CollectionUtils.isEmpty(itemsToUpdate)) {
                for (Integer orderItemId : stockCheckRequest.getOrderItems()) {
                    WarehouseBlockedOrderItemMetaDto warehouseBlockedOrderItemMetaDto = warehouseBlockedOrderItemMetaService.findByOrderItemId(Long.valueOf(orderItemId));
                    if (existingOrderItemMapAtOrderLevel.containsKey(orderItemId) && (Objects.isNull(warehouseBlockedOrderItemMetaDto) || FulfillableStatus.FULFILLABLE.name().equalsIgnoreCase(warehouseBlockedOrderItemMetaDto.getFulfillableStatus().name()))) {
                        if (differentFacilityResponseEnable) {
                            populateFacilityLevelStockCheckResponse(stockCheckRequest, existingOrderItemMapAtOrderLevel, stockCheckResponses, orderItemId, isUnfulfillableBlockingAllowed, true);
                        } else {
                            populateStockCheckResponse(stockCheckRequest, stockCheckResponses, true);
                        }
                    } else {
                        if (differentFacilityResponseEnable) {
                            populateFacilityLevelStockCheckResponse(stockCheckRequest, existingOrderItemMapAtOrderLevel, stockCheckResponses, orderItemId, isUnfulfillableBlockingAllowed, false);
                        } else {
                            populateStockCheckResponse(stockCheckRequest, stockCheckResponses, false);
                        }
                    }
                }
                return null;
            } else {
                InventoryBlockRequestWrapper inventoryBlockRequestAtPidLevel = new InventoryBlockRequestWrapper();
                inventoryBlockRequestAtPidLevel.setOrderId(orderId);
                inventoryBlockRequestAtPidLevel.setStockCheckRequest(stockCheckRequest);
                inventoryBlockRequestAtPidLevel.setItemsToUpdate(itemsToUpdate);

                return inventoryBlockRequestAtPidLevel;
            }
        } catch (ItemBlockedInDifferentFacilityException e) {
            logger.error("[InventoryBlockFacade -> validateAndFetchPidLevelRequest] Item is already blocked in other facility for pid {}", stockCheckRequest.getPid(), e);
            throw e;
        } catch (Exception e) {
            logger.error("[performStockBlockOperation -> validateAndFetchPidLevelRequest] Bad Request, pid {} is Unfulfillable", stockCheckRequest.getPid());
            if (isUnfulfillableBlockingAllowed && differentFacilityResponseEnable) {
                throw new ItemBlockedInDifferentFacilityException("ITEM_BLOCKED_IN_DIFFERENT_FACILITY", "Item is already blocked in other facility");
            }
            populateStockCheckResponse(stockCheckRequest, stockCheckResponses, false);
            return null;
        }
    }

    private void populateFacilityLevelStockCheckResponse(StockCheck stockCheckRequest, Map<Integer, WarehouseBlockedOrderItemDto> existingOrderItemMapAtOrderLevel, List<StockCheckResponse> stockCheckResponses, Integer orderItemId, boolean isUnfulfillableBlockingAllowed, boolean fulfillability) throws Exception {
        WarehouseBlockedOrderItemDto warehouseBlockedOrderItemDto = existingOrderItemMapAtOrderLevel.get(orderItemId);
        if (warehouseBlockedOrderItemDto.getWarehouseBlockedInventoryDto().getFacility().equals(stockCheckRequest.getFacility())) {
            populateStockCheckResponse(stockCheckRequest, stockCheckResponses, fulfillability);
        } else {
                throw new ItemBlockedInDifferentFacilityException("ITEM_BLOCKED_IN_DIFFERENT_FACILITY", "Item is already blocked in other facility",
                        warehouseBlockedOrderItemDto.getWarehouseBlockedInventoryDto().getFacility());
        }
    }

    private boolean checkForLoyaltyPid(StockCheck stockCheckRequest, List<StockCheckResponse> stockCheckResponses) throws ApplicationException {
        try {
            Set<Long> loyaltyPidSet = new HashSet<>();
            fetchLoyaltyProducts(loyaltyPidSet);

            if (loyaltyPidSet.contains(Long.valueOf(stockCheckRequest.getPid())) || carryBagPidSet.contains(stockCheckRequest.getPid())) {
                logger.debug("[performStockBlockOperation -> checkForLoyaltyPid] request PID {} is a loyalty or carry bag PID", stockCheckRequest.getPid());
                populateStockCheckResponse(stockCheckRequest, stockCheckResponses, true);
                return true;
            }

            logger.debug("[performStockBlockOperation -> checkForLoyaltyPid] request PID {} is not part of loyalty PIDs List {}", stockCheckRequest.getPid(), loyaltyPidSet);
            return false;
        } catch (Exception e) {
            logger.error("[performStockBlockOperation -> checkForLoyaltyPid] Loyalty PID check failed for pid " + stockCheckRequest.getPid(), e);
            throw new ApplicationException("Loyalty PID check failed for pid " + stockCheckRequest.getPid());
        }
    }

    private void fetchLoyaltyProducts(Set<Long> loyaltyPidSet) throws Exception {
        logger.debug("[performStockBlockOperation -> fetchLoyaltyProducts] going to fetch products list by classifications {}", catalogClassifications);

        List<Product> productListByClassifications = catalogOpsConnector.findByClassification(catalogClassifications);
        for (Product product : productListByClassifications) {
            loyaltyPidSet.add(product.getProductId());
        }
        logger.debug("[performStockBlockOperation -> fetchLoyaltyProducts] list of loyalty PIDs is {}", loyaltyPidSet);
    }

    private Map<Integer, WarehouseBlockedOrderItemDto> fetchOrderItemsListToUpdate(Map<Integer, WarehouseBlockedOrderItemDto> existingOrderItemMapAtOrderLevel, List<Integer> orderItems, Integer orderId) throws ApplicationException {
        Map<Integer, WarehouseBlockedOrderItemDto> itemsToUpdate = new HashMap<>();
        for (Integer orderItemId : orderItems) {
            if (existingOrderItemMapAtOrderLevel.containsKey(orderItemId)) {
                WarehouseBlockedOrderItemDto warehouseBlockedOrderItemDto = existingOrderItemMapAtOrderLevel.get(orderItemId);
                if (WarehouseBlockedOrderItemStatus.BLOCKED.name().equals(warehouseBlockedOrderItemDto.getWarehouseBlockedOrderItemStatus().name())) {
                    logger.info("[filterOrderItemListToUpdate] stock is already blocked for order item {}", warehouseBlockedOrderItemDto.getOrderItemId());
                    continue;
                }
            } else {
                WarehouseBlockedOrderItemDto warehouseBlockedOrderItemDto = warehouseBlockedOrderItemService.findBySearchTerms("orderItemId.eq:" + orderItemId);
                if (!ObjectUtils.isEmpty(warehouseBlockedOrderItemDto)
                        && !warehouseBlockedOrderItemDto.getOrderId().equals(Long.valueOf(orderId))
                ) {
                    logger.error("[performStockBlockOperation] Bad Request, orderItemId {} is already blocked for different orderId {}", orderItemId, warehouseBlockedOrderItemDto.getOrderId());
                    throw new ApplicationException("orderItemId " + orderItemId + " is already blocked for different orderId " + warehouseBlockedOrderItemDto.getOrderId());
                }
            }
            itemsToUpdate.put(orderItemId, existingOrderItemMapAtOrderLevel.get(orderItemId));
        }

        return itemsToUpdate;
    }

    public StockBlockOrderResponse stockBlockAvailableInventory(StockBlockOrderRequest stockBlockOrderRequest) throws ApplicationException {
        int orderItemsCount = getOrderItemsCount(stockBlockOrderRequest);
        logger.info("[stockBlockAvailableInventory] order item total count {}", orderItemsCount);
        if (orderItemsCount > allowedOrderItemCount) {
            logger.error("[stockBlockAvailableInventory] has more than {} items in request {}", allowedOrderItemCount, stockBlockOrderRequest);
            throw new ApplicationException("Bad Request,more than " + allowedOrderItemCount + " items in stockBlockOrderRequest");
        }
        Map<Integer, WarehouseBlockedOrderItemDto> existingOrderItemMapAtOrderLevel =
                warehouseBlockedOrderItemService.getExistingWarehouseBlockedInventoryMapForRequest(stockBlockOrderRequest.getOrderId());
        List<StockBlockOrderItemResponse> stockBlockResponseList = new ArrayList<>();
        for (StockBlockOrderItemRequest stockBlockOrderItemRequest : stockBlockOrderRequest.getItems()) {
            checkAndBlockInventory(stockBlockOrderRequest, existingOrderItemMapAtOrderLevel, stockBlockResponseList, stockBlockOrderItemRequest);
        }
        return getStockBlockOrderResponse(stockBlockOrderRequest, stockBlockResponseList);
    }

    private StockBlockOrderResponse getStockBlockOrderResponse(StockBlockOrderRequest stockBlockOrderRequest, List<StockBlockOrderItemResponse> stockBlockResponseList) {
        StockBlockOrderResponse stockBlockOrderResponse = new StockBlockOrderResponse();
        stockBlockOrderResponse.setFacility(stockBlockOrderRequest.getFacility());
        stockBlockOrderResponse.setOrderId(stockBlockOrderRequest.getOrderId());
        stockBlockOrderResponse.setItems(stockBlockResponseList);
        return stockBlockOrderResponse;
    }

    private void checkAndBlockInventory(StockBlockOrderRequest stockBlockOrderRequest, Map<Integer, WarehouseBlockedOrderItemDto> existingOrderItemMapAtOrderLevel, List<StockBlockOrderItemResponse> stockBlockResponseList, StockBlockOrderItemRequest stockBlockOrderItemRequest) throws ApplicationException {
        if (isLoyaltyPid(stockBlockOrderItemRequest.getProductId(), stockBlockOrderItemRequest, stockBlockResponseList)) {
            return;
        }
        try {
            Map<Integer, WarehouseBlockedOrderItemDto> itemsToUpdate = fetchOrderItemsListToUpdate(existingOrderItemMapAtOrderLevel, stockBlockOrderItemRequest.getOrderItems(), stockBlockOrderRequest.getOrderId());
            if (CollectionUtils.isEmpty(itemsToUpdate)) {
                logger.info("[checkAndBlockInventory] order items are already blocked for request {} bulkDifferentFacilityResponseEnable: {}", stockBlockOrderItemRequest, bulkDifferentFacilityResponseEnable);
                if (bulkDifferentFacilityResponseEnable) {
                    populateBlockedOrderItemResponse(stockBlockOrderRequest.getFacility(), stockBlockResponseList, stockBlockOrderItemRequest, stockBlockOrderItemRequest.getOrderItems(), stockBlockOrderRequest.isUnfulfillableBlockingAllowed());
                } else {
                    populateStockBlockResponseList(stockBlockOrderItemRequest.getProductId(), stockBlockOrderItemRequest.getOrderItems(), true, stockBlockResponseList);
                }
            } else {
                List<Integer> alreadyBlockedOrderItems = stockBlockOrderItemRequest.getOrderItems().stream()
                        .filter(items -> !itemsToUpdate.containsKey(items))
                        .collect(Collectors.toList());
                if (bulkDifferentFacilityResponseEnable) {
                    populateBlockedOrderItemResponse(stockBlockOrderRequest.getFacility(), stockBlockResponseList, stockBlockOrderItemRequest, alreadyBlockedOrderItems, stockBlockOrderRequest.isUnfulfillableBlockingAllowed());
                } else {
                    populateStockBlockResponseList(stockBlockOrderItemRequest.getProductId(), alreadyBlockedOrderItems, true, stockBlockResponseList);
                }
                inventoryBlockRetryFacade.verifyAndBlockAvailableInventory(stockBlockOrderRequest, itemsToUpdate, stockBlockResponseList, stockBlockOrderItemRequest);
            }
        } catch (Exception e) {
            logger.error("[checkAndBlockInventory] Stock Check failed for request {}", stockBlockOrderItemRequest, e);
            populateStockBlockResponseList(stockBlockOrderItemRequest.getProductId(), stockBlockOrderItemRequest.getOrderItems(), false, stockBlockResponseList);
            if (failFastBulkCidResponse) {
                throw new ApplicationException("Exception while blocking inventory in cid", e);
            }
        }
    }

    private void populateBlockedOrderItemResponse(String requestedFacility, List<StockBlockOrderItemResponse> stockBlockResponseList, StockBlockOrderItemRequest stockBlockOrderItemRequest, List<Integer> alreadyBlockedOrderItems, boolean isUnfulfillableBlockingAllowed) {
        List<Integer> itemsBlockedDifferentFacility = new ArrayList<>();
        List<Integer> itemsBlockedSameFacility = new ArrayList<>();
        if (!CollectionUtils.isEmpty(alreadyBlockedOrderItems)) {
            populateBlockedFacilityList(itemsBlockedDifferentFacility, itemsBlockedSameFacility, alreadyBlockedOrderItems, requestedFacility, isUnfulfillableBlockingAllowed);
            populateStockBlockResponseList(stockBlockOrderItemRequest.getProductId(), itemsBlockedDifferentFacility, false, stockBlockResponseList);
            populateStockBlockResponseList(stockBlockOrderItemRequest.getProductId(), itemsBlockedSameFacility, true, stockBlockResponseList);
        }
    }

    private void populateBlockedFacilityList(List<Integer> itemsBlockedDifferentFacility, List<Integer> itemsBlockedSameFacility, List<Integer> alreadyBlockedOrderItems, String requestedFacility, boolean isUnfulfillableBlockingAllowed) {
        String blockedItems = alreadyBlockedOrderItems.stream().map(String::valueOf).collect(Collectors.joining(","));
        List<WarehouseBlockedOrderItemDto> warehouseBlockedOrderItemDtoList = warehouseBlockedOrderItemService.search("orderItemId.in:" + blockedItems);
        for (WarehouseBlockedOrderItemDto warehouseBlockedOrderItemDto : warehouseBlockedOrderItemDtoList) {
            logger.debug("[populateBlockedFacilityList] warehouseBlockedOrderItemDto {}", warehouseBlockedOrderItemDto);
            if (warehouseBlockedOrderItemDto.getWarehouseBlockedInventoryDto().getFacility().equals(requestedFacility)) {
                itemsBlockedSameFacility.add(Math.toIntExact(warehouseBlockedOrderItemDto.getOrderItemId()));
            } else {
                logger.info("[populateBlockedFacilityList] requested facility is {} and Order is already blocked in other facility {} for orderItemId {} and orderId {} bulkDifferentFacilityUffExceptionEnabled: {} : isUnfulfillableBlockingAllowed: {}",
                        requestedFacility, warehouseBlockedOrderItemDto.getWarehouseBlockedInventoryDto().getFacility(),
                        warehouseBlockedOrderItemDto.getOrderItemId(), warehouseBlockedOrderItemDto.getOrderId(), bulkDifferentFacilityUffExceptionEnabled, isUnfulfillableBlockingAllowed);
                if(bulkDifferentFacilityUffExceptionEnabled && isUnfulfillableBlockingAllowed) {
                    logger.error("requested facility is {} and Order is already blocked in other facility {} for orderItemId {} and orderId {}",
                            requestedFacility, warehouseBlockedOrderItemDto.getWarehouseBlockedInventoryDto().getFacility(),
                            warehouseBlockedOrderItemDto.getOrderItemId(), warehouseBlockedOrderItemDto.getOrderId());
                    throw new ItemBlockedInDifferentFacilityException("ITEM_BLOCKED_IN_DIFFERENT_FACILITY", "Item is already blocked in other facility");
                }
                itemsBlockedDifferentFacility.add(Math.toIntExact(warehouseBlockedOrderItemDto.getOrderItemId()));
            }
        }
    }

    private int getOrderItemsCount(StockBlockOrderRequest stockBlockOrderRequest) {
        return (int) stockBlockOrderRequest.getItems().stream()
                .flatMap(stockBlockItem -> stockBlockItem.getOrderItems().stream())
                .mapToInt(Integer::intValue)
                .count();
    }

    private boolean isLoyaltyPid(Long pid, StockBlockOrderItemRequest stockBlockOrderItemRequest, List<StockBlockOrderItemResponse> stockBlockResponseList) {
        try {
            Set<Long> loyaltyPidSet = new HashSet<>();
            fetchLoyaltyProducts(loyaltyPidSet);

            if (loyaltyPidSet.contains(pid) || carryBagPidSet.contains(Math.toIntExact(pid))) {
                logger.debug("[checkAndBlockInventory -> isLoyaltyPid] request PID {} is a loyalty or carry bag PID", pid);
                populateStockBlockResponseList(pid, stockBlockOrderItemRequest.getOrderItems(), true, stockBlockResponseList);
                return true;
            }
            logger.debug("[checkAndBlockInventory -> isLoyaltyPid] request PID {} is not part of loyalty PIDs List {}", pid, loyaltyPidSet);
            return false;
        } catch (Exception e) {
            logger.error("[checkAndBlockInventory -> isLoyaltyPid] Loyalty PID check failed for pid " + pid, e);
            populateStockBlockResponseList(pid, stockBlockOrderItemRequest.getOrderItems(), false, stockBlockResponseList);
            return true;
        }
    }

    public void reconcileBlockedOrderInventory(List<Long> pidList, String facilityCode, String legalOwner) throws ApplicationException {
        for (Long pid : pidList) {
            WarehouseBlockedInventoryDto warehouseBlockedInventoryDto = warehouseBlockedInventoryService.findByProductIdAndFacilityAndLegalOwner(pid, facilityCode, legalOwner);
            if (!Objects.isNull(warehouseBlockedInventoryDto)) {
                String searchTerm = "warehouseBlockedInventory.id.eq:" + warehouseBlockedInventoryDto.getId() + "___warehouseBlockedOrderItemStatus.eq:" + WarehouseBlockedOrderItemStatus.BLOCKED;
                List<WarehouseBlockedOrderItemDto> warehouseBlockedOrderItemDtoList = warehouseBlockedOrderItemService.search(searchTerm);
                int orderItemQuantity = CollectionUtils.isEmpty(warehouseBlockedOrderItemDtoList) ? 0 : warehouseBlockedOrderItemDtoList.size();
                if (warehouseBlockedInventoryDto.getQuantity() != orderItemQuantity) {
                    warehouseBlockedInventoryDto.setQuantity(orderItemQuantity);
                    warehouseBlockedInventoryDto.setUpdatedBy(MDC.get(CommonConstants.USER_ID));
                    warehouseBlockedInventoryService.update(warehouseBlockedInventoryDto, warehouseBlockedInventoryDto.getId());
                    inventoryBlockRetryFacade.publishInventoryUpdateEvent(Math.toIntExact(pid), facilityCode, legalOwner);
                    logger.info("[reconcileBlockedOrderInventory] pid reconciled successfully {}", pid);
                }
            }
        }
    }

    public void verifyAndBlockInventory(OrderStockCheckRequest orderStockCheckRequest, List<OrderItemStockCheckResponse> orderItemStockCheckResponses) throws ApplicationException {
        RedisAtomicInteger redisAtomicInteger = new RedisAtomicInteger(DISTRIBUTED_ORDER_PROCESSING, stringRedisTemplate.getConnectionFactory());
        if (redisAtomicInteger.incrementAndGet() > distributedOrderLimit) {
            decrementCount(redisAtomicInteger);
            throw new ApplicationException("distributed order already processing" + orderStockCheckRequest.getOrderItemStockCheckList().get(0).getOrderId());
        }
        try {
            logger.info("[verifyAndBlockInventory] current distributed order count {}", redisAtomicInteger.get());
            performInventoryCheckAndBlock(orderStockCheckRequest, orderItemStockCheckResponses);
        }
        finally {
            decrementCount(redisAtomicInteger);
        }
    }

    private void decrementCount(RedisAtomicInteger redisAtomicInteger) {
        try {
            int currentCount = redisAtomicInteger.decrementAndGet();
            logger.info("[decrementCount][verifyAndBlockInventory] current count post decrement is {}", currentCount);
        } catch (Exception e) {
            logger.error("[decrementCount][verifyAndBlockInventory] exception while decrementing current count" + e);
        }
    }
}
