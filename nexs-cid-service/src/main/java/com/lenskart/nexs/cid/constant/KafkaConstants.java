package com.lenskart.nexs.cid.constant;

public final class KafkaConstants {

    private KafkaConstants() {}

    public static final String EVENT_IDENTIFIER_KEY = "event-identifier-key";
    public static final String MESSAGE_IDEMPOTENCY_KEY = "message-idempotency-key";
    public static final String NEXS_CID_INVENTORY_SYNC_TOPIC = "nexs-cid-inventory-sync-topic";
    public static final String NEXS_CID_SCM_INVENTORY_SYNC_TOPIC = "nexs-cid-scm-inventory-sync-topic";
    public static final String NEXS_CID_KAFKA_GROUP_ID = "nexs_cid";
    public static final String NEXS_CID_HISTORY_SYNC_TOPIC = "nexs-cid-history-sync-topic";
    public static final String NEXS_CID_JUNO_INVENTORY_SYNC_TOPIC = "nexs-cid-juno-inventory-sync-topic";
    public static final String INVENTORY_RECONCILIATION_TOPIC = "inventory-reconciliation-topic";
    public static final String NEXS_CID_JUNO_KAFKA_GROUP_ID = "nexs_cid_juno_group";
    public static final String NEXS_CID_INVENTORY_SYNC_TOPIC_P0 = "nexs-cid-inventory-sync-topic-p0";
    public static final String NEXS_CID_INVENTORY_SYNC_TOPIC_P1 = "nexs-cid-inventory-sync-topic-p1";
    public static final String NEXS_CID_KAFKA_GROUP_P0 = "nexs_cid_p0";
    public static final String NEXS_CID_KAFKA_GROUP_P1 = "nexs_cid_p1";
}
