package com.lenskart.nexs.cid.facade;

import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.cid.exception.UffException;
import com.lenskart.nexs.cid.request.InventoryBlockRequestWrapper;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.ims.request.StockCheck;
import com.lenskart.nexs.ims.response.StockCheckResponse;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.lenskart.nexs.cid.util.WarehouseBlockedInventoryUtil.populateStockCheckResponse;

@Component
public class InventoryOptimisticRetryFacade {

    @CustomLogger
    private Logger logger;

    @Setter(onMethod__ = {@Autowired})
    private InventoryBlockRetryFacade inventoryBlockRetryFacade;

    @Value("${fail.fast.cid.response}")
    private boolean failFastCidResponse;

    @Transactional(rollbackFor = Exception.class)
    public void verifyAvailableInventoryAndBlockStock(List<InventoryBlockRequestWrapper> inventoryBlockRequestAtOrderLevel, List<StockCheckResponse> stockCheckResponses, boolean isUnfulfillableBlockingAllowed, String entityType) throws ApplicationException, UffException {
        for (InventoryBlockRequestWrapper inventoryBlockRequestAtPidLevel : inventoryBlockRequestAtOrderLevel) {
            StockCheck stockCheck = inventoryBlockRequestAtPidLevel.getStockCheckRequest();

            try {
                logger.info("[verifyAvailableInventoryAndBlockStock]  {}", inventoryBlockRequestAtPidLevel);
                inventoryBlockRetryFacade.inventoryStockCheckAndBlock(stockCheckResponses, isUnfulfillableBlockingAllowed, entityType, inventoryBlockRequestAtPidLevel, stockCheck);
            } catch (Exception e) {
                logger.error("[performStockBlockOperation -> verifyAvailableInventoryAndBlockStock] Stock block for request " + stockCheck + " is failed with exception " + e.getMessage(), e);
                populateStockCheckResponse(stockCheck, stockCheckResponses, false);
                if (failFastCidResponse) {
                    throw new ApplicationException("Exception while blocking inventory in cid", e);
                }
            }
        }

        validateStockCheckResponse(stockCheckResponses,isUnfulfillableBlockingAllowed);
    }

    private void validateStockCheckResponse(List<StockCheckResponse> stockCheckResponses, boolean isUnfulfillableBlockingAllowed) throws UffException {
        if(!isUnfulfillableBlockingAllowed) {
            for (StockCheckResponse stockCheckResponse : stockCheckResponses) {
                if (!stockCheckResponse.isFulfillable()) {
                    logger.error("Stock not available for pid {} with response {}", stockCheckResponse.getPid(), stockCheckResponse);
                    throw new UffException("Stock not available for pid " + stockCheckResponse.getPid());
                }
            }
        }
    }
}
