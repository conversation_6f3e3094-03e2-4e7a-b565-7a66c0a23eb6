package com.lenskart.nexs.cid.facade;

import com.lenskart.nexs.cid.constant.CommonConstants;
import com.lenskart.nexs.cid.dto.WarehouseBlockedInventoryDto;
import com.lenskart.nexs.cid.dto.WarehouseBlockedOrderItemDto;
import com.lenskart.nexs.cid.dto.WarehouseBlockedOrderItemMetaDto;
import com.lenskart.nexs.cid.dto.WarehouseInventoryDto;
import com.lenskart.nexs.cid.enums.FulfillableStatus;
import com.lenskart.nexs.cid.enums.WarehouseBlockedOrderItemStatus;
import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.cid.request.InventoryBlockRequestWrapper;
import com.lenskart.nexs.cid.request.StockBlockOrderItemRequest;
import com.lenskart.nexs.cid.request.StockBlockOrderRequest;
import com.lenskart.nexs.cid.response.StockBlockOrderItemResponse;
import com.lenskart.nexs.cid.service.WarehouseBlockedInventoryService;
import com.lenskart.nexs.cid.service.WarehouseBlockedOrderItemMetaService;
import com.lenskart.nexs.cid.service.WarehouseBlockedOrderItemService;
import com.lenskart.nexs.cid.service.WarehouseInventoryService;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.ims.enums.CidInventoryOperation;
import com.lenskart.nexs.ims.enums.LocationType;
import com.lenskart.nexs.ims.model.Availability;
import com.lenskart.nexs.ims.model.Condition;
import com.lenskart.nexs.ims.model.Status;
import com.lenskart.nexs.ims.request.InventoryUpdateRequest;
import com.lenskart.nexs.ims.request.InventoryUpdateRequestWrapper;
import com.lenskart.nexs.ims.request.StockCheck;
import com.lenskart.nexs.ims.response.StockCheckResponse;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.hibernate.StaleObjectStateException;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.lenskart.nexs.cid.constant.CommonConstants.DEFAULT_USER;
import static com.lenskart.nexs.cid.util.WarehouseBlockedInventoryUtil.*;

@Component
public class InventoryBlockRetryFacade {

    @CustomLogger
    private Logger logger;

    @Setter(onMethod__ = {@Autowired})
    private InventoryUpdateFacade inventoryUpdateFacade;

    @Setter(onMethod__ = {@Autowired})
    private WarehouseInventoryService warehouseInventoryService;

    @Setter(onMethod__ = {@Autowired})
    private WarehouseBlockedOrderItemService warehouseBlockedOrderItemService;

    @Setter(onMethod__ = {@Autowired})
    private WarehouseBlockedInventoryService warehouseBlockedInventoryService;

    @Setter(onMethod__ = {@Autowired})
    private WarehouseBlockedOrderItemMetaService warehouseBlockedOrderItemMetaService;

    @Retryable(value = {ObjectOptimisticLockingFailureException.class, StaleObjectStateException.class}, maxAttemptsExpression = "${cid.optimistic.retry.maxAttempts:2}", backoff = @Backoff(delayExpression = "${cid.optimistic.retry.maxDelay:200}"))
    protected void inventoryStockCheckAndBlock(List<StockCheckResponse> stockCheckResponses, boolean isUnfulfillableBlockingAllowed, String entityType, InventoryBlockRequestWrapper inventoryBlockRequestAtPidLevel, StockCheck stockCheck) throws ApplicationException {
        WarehouseInventoryDto warehouseInventory = warehouseInventoryService.findByProductIdFacilityConditionAvailabilityAndStatusAndLegalOwnerAndLocationType(Long.valueOf(stockCheck.getPid()), stockCheck.getFacility(), Condition.GOOD, Availability.AVAILABLE, Status.AVAILABLE, stockCheck.getLegalOwner(), LocationType.DEFAULT);
        long totalAvailableQuantityAgainstPidAndFacility = warehouseInventory != null ? warehouseInventory.getQuantity() : 0;
        WarehouseBlockedInventoryDto warehouseBlockedInventoryDto = warehouseBlockedInventoryService.findByProductIdAndFacilityAndLegalOwner(Long.valueOf(stockCheck.getPid()), stockCheck.getFacility(), stockCheck.getLegalOwner());
        long totalBlockedQuantityAgainstPidAndFacility = warehouseBlockedInventoryDto != null ? warehouseBlockedInventoryDto.getQuantity() : 0;
        logger.info("[inventoryStockCheckAndBlock] total available quantity is {} and total blocked quantity is {} for pid {} and facility {}", totalAvailableQuantityAgainstPidAndFacility, totalBlockedQuantityAgainstPidAndFacility, stockCheck.getPid(), stockCheck.getFacility());
        totalBlockedQuantityAgainstPidAndFacility = totalBlockedQuantityAgainstPidAndFacility < 0 ? 0 : totalBlockedQuantityAgainstPidAndFacility;
        if ((stockCheck.isPriority() && totalAvailableQuantityAgainstPidAndFacility != 0 && totalAvailableQuantityAgainstPidAndFacility >= inventoryBlockRequestAtPidLevel.getItemsToUpdate().size()) ||
                (totalAvailableQuantityAgainstPidAndFacility - totalBlockedQuantityAgainstPidAndFacility >= inventoryBlockRequestAtPidLevel.getItemsToUpdate().size())) {
            logger.info("PID {} is fulfillable true for order id {} and isUnfulfillableBlockingAllowed {}", stockCheck.getPid(), inventoryBlockRequestAtPidLevel.getOrderId(), isUnfulfillableBlockingAllowed);
            saveOrUpdateWarehouseBlockedInventory(stockCheck, inventoryBlockRequestAtPidLevel.getItemsToUpdate(), inventoryBlockRequestAtPidLevel.getOrderId(), warehouseBlockedInventoryDto, entityType, FulfillableStatus.FULFILLABLE);
            publishInventoryUpdateEvent(stockCheck.getPid(), stockCheck.getFacility(), stockCheck.getLegalOwner());

            populateStockCheckResponse(stockCheck, stockCheckResponses, true);
        } else {
            logger.info("PID {} is fulfillable false for order id {} and isUnfulfillableBlockingAllowed {}", stockCheck.getPid(), inventoryBlockRequestAtPidLevel.getOrderId(), isUnfulfillableBlockingAllowed);
            if (isUnfulfillableBlockingAllowed) {
                saveOrUpdateWarehouseBlockedInventory(stockCheck, inventoryBlockRequestAtPidLevel.getItemsToUpdate(), inventoryBlockRequestAtPidLevel.getOrderId(), warehouseBlockedInventoryDto, entityType, FulfillableStatus.UNFULFILLABLE);
                publishInventoryUpdateEvent(stockCheck.getPid(), stockCheck.getFacility(), stockCheck.getLegalOwner());
            }
            populateStockCheckResponse(stockCheck, stockCheckResponses, false);
        }
    }

    protected void publishInventoryUpdateEvent(Integer pid, String facility,String legalOwner) throws ApplicationException {
        List<InventoryUpdateRequest> inventoryUpdateRequestList = new ArrayList<>();
        populateInventoryUpdateRequestWithLegalOwner(pid, facility, inventoryUpdateRequestList, CidInventoryOperation.INV_BLOCK,legalOwner);

        InventoryUpdateRequestWrapper inventoryUpdateRequestWrapper = new InventoryUpdateRequestWrapper();
        inventoryUpdateRequestWrapper.setUpdatedBy(MDC.get(CommonConstants.USER_ID));
        inventoryUpdateRequestWrapper.setInventoryUpdateRequests(inventoryUpdateRequestList);
        inventoryUpdateFacade.updateInventory(inventoryUpdateRequestWrapper);
    }

    private void saveOrUpdateWarehouseBlockedInventory(StockCheck stockCheck, Map<Integer,
            WarehouseBlockedOrderItemDto> finalOrderItemsToUpdate, Integer orderId,WarehouseBlockedInventoryDto warehouseBlockedInventoryDto, String entityType, FulfillableStatus fulfillableStatus) throws ApplicationException {
        List<WarehouseBlockedOrderItemMetaDto> warehouseBlockedOrderItemMetaDtoList = warehouseBlockedOrderItemMetaService.findAllByOrderItemId(stockCheck.getOrderItems().stream().map(String::valueOf).collect(Collectors.joining(",")));
        for (Integer orderItemId : stockCheck.getOrderItems()) {
            if (finalOrderItemsToUpdate.get(orderItemId) != null && Objects.nonNull(warehouseBlockedInventoryDto)) {
                updateExistingWarehouseBlockedInventoryData(finalOrderItemsToUpdate.get(orderItemId), stockCheck,warehouseBlockedInventoryDto, fulfillableStatus);
            } else if (finalOrderItemsToUpdate.containsKey(orderItemId)) {
                warehouseBlockedInventoryDto = createWarehouseBlockedInventoryData(stockCheck, orderItemId, orderId,
                        warehouseBlockedInventoryDto, entityType, fulfillableStatus, warehouseBlockedOrderItemMetaDtoList);
            }
        }

    }

    private void updateExistingWarehouseBlockedInventoryData(WarehouseBlockedOrderItemDto warehouseBlockedOrderItemDto, StockCheck stockCheck,
                                                             WarehouseBlockedInventoryDto warehouseBlockedInventoryDto, FulfillableStatus fulfillableStatus) {
        logger.info("[{}, updateExistingWarehouseBlockedInventoryData] for pid {} and facility {} ",this,getClass().getSimpleName() , stockCheck.getPid(), stockCheck.getFacility());

        if (warehouseBlockedInventoryDto.getQuantity() < 0) {
            warehouseBlockedInventoryDto.setQuantity(0);
        }
        warehouseBlockedInventoryDto.setUpdatedBy(Objects.isNull(MDC.get(CommonConstants.USER_ID)) ? DEFAULT_USER : MDC.get(CommonConstants.USER_ID));
        warehouseBlockedInventoryDto.setQuantity(warehouseBlockedInventoryDto.getQuantity() + 1);
        warehouseBlockedInventoryService.update(warehouseBlockedInventoryDto, warehouseBlockedInventoryDto.getId());

        warehouseBlockedOrderItemDto.setUpdatedBy(Objects.isNull(MDC.get(CommonConstants.USER_ID)) ? DEFAULT_USER : MDC.get(CommonConstants.USER_ID));
        warehouseBlockedOrderItemDto.setWarehouseBlockedInventoryDto(warehouseBlockedInventoryDto);
        warehouseBlockedOrderItemDto.setWarehouseBlockedOrderItemStatus(WarehouseBlockedOrderItemStatus.BLOCKED);
        warehouseBlockedOrderItemService.save(warehouseBlockedOrderItemDto);

        WarehouseBlockedOrderItemMetaDto warehouseBlockedOrderItemMetaDto = warehouseBlockedOrderItemMetaService.findByOrderItemId(warehouseBlockedOrderItemDto.getOrderItemId());
        if (Objects.isNull(warehouseBlockedOrderItemMetaDto)) {
            warehouseBlockedOrderItemMetaDto = new WarehouseBlockedOrderItemMetaDto();
            buildWarehouseBlockedOrderItemMetaDto(fulfillableStatus, warehouseBlockedOrderItemDto, warehouseBlockedOrderItemMetaDto);
            warehouseBlockedOrderItemMetaDto.setCreatedBy(MDC.get(CommonConstants.USER_ID));
            warehouseBlockedOrderItemMetaService.save(warehouseBlockedOrderItemMetaDto);
        } else {
            buildWarehouseBlockedOrderItemMetaDto(fulfillableStatus, warehouseBlockedOrderItemDto, warehouseBlockedOrderItemMetaDto);
            warehouseBlockedOrderItemMetaService.update(warehouseBlockedOrderItemMetaDto, warehouseBlockedOrderItemMetaDto.getId());
        }
    }


    private WarehouseBlockedInventoryDto createWarehouseBlockedInventoryData(StockCheck stockCheck, Integer orderItemId, Integer orderId, WarehouseBlockedInventoryDto warehouseBlockedInventoryDto, String entityType, FulfillableStatus fulfillableStatus, List<WarehouseBlockedOrderItemMetaDto> warehouseBlockedOrderItemMetaDtoList) {
        logger.info("[{}, createWarehouseBlockedInventoryData] for pid {} and facility {} and warehouse inventory {}",this.getClass().getSimpleName(), stockCheck.getPid(), stockCheck.getFacility(), warehouseBlockedInventoryDto);
        if (ObjectUtils.isEmpty(warehouseBlockedInventoryDto)) {
            warehouseBlockedInventoryDto = getWarehouseBlockedInventoryDtoWithLegalOwner(stockCheck.getFacility(), stockCheck.getPid(), 1, stockCheck.getLegalOwner());
        } else {
            if (warehouseBlockedInventoryDto.getQuantity() < 0) {
                warehouseBlockedInventoryDto.setQuantity(0);
            }
            warehouseBlockedInventoryDto.setQuantity(warehouseBlockedInventoryDto.getQuantity() + 1);
        }

       return constructAndUpdateWarehouseBlockedInventoryData(orderItemId, orderId, warehouseBlockedInventoryDto, entityType, fulfillableStatus, warehouseBlockedOrderItemMetaDtoList);
    }

    private WarehouseBlockedInventoryDto constructAndUpdateWarehouseBlockedInventoryData(Integer orderItemId, Integer orderId, WarehouseBlockedInventoryDto warehouseBlockedInventoryDto, String entityType, FulfillableStatus fulfillableStatus,  List<WarehouseBlockedOrderItemMetaDto> warehouseBlockedOrderItemMetaDtoList) {
        WarehouseBlockedOrderItemDto warehouseBlockedOrderItemDto = getWarehouseBlockedOrderItemDto(
                orderItemId, orderId, warehouseBlockedInventoryDto, WarehouseBlockedOrderItemStatus.BLOCKED
        );
        warehouseBlockedOrderItemDto.setEntityType(entityType);
        warehouseBlockedInventoryDto.setUpdatedBy(Objects.isNull(MDC.get(CommonConstants.USER_ID)) ? DEFAULT_USER : MDC.get(CommonConstants.USER_ID));
        warehouseBlockedInventoryDto = warehouseBlockedInventoryService.save(warehouseBlockedInventoryDto);
        warehouseBlockedOrderItemDto.setUpdatedBy(Objects.isNull(MDC.get(CommonConstants.USER_ID)) ? DEFAULT_USER : MDC.get(CommonConstants.USER_ID));
        warehouseBlockedOrderItemDto.setWarehouseBlockedInventoryDto(warehouseBlockedInventoryDto);
        warehouseBlockedOrderItemService.save(warehouseBlockedOrderItemDto);

        Optional<WarehouseBlockedOrderItemMetaDto> optionalWarehouseBlockedOrderItemMetaDto = warehouseBlockedOrderItemMetaDtoList.stream()
                .filter(itemMetaDto -> itemMetaDto.getWarehouseBlockedOrderItemId().equals(warehouseBlockedOrderItemDto.getOrderItemId())).findFirst();
        if (CollectionUtils.isEmpty(warehouseBlockedOrderItemMetaDtoList) || !optionalWarehouseBlockedOrderItemMetaDto.isPresent()) {
            WarehouseBlockedOrderItemMetaDto warehouseBlockedOrderItemMetaDto = new WarehouseBlockedOrderItemMetaDto();
            buildWarehouseBlockedOrderItemMetaDto(fulfillableStatus, warehouseBlockedOrderItemDto, warehouseBlockedOrderItemMetaDto);
            warehouseBlockedOrderItemMetaDto.setCreatedBy(MDC.get(CommonConstants.USER_ID));
            warehouseBlockedOrderItemMetaService.save(warehouseBlockedOrderItemMetaDto);
        } else {
            WarehouseBlockedOrderItemMetaDto warehouseBlockedOrderItemMetaDto = optionalWarehouseBlockedOrderItemMetaDto.get();
            buildWarehouseBlockedOrderItemMetaDto(fulfillableStatus, warehouseBlockedOrderItemDto, warehouseBlockedOrderItemMetaDto);
            warehouseBlockedOrderItemMetaService.update(warehouseBlockedOrderItemMetaDto, warehouseBlockedOrderItemMetaDto.getId());
        }

        logger.info("[constructAndUpdateWarehouseBlockedInventoryData] created entry in warehouseBlockedOrderItemService ordersItemId{}", orderItemId);
        return warehouseBlockedInventoryDto;
    }

    private void buildWarehouseBlockedOrderItemMetaDto(FulfillableStatus fulfillableStatus, WarehouseBlockedOrderItemDto warehouseBlockedOrderItemDto, WarehouseBlockedOrderItemMetaDto warehouseBlockedOrderItemMetaDto) {
        warehouseBlockedOrderItemMetaDto.setWarehouseBlockedOrderItemId(warehouseBlockedOrderItemDto.getOrderItemId());
        warehouseBlockedOrderItemMetaDto.setFulfillableStatus(fulfillableStatus);
        warehouseBlockedOrderItemMetaDto.setUpdatedBy(MDC.get(CommonConstants.USER_ID));
    }

    @Transactional(rollbackFor = Exception.class)
    @Retryable(value = {ObjectOptimisticLockingFailureException.class, StaleObjectStateException.class}, maxAttempts = 2, backoff = @Backoff(delay = 200L))
    public void verifyAndBlockAvailableInventory(StockBlockOrderRequest stockBlockOrderRequest, Map<Integer, WarehouseBlockedOrderItemDto> itemsToUpdate, List<StockBlockOrderItemResponse> stockBlockResponseList, StockBlockOrderItemRequest stockBlockOrderItemRequest) throws ApplicationException {
        WarehouseInventoryDto warehouseInventory = warehouseInventoryService.findByProductIdFacilityConditionAvailabilityAndStatusAndLegalOwnerAndLocationType(stockBlockOrderItemRequest.getProductId(), stockBlockOrderRequest.getFacility(), Condition.GOOD, Availability.AVAILABLE, Status.AVAILABLE, stockBlockOrderRequest.getLegalOwner(), LocationType.DEFAULT);
        long totalAvailableQuantityAgainstPidAndFacility = warehouseInventory != null ? warehouseInventory.getQuantity() : 0;

        WarehouseBlockedInventoryDto warehouseBlockedInventoryDto = warehouseBlockedInventoryService.findByProductIdAndFacilityAndLegalOwner(stockBlockOrderItemRequest.getProductId(), stockBlockOrderRequest.getFacility(), stockBlockOrderRequest.getLegalOwner());
        long totalBlockedQuantityAgainstPidAndFacility = warehouseBlockedInventoryDto != null ? warehouseBlockedInventoryDto.getQuantity() : 0;

        logger.info("[verifyAndBlockAvailableInventoryDto] total available quantity is {} and total blocked quantity is {} for pid {} and facility {}", totalAvailableQuantityAgainstPidAndFacility, totalBlockedQuantityAgainstPidAndFacility, stockBlockOrderItemRequest.getProductId(), stockBlockOrderRequest.getFacility());
        totalBlockedQuantityAgainstPidAndFacility = totalBlockedQuantityAgainstPidAndFacility < 0 ? 0 : totalBlockedQuantityAgainstPidAndFacility;
        processRequestAndBlockInventory(stockBlockOrderRequest, itemsToUpdate, stockBlockResponseList, stockBlockOrderItemRequest, totalAvailableQuantityAgainstPidAndFacility, totalBlockedQuantityAgainstPidAndFacility,warehouseBlockedInventoryDto);
    }

    private void processRequestAndBlockInventory(StockBlockOrderRequest stockBlockOrderRequest, Map<Integer, WarehouseBlockedOrderItemDto> itemsToUpdate,
                                                 List<StockBlockOrderItemResponse> stockBlockResponseList, StockBlockOrderItemRequest stockBlockOrderItemRequest,
                                                 long totalAvailableQuantityAgainstPidAndFacility, long totalBlockedQuantityAgainstPidAndFacility,
                                                 WarehouseBlockedInventoryDto warehouseBlockedInventoryDto) throws ApplicationException {
        if (totalAvailableQuantityAgainstPidAndFacility - totalBlockedQuantityAgainstPidAndFacility > 0) {
            long availableQuantity = totalAvailableQuantityAgainstPidAndFacility - totalBlockedQuantityAgainstPidAndFacility;
            List<Integer> fulfillableOrderItems = itemsToUpdate.keySet().stream()
                    .limit(availableQuantity)
                    .collect(Collectors.toList());
            List<Integer> unfulfillableOrderItems = itemsToUpdate.keySet().stream()
                    .filter(items -> !fulfillableOrderItems.contains(items))
                    .collect(Collectors.toList());
            if (stockBlockOrderRequest.isUnfulfillableBlockingAllowed()) {
                buildStockCheckAndUpdateInventory(stockBlockOrderRequest, itemsToUpdate, stockBlockOrderItemRequest, new ArrayList<>(itemsToUpdate.keySet()), warehouseBlockedInventoryDto);
            } else {
                buildStockCheckAndUpdateInventory(stockBlockOrderRequest, itemsToUpdate, stockBlockOrderItemRequest, fulfillableOrderItems, warehouseBlockedInventoryDto);
            }

            populateStockBlockResponseList(stockBlockOrderItemRequest.getProductId(), fulfillableOrderItems, true, stockBlockResponseList);
            populateStockBlockResponseList(stockBlockOrderItemRequest.getProductId(), unfulfillableOrderItems, false, stockBlockResponseList);
        } else {
            if (stockBlockOrderRequest.isUnfulfillableBlockingAllowed()) {
                buildStockCheckAndUpdateInventory(stockBlockOrderRequest, itemsToUpdate, stockBlockOrderItemRequest, new ArrayList<>(itemsToUpdate.keySet()), warehouseBlockedInventoryDto);
            }
            populateStockBlockResponseList(stockBlockOrderItemRequest.getProductId(), new ArrayList<>(itemsToUpdate.keySet()), false, stockBlockResponseList);
        }
    }

    private void buildStockCheckAndUpdateInventory(StockBlockOrderRequest stockBlockOrderRequest, Map<Integer, WarehouseBlockedOrderItemDto> itemsToUpdate, StockBlockOrderItemRequest stockBlockOrderItemRequest, List<Integer> availableStockBlockOrderItem, WarehouseBlockedInventoryDto warehouseBlockedInventoryDto) throws ApplicationException {
        StockCheck stockCheck = buildStockCheck(stockBlockOrderItemRequest.getProductId(), stockBlockOrderRequest.getFacility(), availableStockBlockOrderItem, stockBlockOrderRequest.getLegalOwner(), availableStockBlockOrderItem.size());
        saveOrUpdateWarehouseBlockedInventory(stockCheck, itemsToUpdate, stockBlockOrderRequest.getOrderId(),warehouseBlockedInventoryDto, CommonConstants.DEFAULT_ENTITY_TTYPE, FulfillableStatus.FULFILLABLE);
        publishInventoryUpdateEvent(stockCheck.getPid(), stockCheck.getFacility(), stockCheck.getLegalOwner());
    }

    private StockCheck buildStockCheck(Long pid, String facility, List<Integer> availableStockBlockOrderItem, String legalOwner, Integer requiredQuantity) {
        StockCheck stockCheck = new StockCheck();
        stockCheck.setPid(pid.intValue());
        stockCheck.setFacility(facility);
        stockCheck.setOrderItems(availableStockBlockOrderItem);
        stockCheck.setLegalOwner(legalOwner);
        stockCheck.setRequiredQuantity(requiredQuantity);
        return stockCheck;
    }
}
