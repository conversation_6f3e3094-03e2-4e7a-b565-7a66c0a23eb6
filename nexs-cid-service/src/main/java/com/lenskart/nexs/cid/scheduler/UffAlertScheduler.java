package com.lenskart.nexs.cid.scheduler;

import com.lenskart.nexs.cid.facade.InventoryReconcileFacade;
import com.lenskart.nexs.cid.request.UffRedisPayload;
import com.lenskart.nexs.commonMailer.connector.CommunicationConnector;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.constants.RedisOps;
import com.lenskart.nexs.service.RedisHandler;
import io.micrometer.core.annotation.Timed;
import lombok.AccessLevel;
import lombok.Setter;
import net.javacrumbs.shedlock.core.SchedulerLock;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import static com.lenskart.nexs.cid.constant.CommonConstants.*;

@Component
@Setter(onMethod__ = {@Autowired})
public class UffAlertScheduler {

    @CustomLogger
    @Setter(AccessLevel.NONE)
    protected Logger logger;

    @Value("${spring.profiles.active}")
    @Setter(AccessLevel.NONE)
    private String currentEnv;

    @Value("${uff.inventory.alert.fromEmailId}")
    @Setter(AccessLevel.NONE)
    public String fromMailId;

    @Value("${uff.inventory.alert.toEmailIds}")
    @Setter(AccessLevel.NONE)
    public String[] toMailIds;

    @Setter(AccessLevel.NONE)
    private final DateFormat dateFormat = new SimpleDateFormat(DATE_PATTERN);

    private InventoryReconcileFacade inventoryReconcileFacade;
    private CommunicationConnector communicationConnector;

    /***
     * This scheduler will fetch all the UFF PIDs where
     *** inventory is available for UNICOM in CID but still UNICOM marked the order un-fulfillable
     *** inventory is not available in NEXS and UNICOM but still we got the order
     * from REDIS and trigger the mail twice a day and clear the REDIS for next iteration
     * @throws Exception in case any 4** and 5**.
     */
    @Timed
    @Scheduled(cron = "0 0 */6 * * *")
    @SchedulerLock(name = "UFF_ALERT_SCHEDULER")
    public void alertUnFulfillablePidsScheduler() throws Exception {
        logger.info("[alertUnFulfillablePidsScheduler] Starting scheduler job to trigger alerts for UFF PIDs {}", dateFormat.format(new Date()));
        triggerAlertsForUffPids();
    }

    private void triggerAlertsForUffPids() throws Exception {
        logger.info("[alertUnFulfillablePidsScheduler -> triggerAlertsForUffPids] Starting scheduler job to re-sync pending OMS orders to OMS process kafka");
        List<UffRedisPayload> uffPidSetPositiveInventoryForUnicom = inventoryReconcileFacade.getUffPidListFromRedis(UFF_POSITIVE_INVENTORY_FOR_UNICOM_CACHE_KEY);
        List<UffRedisPayload> uffPidSetNegativeInventoryForUnicomAndNexs = inventoryReconcileFacade.getUffPidListFromRedis(UFF_NEGATIVE_INVENTORY_FOR_NEXS_AND_UNICOM_CACHE_KEY);

        triggerMail(uffPidSetPositiveInventoryForUnicom, uffPidSetNegativeInventoryForUnicomAndNexs);

        RedisHandler.redisOps(RedisOps.DEL, UFF_POSITIVE_INVENTORY_FOR_UNICOM_CACHE_KEY);
        RedisHandler.redisOps(RedisOps.DEL, UFF_NEGATIVE_INVENTORY_FOR_NEXS_AND_UNICOM_CACHE_KEY);
    }

    private void triggerMail(List<UffRedisPayload> uffPidSetPositiveInventoryForUnicom, List<UffRedisPayload> uffPidSetNegativeInventoryForUnicomAndNexs) {
        if (CollectionUtils.isEmpty(uffPidSetPositiveInventoryForUnicom)
                && CollectionUtils.isEmpty(uffPidSetNegativeInventoryForUnicomAndNexs)
        ) {
            logger.info("[alertUnFulfillablePidsScheduler -> triggerAlertsForUffPids] no data to send the email alerts");
            return;
        }

        String mailBody = buildMailBody(uffPidSetPositiveInventoryForUnicom, uffPidSetNegativeInventoryForUnicomAndNexs);
        String subject = "UFF orders summary [" + currentEnv.split("-")[0] + "] - " + dateFormat.format(new Date());

        communicationConnector.sendMail(mailBody, fromMailId, toMailIds, subject, null, null);
    }

    private String buildMailBody(List<UffRedisPayload> uffPidSetPositiveInventoryForUnicom, List<UffRedisPayload> uffPidSetNegativeInventoryForUnicomAndNexs) {
        StringBuilder html = new StringBuilder("<html><body><h2>UFF orders summary</h2>");

        StringBuilder table1 = new StringBuilder("<br><h2>UFF PID with Positive Unicom Inventory in CID</h2><table border='1' width='700'>");
        table1.append("<tr><th style='background-color:yellow'>ProductId</th><th style='background-color:yellow'>IncrementId</th></tr>");
        for (UffRedisPayload uffPidPositiveInventoryForUnicom : uffPidSetPositiveInventoryForUnicom) {
            table1.append("<tr>")
                    .append("<td align='middle'>")
                    .append(uffPidPositiveInventoryForUnicom.getProductId())
                    .append("</td>")
                    .append("<td align='middle'>")
                    .append(uffPidPositiveInventoryForUnicom.getIncrementId())
                    .append("</td>")
                    .append("</tr>");
        }
        table1.append("</table>");


        StringBuilder table2 = new StringBuilder("<br><h2>UFF PID with Negative Unicom and NEXS inventory in CID</h2><table border='1' width='700'>");
        table2.append("<tr><th style='background-color:yellow'>ProductId</th><th style='background-color:yellow'>IncrementId</th></tr>");
        for (UffRedisPayload uffPidNegativeInventoryForUnicomAndNexs : uffPidSetNegativeInventoryForUnicomAndNexs) {
            table2.append("<tr>")
                    .append("<td align='middle'>")
                    .append(uffPidNegativeInventoryForUnicomAndNexs.getProductId())
                    .append("</td>")
                    .append("<td align='middle'>")
                    .append(uffPidNegativeInventoryForUnicomAndNexs.getIncrementId())
                    .append("</td>")
                    .append("</tr>");
        }
        table2.append("</table>");

        return html + table1.toString() + table2 + "</body></html>";
    }
}
