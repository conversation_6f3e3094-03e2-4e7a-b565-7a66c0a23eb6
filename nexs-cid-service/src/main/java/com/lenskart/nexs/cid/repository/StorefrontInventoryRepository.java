package com.lenskart.nexs.cid.repository;

import com.lenskart.nexs.cid.entity.StorefrontInventory;
import com.lenskart.nexs.commons.repository.BaseRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface StorefrontInventoryRepository extends BaseRepository<StorefrontInventory> {

    StorefrontInventory findByProductId(Long productId);
    StorefrontInventory findByProductIdAndLegalOwner(Long productId, String legalOwner);


}
