package com.lenskart.nexs.cid.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.*;

import java.util.HashMap;
import java.util.Map;

@Configuration
@Slf4j
@EnableKafka
public class ScmKafkaConsumerConfig {

    @Value(value = "${scm.kafka.bootstrap-servers}")
    private String scmBootstrapAddress;

    @Bean
    @Qualifier("scmConsumerFactory")
    public ConsumerFactory<String, String> scmConsumerFactory() {
        Map<String, Object> configurationProperties = new HashMap<>();
        log.info("[ScmKafkaConsumerConfig] The kafka bootstarp servers are {}", scmBootstrapAddress);
        configurationProperties.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG,scmBootstrapAddress);
        configurationProperties.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        configurationProperties.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        return new DefaultKafkaConsumerFactory<>(configurationProperties);
    }

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, String> scmKafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(scmConsumerFactory());
        return factory;
    }
}
