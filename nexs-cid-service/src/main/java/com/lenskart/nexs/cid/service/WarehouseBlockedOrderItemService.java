package com.lenskart.nexs.cid.service;

import com.lenskart.nexs.cid.dto.WarehouseBlockedOrderItemDto;
import com.lenskart.nexs.cid.entity.WarehouseBlockedOrderItem;
import com.lenskart.nexs.commons.service.BaseService;
import io.swagger.models.auth.In;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

@Transactional(readOnly = true)
public interface WarehouseBlockedOrderItemService extends BaseService<WarehouseBlockedOrderItemDto, WarehouseBlockedOrderItem> {
    Map<Integer, WarehouseBlockedOrderItemDto> getExistingWarehouseBlockedInventoryMapForRequest(Integer orderId);
    Map<Integer,WarehouseBlockedOrderItemDto> getExistingWarehouseBlockedInventoryMapForRequestWithLegalOwner(Integer orderId,String legalOwner);
}
