package com.lenskart.nexs.cid.config;

import com.lenskart.nexs.cid.util.ObjectHelper;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

@Component
public class ApplicationPropertyConfig {
    @Setter(onMethod__ = {@Autowired})
    private Environment env;

    public String getString(String key) {
        return env.getProperty(key);
    }

    public String getString(String key, String defaultValue) {
        String result = getString(key);
        if (result == null) {
            result = defaultValue;
        }
        return result;
    }

    public Integer getInteger(String key) {
        String value = env.getProperty(key);
        return value == null ? null : ObjectHelper.readValue(value, Integer.class);
    }

    public Integer getInteger(String key, Integer defaultValue) {
        Integer value = getInteger(key);
        return value == null ? defaultValue : value;
    }

    public Boolean getBoolean(String key) {
        String value = env.getProperty(key);
        return value == null ? null : ObjectHelper.readValue(value, Boolean.class);
    }

    public Boolean getBoolean(String key, Boolean defaultValue) {
        Boolean value = getBoolean(key);
        return value == null ? defaultValue : value;
    }


    public Double getDouble(String key) {
        String value = env.getProperty(key);
        return value == null ? null : ObjectHelper.readValue(value, Double.class);
    }

    public Double getDouble(String key, Double defaultValue) {
        Double value = getDouble(key);
        return value == null ? defaultValue : value;
    }
}
