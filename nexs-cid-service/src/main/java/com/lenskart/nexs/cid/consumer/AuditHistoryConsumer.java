package com.lenskart.nexs.cid.consumer;

import com.lenskart.nexs.cid.constant.KafkaConstants;
import com.lenskart.nexs.cid.constant.MetricConstants;
import com.lenskart.nexs.cid.entity.AuditHistoryEntity;
import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.cid.service.impl.AuditHistoryService;
import com.lenskart.nexs.cid.util.ObjectHelper;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.newrelic.api.agent.Trace;
import io.micrometer.core.annotation.Timed;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;

@Component
@ConditionalOnProperty(name = "nexs.cid.audit.consumer.enabled", havingValue = "true")
public class AuditHistoryConsumer {

    private final Counter errorCounter  = Metrics.counter(MetricConstants.INVENTORY_HISTORY_UPDATE_CONSUMER, "result", "failure");

    @CustomLogger
    private Logger logger;

    @Setter(onMethod__ = {@Autowired})
    private AuditHistoryService auditHistoryService;

    @Timed
    @Trace(dispatcher = true)
    @KafkaListener(
            topics = KafkaConstants.NEXS_CID_HISTORY_SYNC_TOPIC,
            groupId = KafkaConstants.NEXS_CID_KAFKA_GROUP_ID,
            containerFactory = "kafkaListenerContainerFactory"
    )
    public void listen(@Payload String message) throws ApplicationException {
        try {
            AuditHistoryEntity auditHistoryEntity = ObjectHelper.readNonNullValue(message, AuditHistoryEntity.class);
            //auditHistoryService.addDocument(auditHistoryEntity);
        } catch (Exception e) {
            errorCounter.increment();
            String errorMessage = MessageFormat.format("error while processing inventory history update for {0}", message);
            logger.error(errorMessage, e);
            throw new ApplicationException(errorMessage, e);
        }
    }

}
