package com.lenskart.nexs.cid.connector;

import com.lenskart.nexs.cid.entity.JunoAuditLog;
import com.lenskart.nexs.cid.enums.JunoApiStatus;
import com.lenskart.nexs.cid.request.juno.JunoInventoryUpdateRequest;
import com.lenskart.nexs.cid.service.JunoAuditService;
import com.lenskart.nexs.common.http.RestUtils;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.ResourceAccessException;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.time.Duration;
import java.util.UUID;

import static com.lenskart.nexs.cid.constant.CommonConstants.AUTH_API_KEY;
import static com.lenskart.nexs.cid.constant.CommonConstants.AUTH_APP_ID;

@Configuration
@Setter(onMethod__ = {@Autowired})
public class JunoConnector {

    @CustomLogger
    private Logger logger;

    @Value("${juno-connector.base.url}")
    private String junoConnectorBaseUrl;

    @Value("${juno.connection.timeout}")
    private int junoConnectionTimeout;

    @Value("${juno.read.timeout}")
    private int junoReadTimeout;

    @Value("${juno.inventory.update.url}")
    private String junoInventoryUpdateUrl;

    @Value("${juno-connector.username}")
    private String junoConnectorUsername;

    @Value("${juno-connector.password}")
    private String junoConnectorPassword;

    private JunoAuditService junoAuditService;

    public void updateInventoryToJuno(JunoInventoryUpdateRequest junoInventoryUpdateRequest) throws Exception {
        String correlationId = UUID.randomUUID().toString();
        String apiEndpoint = junoConnectorBaseUrl + junoInventoryUpdateUrl;
        JunoAuditLog auditLog = null;
        long startTime = System.currentTimeMillis();

        logger.info("[updateInventoryToJuno] update juno with correlation ID {} connection time {} payload {}",
                   correlationId, junoConnectionTimeout, junoInventoryUpdateRequest);

        try {
            // Create audit log entry
            if (junoAuditService != null) {
                auditLog = junoAuditService.createAuditLog(correlationId, junoInventoryUpdateRequest, apiEndpoint);
            }

            RestTemplate template = RestUtils.getRestTemplate(Duration.ofMillis(junoConnectionTimeout), Duration.ofMillis(junoReadTimeout));
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add(AUTH_APP_ID, junoConnectorUsername);
            httpHeaders.add(AUTH_API_KEY, junoConnectorPassword);
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> httpEntity = new HttpEntity(junoInventoryUpdateRequest, httpHeaders);

            ResponseEntity<String> responseEntity = template.exchange(apiEndpoint, HttpMethod.POST, httpEntity, String.class, new Object[]{null});
            long duration = System.currentTimeMillis() - startTime;

            if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
                logger.info("[updateInventoryToJuno] update Juno call successful with correlation ID {} response {}",
                           correlationId, responseEntity.getBody());

                // Update audit log with success
                if (auditLog != null && junoAuditService != null) {
                    junoAuditService.updateAuditLogWithResponse(auditLog.getId(), responseEntity.getBody(),
                                                              JunoApiStatus.SUCCESS, responseEntity.getStatusCodeValue(), duration);
                }
            } else if (responseEntity.getStatusCode() == HttpStatus.INTERNAL_SERVER_ERROR) {
                String errorMsg = "Issue while updating inventory to juno with code " + responseEntity.getStatusCode() +
                                 " and response " + responseEntity.getBody();
                logger.error("[updateInventoryToJuno] {} correlation ID {}", errorMsg, correlationId);

                // Update audit log with error
                if (auditLog != null && junoAuditService != null) {
                    junoAuditService.updateAuditLogWithError(auditLog.getId(), errorMsg, null,
                                                           JunoApiStatus.FAILED, responseEntity.getStatusCodeValue(), duration);
                }
                throw new Exception("Issue while updating inventory to juno");
            } else {
                String errorMsg = "Issue while updating inventory to juno with code " + responseEntity.getStatusCode() +
                                 " and response " + responseEntity.getBody();
                logger.error("[updateInventoryToJuno] {} correlation ID {}", errorMsg, correlationId);

                // Update audit log with error
                if (auditLog != null && junoAuditService != null) {
                    junoAuditService.updateAuditLogWithError(auditLog.getId(), errorMsg, null,
                                                           JunoApiStatus.FAILED, responseEntity.getStatusCodeValue(), duration);
                }
            }
        } catch (HttpClientErrorException | HttpServerErrorException ex) {
            long duration = System.currentTimeMillis() - startTime;
            String errorMsg = "HTTP error occurred while updating inventory to juno: " + ex.getMessage();
            String stackTrace = getStackTrace(ex);
            logger.error("[updateInventoryToJuno] {} correlation ID {} stacktrace {}", errorMsg, correlationId, stackTrace);

            // Update audit log with HTTP error
            if (auditLog != null && junoAuditService != null) {
                junoAuditService.updateAuditLogWithError(auditLog.getId(), errorMsg, stackTrace,
                                                       JunoApiStatus.FAILED, ex.getRawStatusCode(), duration);
            }
            throw new Exception("Exception occurred while updating inventory to juno");
        } catch (ResourceAccessException ex) {
            long duration = System.currentTimeMillis() - startTime;
            String errorMsg = "Timeout occurred while updating inventory to juno: " + ex.getMessage();
            String stackTrace = getStackTrace(ex);
            logger.error("[updateInventoryToJuno] {} correlation ID {} stacktrace {}", errorMsg, correlationId, stackTrace);

            // Update audit log with timeout error
            if (auditLog != null && junoAuditService != null) {
                junoAuditService.updateAuditLogWithError(auditLog.getId(), errorMsg, stackTrace,
                                                       JunoApiStatus.TIMEOUT, null, duration);
            }
            throw new Exception("Exception occurred while updating inventory to juno");
        } catch (Exception ex) {
            long duration = System.currentTimeMillis() - startTime;
            String errorMsg = "Exception occurred while updating inventory to juno: " + ex.getMessage();
            String stackTrace = getStackTrace(ex);
            logger.error("[updateInventoryToJuno] {} correlation ID {} stacktrace {}", errorMsg, correlationId, stackTrace);

            // Update audit log with general error
            if (auditLog != null && junoAuditService != null) {
                junoAuditService.updateAuditLogWithError(auditLog.getId(), errorMsg, stackTrace,
                                                       JunoApiStatus.FAILED, null, duration);
            }
            throw new Exception("Exception occurred while updating inventory to juno");
        }
    }

    private String getStackTrace(Exception ex) {
        try {
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            ex.printStackTrace(pw);
            return sw.toString();
        } catch (Exception e) {
            return "Unable to get stack trace: " + e.getMessage();
        }
    }
}
