package com.lenskart.nexs.cid.connector;

import com.lenskart.nexs.cid.request.juno.JunoInventoryUpdateRequest;
import com.lenskart.nexs.common.http.RestUtils;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

import static com.lenskart.nexs.cid.constant.CommonConstants.AUTH_API_KEY;
import static com.lenskart.nexs.cid.constant.CommonConstants.AUTH_APP_ID;

@Configuration
public class JunoConnector {

    @CustomLogger
    private Logger logger;

    @Value("${juno-connector.base.url}")
    private String junoConnectorBaseUrl;

    @Value("${juno.connection.timeout}")
    private int junoConnectionTimeout;

    @Value("${juno.read.timeout}")
    private int junoReadTimeout;

    @Value("${juno.inventory.update.url}")
    private String junoInventoryUpdateUrl;

    @Value("${juno-connector.username}")
    private String junoConnectorUsername;

    @Value("${juno-connector.password}")
    private String junoConnectorPassword;

    public void updateInventoryToJuno(JunoInventoryUpdateRequest junoInventoryUpdateRequest) throws Exception {
        {
            logger.info("[updateInventoryToJuno] update juno with connection time {} payload {}", junoConnectionTimeout, junoInventoryUpdateRequest);
            try {
                RestTemplate template = RestUtils.getRestTemplate(Duration.ofMillis(junoConnectionTimeout), Duration.ofMillis(junoReadTimeout));
                HttpHeaders httpHeaders = new HttpHeaders();
                httpHeaders.add(AUTH_APP_ID, junoConnectorUsername);
                httpHeaders.add(AUTH_API_KEY, junoConnectorPassword);
                httpHeaders.setContentType(MediaType.APPLICATION_JSON);
                HttpEntity<String> httpEntity = new HttpEntity(junoInventoryUpdateRequest, httpHeaders);
                ResponseEntity<String> responseEntity = template.exchange(junoConnectorBaseUrl + junoInventoryUpdateUrl, HttpMethod.POST, httpEntity, String.class, new Object[]{null});
                if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
                    logger.info("[updateInventoryToJuno] update Juno call successful with response {}", responseEntity.getBody());
                } else if (responseEntity.getStatusCode() == HttpStatus.INTERNAL_SERVER_ERROR) {
                    logger.error("Issue while updating inventory to juno with code {} and response {}", responseEntity.getStatusCode(), responseEntity.getBody());
                    throw new Exception("Issue while updating inventory to juno");
                } else {
                    logger.error("Issue while updating inventory to juno with code {} and response {}", responseEntity.getStatusCode(), responseEntity.getBody());
                }
            } catch (Exception ex) {
                logger.error("Exception occurred while updating inventory to juno " + ex);
                throw new Exception("Exception occurred while updating inventory to juno");
            }
        }
    }
}
