package com.lenskart.nexs.cid.config;


import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.cloud.vault.config.SecretBackendConfigurer;
import org.springframework.cloud.vault.config.VaultConfigurer;

@Configuration
public class VaultConfig implements VaultConfigurer {

    @Value("${VAULT_BACKEND}")
    String vault_backend;

    @Value("${VAULT_DEFAULT_CONTEXT}")
    String appName;

    @Override
    public void addSecretBackends(SecretBackendConfigurer configurer) {
        configurer.add(vault_backend + "/" + appName);
        configurer.registerDefaultKeyValueSecretBackends(false);
        configurer.registerDefaultDiscoveredSecretBackends(false);
    }
}