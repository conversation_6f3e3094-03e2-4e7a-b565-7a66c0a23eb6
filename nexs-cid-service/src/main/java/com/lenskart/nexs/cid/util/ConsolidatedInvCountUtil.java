package com.lenskart.nexs.cid.util;

import com.lenskart.nexs.cid.dto.WarehouseInventoryDto;
import com.lenskart.nexs.ims.enums.LocationType;
import com.lenskart.nexs.ims.model.Availability;
import com.lenskart.nexs.ims.model.Condition;
import com.lenskart.nexs.ims.model.Status;

import java.util.List;

public final class ConsolidatedInvCountUtil {

    private ConsolidatedInvCountUtil() {}

    public static long getTotalAvlWhInv(List<WarehouseInventoryDto> warehouseInventoryDtos) {
        return warehouseInventoryDtos.stream()
                .filter(dto -> Condition.GOOD.equals(dto.getCondition()))
                .filter(dto -> Availability.AVAILABLE.equals(dto.getAvailability()))
                .filter(dto -> Status.AVAILABLE.equals(dto.getStatus()))
                .filter(dto -> LocationType.DEFAULT.equals(dto.getLocationType()))
                .map(WarehouseInventoryDto::getQuantity)
                .reduce(0, Integer::sum);
    }

    public static long getReservedAvlWhInv(List<WarehouseInventoryDto> warehouseInventoryDtos) {
        return warehouseInventoryDtos.stream()
                .filter(dto -> Condition.GOOD.equals(dto.getCondition()))
                .filter(dto -> Availability.AVAILABLE.equals(dto.getAvailability()))
                .filter(dto -> Status.AVAILABLE.equals(dto.getStatus()))
                .filter(dto -> LocationType.RESERVED.equals(dto.getLocationType()))
                .map(WarehouseInventoryDto::getQuantity)
                .reduce(0, Integer::sum);
    }

    public static long getVirtualWhInv(List<WarehouseInventoryDto> warehouseInventoryDtos) {
        return warehouseInventoryDtos.stream()
                .filter(dto -> Condition.GOOD.equals(dto.getCondition()))
                .filter(dto -> Availability.VIRTUAL.equals(dto.getAvailability()))
                .filter(dto -> Status.AVAILABLE.equals(dto.getStatus()))
                .map(WarehouseInventoryDto::getQuantity)
                .reduce(0, Integer::sum);
    }

    public static long getPutAwayPendingCount(List<WarehouseInventoryDto> warehouseInventoryDtos) {
        return warehouseInventoryDtos.stream()
                .filter(dto -> Status.PUTAWAY_PENDING.equals(dto.getStatus()))
                .map(WarehouseInventoryDto::getQuantity)
                .reduce(0, Integer::sum);
    }

    public static long getAllocatedInv(List<WarehouseInventoryDto> warehouseInventoryDtos) {
        return warehouseInventoryDtos.stream()
                .filter(dto -> Availability.ALLOCATED.equals(dto.getAvailability()))
                .filter(dto -> !Status.DISPATCHED.equals(dto.getStatus()))
                .map(WarehouseInventoryDto::getQuantity)
                .reduce(0, Integer::sum);
    }

    public static long getTotalInventory(List<WarehouseInventoryDto> warehouseInventoryDtos) {
        return warehouseInventoryDtos.stream()
                .filter(dto -> !Status.DISPATCHED.equals(dto.getStatus()))
                .map(WarehouseInventoryDto::getQuantity)
                .reduce(0, Integer::sum);
    }

    public static long getGrnCount(List<WarehouseInventoryDto> warehouseInventoryDtos) {
        return warehouseInventoryDtos.stream()
                .filter(dto -> Status.GRN_DONE.equals(dto.getStatus()) || Status.TRANSFER_RECEIVED.equals(dto.getStatus()))
                .map(WarehouseInventoryDto::getQuantity)
                .reduce(0, Integer::sum);
    }
}
