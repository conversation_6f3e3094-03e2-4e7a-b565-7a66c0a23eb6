package com.lenskart.nexs.cid.strategy.impl;

import com.lenskart.nexs.cid.dto.WarehouseInventoryDto;
import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.cid.strategy.BaseInventoryUpdateStrategy;
import com.lenskart.nexs.ims.enums.CidInventoryOperation;
import com.lenskart.nexs.ims.request.InventoryUpdateRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Component
public class ReleaseInventoryUpdateStrategy extends BlockInventoryUpdateStrategy {

    @Override
    public CidInventoryOperation supportedInventoryOperation() {
        return CidInventoryOperation.INV_RELEASE;
    }

}
