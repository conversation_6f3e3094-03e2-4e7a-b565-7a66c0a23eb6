package com.lenskart.nexs.cid.service;

import com.lenskart.nexs.cid.dto.WarehouseInventoryDto;
import com.lenskart.nexs.cid.entity.WarehouseInventory;
import com.lenskart.nexs.commons.service.BaseService;
import com.lenskart.nexs.ims.enums.LocationType;
import com.lenskart.nexs.ims.response.ConsolidatedInvInfo;
import org.springframework.data.domain.Page;

import java.util.Map;
import com.lenskart.nexs.ims.model.Availability;
import com.lenskart.nexs.ims.model.Condition;
import com.lenskart.nexs.ims.model.Status;
import org.springframework.transaction.annotation.Transactional;
import com.lenskart.nexs.cid.response.AvailableInventoryResponse;
import java.util.List;


@Transactional(readOnly = true)
public interface WarehouseInventoryService extends BaseService<WarehouseInventoryDto, WarehouseInventory> {

    Page<WarehouseInventoryDto> getWarehouseInventory(Long productId, Map<String, String> params,
                                                      int page, int size, String sortBy, String sortOrder, String legalOwner);

    ConsolidatedInvInfo getConsolidatedInvInfo(String facilityCode, Integer productId, String legalOwner);

    WarehouseInventoryDto findByProductIdFacilityConditionAvailabilityAndStatusAndLegalOwnerAndLocationType(Long productId, String facility, Condition condition,
                                                                                Availability availability, Status status,String legalOwner, LocationType locationType);


    int getBufferedQuantity(Integer productId, String hubCode);

    List<AvailableInventoryResponse> getAvailableInventoryByPidAndCountry(Long productId, String countryCode, boolean considerBuffer);
}
