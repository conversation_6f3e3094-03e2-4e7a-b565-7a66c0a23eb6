package com.lenskart.nexs.cid.service;

import com.lenskart.nexs.cid.dto.StorefrontInventoryDto;
import com.lenskart.nexs.cid.entity.StorefrontInventory;
import com.lenskart.nexs.commons.service.BaseService;

public interface StorefrontInventoryService extends BaseService<StorefrontInventoryDto, StorefrontInventory> {

    StorefrontInventoryDto findByProductIdAndLegalOwner(Long pid,String legalOwner);

}
