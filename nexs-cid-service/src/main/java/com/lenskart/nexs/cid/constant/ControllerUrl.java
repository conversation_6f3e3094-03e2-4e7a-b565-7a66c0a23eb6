package com.lenskart.nexs.cid.constant;

public final class ControllerUrl {

    private ControllerUrl() {}

    public static final String BASE_URL = "/nexs/api/cid";
    public static final String BASE_URL_V1 = "/nexs/cid/api/v1";
    public static final String WAREHOUSE_INVENTORY = "/warehouseInventory";
    public static final String PENDING_ORDER = "/pendingOrder";
    public static final String UPDATE_CID_INVENTORY = "/updateInventory";
    public static final String UPDATE_CID_INVENTORY_SYNC = "/updateInventorySync";
    public static final String UPDATE_CID_INVENTORY_SOAP = "/soap/updateInventory";
    public static final String FIND_BY_PRODUCT_ID = "/findBy/productId";
    public static final String INVENTORY_CHECK_AND_BLOCK = "/inventoryCheckAndBlock";
    public static final String GET_CONSOLIDATED_INVENTORY_INFO = "/getConsolidatedInvInfo/{pid}";
    public static final String GET_INVENTORY_AVAILABILITY = "/getInventoryAvailability";
    public static final String GET_INVENTORY_AVAILABILITY_COUNT = "/getInventoryAvailabilityCount";
    public static final String STOCK_BLOCK_AVAILABLE_INVENTORY = "/stockBlockAvailableInventory";
    public static final String RECONCILE_BLOCKED_INVENTORY = "/reconcileBlockedInventory";
    public static final String INVENTORY_BLOCK_WRAPPER = "/inventoryBlockWrapper";
    public static final String INVENTORY_RELEASE = "/inventoryRelease";
    public static final String GET_BUFFER_QTY = "/bufferQuantity/productId/{productId}/hubCode/{hubCode}";
    public static final String GET_AVAILABLE_INVENTORY = "/getAvailableInventory/{pid}/{country}";
}