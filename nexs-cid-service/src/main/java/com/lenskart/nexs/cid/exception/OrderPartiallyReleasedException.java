package com.lenskart.nexs.cid.exception;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class OrderPartiallyReleasedException extends RuntimeException {

    private final String errorMessage;

    private final String errorCode;

    private Throwable cause;

    private String blockedFacility;


    public OrderPartiallyReleasedException(String errorCode, String errorMessage) {
        super(errorMessage);
        this.errorMessage = errorMessage;
        this.errorCode = errorCode;
    }

    public OrderPartiallyReleasedException(String errorCode, String errorMessage, String blockedFacility) {
        super(errorMessage);
        this.errorMessage = errorMessage;
        this.errorCode = errorCode;
        this.blockedFacility = blockedFacility;
    }

    public OrderPartiallyReleasedException(String errorCode, String errorMessage, Throwable cause) {
        super(errorMessage, cause);
        this.errorMessage = errorMessage;
        this.errorCode = errorCode;
        this.cause = cause;
    }

}
