package com.lenskart.nexs.cid.consumer;

import com.lenskart.nexs.cid.constant.CommonConstants;
import com.lenskart.nexs.cid.constant.KafkaConstants;
import com.lenskart.nexs.cid.constant.MetricConstants;
import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.cid.facade.InventoryUpdateFacade;
import com.lenskart.nexs.cid.util.ObjectHelper;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.ims.request.InventoryUpdateRequestWrapper;
import com.lenskart.nexs.newrelic.util.NewRelicUtil;
import com.newrelic.api.agent.Trace;
import io.micrometer.core.annotation.Timed;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import lombok.Setter;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.logging.log4j.Logger;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.MessageFormat;
import java.util.Objects;


@Component
@ConditionalOnProperty(name = "nexs.cid.consumer.enabled", havingValue = "true")
public class InventoryUpdateP0Consumer {

    private final Counter errorCounter  = Metrics.counter(MetricConstants.INVENTORY_UPDATE_CONSUMER, "result", "failure");

    @CustomLogger
    private Logger logger;
    @Setter(onMethod__ = {@Autowired})
    private InventoryUpdateFacade inventoryUpdateFacade;

    @Timed
    @Trace(dispatcher = true)
    @Transactional(rollbackFor = Exception.class)
    @KafkaListener(
            topics = KafkaConstants.NEXS_CID_INVENTORY_SYNC_TOPIC_P0,
            groupId = KafkaConstants.NEXS_CID_KAFKA_GROUP_P0,
            containerFactory = "kafkaListenerContainerFactory"
    )
    public void listen(ConsumerRecord<String, String> consumerRecord) throws ApplicationException {
        logger.info("started processing inventory update p0 request for pid: {} with payload: {}",
                consumerRecord.key(), consumerRecord.value());
        InventoryUpdateRequestWrapper inventoryUpdateRequest = null;
        try {
            extractEventIdentifierKeyAndLog(consumerRecord);
            inventoryUpdateRequest = ObjectHelper.readValue(consumerRecord.value(), InventoryUpdateRequestWrapper.class);
            if (Objects.isNull(inventoryUpdateRequest)) {
                throw new ApplicationException("invalid event payload");
            }
            if (MDC.get(CommonConstants.USER_ID) == null)
                MDC.put(CommonConstants.USER_ID, inventoryUpdateRequest.getUpdatedBy());
            NewRelicUtil.publishRecord(MetricConstants.CID_INVENTORY_UPDATE_CONSUMER_EVENT, inventoryUpdateRequest);
            inventoryUpdateFacade.updateInventory(inventoryUpdateRequest);
        } catch (Exception e) {
            errorCounter.increment();
            String errorMessage = MessageFormat.format("error while processing inventory update for {0}", consumerRecord.value());
            logger.error(errorMessage, e);
            if (inventoryUpdateRequest != null) {
                NewRelicUtil.publishRecord(MetricConstants.CID_INVENTORY_UPDATE_CONSUMER_FAILURE_EVENT, inventoryUpdateRequest);
            }
            throw new ApplicationException(errorMessage, e);
        }
    }

    private void extractEventIdentifierKeyAndLog(ConsumerRecord<String, String> consumerRecord) {
        Header[] strings = consumerRecord.headers().toArray();
        for (Header header : strings) {
            if (header.key().equalsIgnoreCase(KafkaConstants.EVENT_IDENTIFIER_KEY)) {
                logger.info(KafkaConstants.EVENT_IDENTIFIER_KEY + " {} ", new String(header.value()));
            }
        }
    }
}
