package com.lenskart.nexs.cid.strategy.impl;

import com.lenskart.nexs.cid.dto.WarehouseInventoryDto;
import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.cid.response.WarehouseInventoryCountResponse;
import com.lenskart.nexs.cid.strategy.BaseInventoryUpdateStrategy;
import com.lenskart.nexs.ims.enums.CidInventoryOperation;
import com.lenskart.nexs.ims.request.InventoryUpdateRequest;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class AbsoluteInventoryUpdateStrategy extends BaseInventoryUpdateStrategy {
    @Value("${legalOwner.defaultLegalOnwner}")
    private String defaultLegalOnwner;
    @Value("${legalOwner.uaeLegalOwner}")
    private String uaeLegalOwner;
    @Override
    public CidInventoryOperation supportedInventoryOperation() {
        return CidInventoryOperation.ABSOLUTE;
    }

    @Override
    public void execute(InventoryUpdateRequest request) throws ApplicationException {
        WarehouseInventoryDto persistedWhInvDto = updateWarehouseInventory(null, request, request.getQuantity());
        if (isGoodAvailableAvailableInventoryUpdateEvent(request)) {
            triggerEmsInventoryUpdateIfRequired(persistedWhInvDto);
            WarehouseInventoryCountResponse warehouseInventoryCountResponse = getTotalWarehouseInventory(request);
            int bufferedQuantity = warehouseInventoryCountResponse.getBufferedWhInventoryCount();
            logger.info("processing absolute update where productId: {} facility: {} condition: {} " +
                            "availability: {}, status: {} absolute count: {} totalWhInventoryCount: {} bufferedQuantity: {}",
                    request.getProductId(), request.getFacility(), request.getCondition(), request.getAvailability(),
                    request.getStatus(), request.getQuantity(), warehouseInventoryCountResponse.getTotalWhInventoryCount(), bufferedQuantity);
            updateStorefrontInventory(request, warehouseInventoryCountResponse.getTotalWhInventoryCount(), bufferedQuantity);
            sendInventoryUpdateToSCM(request, warehouseInventoryCountResponse.getTotalWhInventoryCount(), bufferedQuantity);
            sendInventoryUpdateToJuno(request);
            updateBlockedInventoryDtoAndSendEvent(request,persistedWhInvDto);
        }
    }

}
