package com.lenskart.nexs.cid.mapper;

import com.lenskart.nexs.cid.constant.CommonConstants;
import com.lenskart.nexs.cid.enums.Supplier;

import com.lenskart.nexs.cid.request.UpdateCIDRequest;
import com.lenskart.nexs.cid.request.updateinventory.Item;
import com.lenskart.nexs.cid.request.updateinventory.StockAdjustment;
import com.lenskart.nexs.ims.enums.CidInventoryOperation;
import com.lenskart.nexs.ims.enums.LocationType;
import com.lenskart.nexs.ims.model.Availability;
import com.lenskart.nexs.ims.model.Condition;
import com.lenskart.nexs.ims.model.Status;
import com.lenskart.nexs.ims.request.InventoryUpdateRequest;
import com.lenskart.nexs.ims.request.InventoryUpdateRequestWrapper;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.slf4j.MDC;

import java.util.Date;
import java.util.List;

@Mapper(componentModel = "spring")
public interface StockAdjustmentToInventoryUpdateRequestMapper {

    @Mapping(target = "quantity", source = "qty")
    InventoryUpdateRequest mapInventory(Item item);

    List<InventoryUpdateRequest> mapList(List<Item> items);

    @Mapping(target = "inventoryUpdateRequests", source = "stockAdjustment.productIdArr.item")
    InventoryUpdateRequestWrapper map(StockAdjustment stockAdjustment, String defaultFacility);


    @AfterMapping
    default void populateDefaultValues(@MappingTarget InventoryUpdateRequestWrapper requestWrapper, String facility){
        List<InventoryUpdateRequest> inventoryUpdateRequests = requestWrapper.getInventoryUpdateRequests();
        inventoryUpdateRequests.forEach(inventoryUpdateRequest -> {
            inventoryUpdateRequest.setStatus(Status.AVAILABLE);
            inventoryUpdateRequest.setAvailability(Availability.AVAILABLE);
            inventoryUpdateRequest.setCondition(Condition.GOOD);
            inventoryUpdateRequest.setSupplier(Supplier.UNICOM.name());
            inventoryUpdateRequest.setLocationType(LocationType.DEFAULT);
            inventoryUpdateRequest.setEnabled(true);
            inventoryUpdateRequest.setFacility(facility);
            inventoryUpdateRequest.setEventTime(new Date());
            inventoryUpdateRequest.setUpdatedBy(MDC.get(CommonConstants.USER_ID));
            inventoryUpdateRequest.setCidInventoryOperation(CidInventoryOperation.ABSOLUTE);
        });
    }

}
