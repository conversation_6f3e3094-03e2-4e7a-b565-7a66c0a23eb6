package com.lenskart.nexs.cid.repository;

import com.lenskart.nexs.cid.entity.WarehouseBlockedInventoryHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface WarehouseBlockedInventoryHistoryRepository extends JpaRepository<WarehouseBlockedInventoryHistory, Long> {
    List<WarehouseBlockedInventoryHistory> findByProductIdAndFacilityInAndUpdatedAtLessThanEqualOrderByUpdatedAtDesc(Long productId, List<String> nexsFacilities, Date updatedAt);
}
