package com.lenskart.nexs.cid.connector;

import com.fasterxml.jackson.core.type.TypeReference;
import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.cid.response.UnSynedCountResponse;
import com.lenskart.nexs.cid.util.ObjectHelper;
import com.lenskart.nexs.common.http.RestUtils;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.Duration;
import java.util.Date;
import java.util.concurrent.CompletableFuture;

@Configuration
public class OrderInterceptorConnector {
    @CustomLogger
    private Logger logger;

    @Value("${order-interceptor.base.url}")
    private String orderInterceptorBaseUrl;

    @Value("${order-interceptor.connection.timeout}")
    private int orderInterceptorConnectionTimeout;

    @Value("${order-interceptor.read.timeout}")
    private int orderInterceptorReadTimeout;

    @Value("${order-interceptor.unsynced.count.url}")
    private String unSyncedCountUrl;

    @Async
    public CompletableFuture<Object> getOrderInterceptorUnSyncedQuantity(Long productId, String legalOwner, String hubCode) throws Exception {
        AsyncResult<Object> result = new AsyncResult<>("success");
        RestTemplate template = RestUtils.getRestTemplate(Duration.ofMillis(orderInterceptorReadTimeout), Duration.ofMillis(orderInterceptorReadTimeout));
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> httpEntity = new HttpEntity(httpHeaders);
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(orderInterceptorBaseUrl + unSyncedCountUrl)
                .queryParam("productId", productId).queryParam("legalOwner", legalOwner).queryParam("hubCode", hubCode);
        logger.info("[getOrderInterceptorUnSyncedQuantity] Request for OrderInterceptor for unsynced count  : " + builder.toUriString());
        ResponseEntity<String> responseEntity = template.exchange(builder.toUriString(), HttpMethod.GET, httpEntity, String.class, new Object[]{null});
        logger.info("[getOrderInterceptorUnSyncedQuantity] Response for OrderInterceptor for unsynced count  : " + responseEntity.getBody());
        if (responseEntity.getStatusCode() != HttpStatus.OK && responseEntity.getBody() == null) {
            return AsyncResult.forExecutionException(
                    new ApplicationException("error while getOrderOpsUnSyncedQuantity in order-interceptor for productId: " + productId, null)
            ).completable();
        } else {
            result = new AsyncResult<>(responseEntity.getBody());
        }
        return result.completable();
    }
}
