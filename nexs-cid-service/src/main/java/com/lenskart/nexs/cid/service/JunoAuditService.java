package com.lenskart.nexs.cid.service;

import com.lenskart.nexs.cid.dto.JunoAuditLogDto;
import com.lenskart.nexs.cid.entity.JunoAuditLog;
import com.lenskart.nexs.cid.enums.JunoApiStatus;
import com.lenskart.nexs.cid.request.juno.JunoInventoryUpdateRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.List;

/**
 * Service interface for Juno audit logging
 */
public interface JunoAuditService {

    /**
     * Create an audit log entry for a Juno API request
     */
    JunoAuditLog createAuditLog(String correlationId, JunoInventoryUpdateRequest request, String apiEndpoint);

    /**
     * Update audit log with response information
     */
    void updateAuditLogWithResponse(Long auditLogId, String responsePayload, JunoApiStatus status, 
                                   Integer httpStatusCode, Long durationMs);

    /**
     * Update audit log with error information
     */
    void updateAuditLogWithError(Long auditLogId, String errorMessage, String stackTrace, 
                                JunoApiStatus status, Integer httpStatusCode, Long durationMs);

    /**
     * Increment retry count for an audit log
     */
    void incrementRetryCount(Long auditLogId);

    /**
     * Find audit logs by correlation ID
     */
    List<JunoAuditLogDto> findByCorrelationId(String correlationId);

    /**
     * Find audit logs by status with pagination
     */
    Page<JunoAuditLogDto> findByStatus(JunoApiStatus status, Pageable pageable);

    /**
     * Find audit logs by date range with pagination
     */
    Page<JunoAuditLogDto> findByDateRange(Date startDate, Date endDate, Pageable pageable);

    /**
     * Find audit logs by product ID with pagination
     */
    Page<JunoAuditLogDto> findByProductId(String productId, Pageable pageable);

    /**
     * Find audit logs by facility code with pagination
     */
    Page<JunoAuditLogDto> findByFacilityCode(String facilityCode, Pageable pageable);

    /**
     * Find audit logs by legal owner with pagination
     */
    Page<JunoAuditLogDto> findByLegalOwner(String legalOwner, Pageable pageable);

    /**
     * Get failed logs within a time range
     */
    List<JunoAuditLogDto> getFailedLogs(Date startDate, Date endDate);

    /**
     * Get audit statistics for a date range
     */
    JunoAuditStatsDto getAuditStats(Date startDate, Date endDate);

    /**
     * Get logs with high retry count
     */
    List<JunoAuditLogDto> getHighRetryLogs(Integer minRetryCount);
}
