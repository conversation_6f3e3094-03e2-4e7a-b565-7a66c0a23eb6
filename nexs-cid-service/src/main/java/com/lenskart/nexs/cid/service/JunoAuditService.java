package com.lenskart.nexs.cid.service;

import com.lenskart.nexs.cid.entity.JunoAuditLog;
import com.lenskart.nexs.cid.enums.JunoApiStatus;
import com.lenskart.nexs.cid.request.juno.JunoInventoryTransaction;
import com.lenskart.nexs.cid.request.juno.JunoInventoryUpdateRequest;

/**
 * Service interface for Juno audit logging
 */
public interface JunoAuditService {

    /**
     * Create audit log entries for each transaction in a Juno API request
     */
    void createAuditLogs(String correlationId, JunoInventoryUpdateRequest request, String apiEndpoint);

    /**
     * Update audit logs with response information for a correlation ID
     */
    void updateAuditLogsWithResponse(String correlationId, String responsePayload, JunoApiStatus status,
                                   Integer httpStatusCode, Long durationMs);

    /**
     * Update audit logs with error information for a correlation ID
     */
    void updateAuditLogsWithError(String correlationId, String errorMessage,
                                JunoApiStatus status, Integer httpStatusCode, Long durationMs);
}
