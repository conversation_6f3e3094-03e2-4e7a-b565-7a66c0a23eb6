package com.lenskart.nexs.cid.facade;

import com.lenskart.nexs.cid.config.ApplicationPropertyConfig;
import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.cid.mapper.StockAdjustmentToInventoryUpdateRequestMapper;
import com.lenskart.nexs.cid.producer.InventoryUpdateProducer;
import com.lenskart.nexs.cid.request.updateinventory.*;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.ims.request.InventoryUpdateRequestWrapper;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Instant;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;


@Component
public class UnicomInventoryUpdateFacade {

    private static final String DEFAULT_UNICOM_FACILITY = "nexs.cid.default.unicom.facility";
    @CustomLogger
    private Logger log;

    @Setter(onMethod__ = {@Autowired})
    private InventoryUpdateProducer inventoryUpdateProducer;

    @Setter(onMethod__ = {@Autowired})
    private StockAdjustmentToInventoryUpdateRequestMapper stockAdjustmentToInventoryUpdateRequestMapper;

    @Setter(onMethod__ = {@Autowired})
    private ApplicationPropertyConfig applicationPropertyConfig;


    @Transactional(rollbackFor = Exception.class)
    public StockAdjustmentResponse updateInventory(StockAdjustment stockAdjustment) throws ApplicationException {
        String timeStamp = String.valueOf(Instant.now().getEpochSecond());
        stockAdjustment.getProductIdArr().getItem()
                .forEach(item -> item.setUpdateTime(timeStamp));
        StockAdjustmentResponse stockAdjustmentResponse = getStockAdjustmentResponse(null, true);

        try {
            InventoryUpdateRequestWrapper inventoryUpdateRequest = stockAdjustmentToInventoryUpdateRequestMapper.map(
                    stockAdjustment,
                    applicationPropertyConfig.getString(DEFAULT_UNICOM_FACILITY)
            );
            inventoryUpdateProducer.sendMessage(inventoryUpdateRequest);
        } catch (ApplicationException ex) {
            log.error("Exception when pushing stockUpdate to kafka,the request is {} and exception is {}",
                    stockAdjustment, ex);
            throw ex;
        }
        return stockAdjustmentResponse;

    }

    private StockAdjustmentResponse getStockAdjustmentResponse(String msg, boolean isSuccess) {
        StockAdjustmentResponse stockAdjustmentResponse = new StockAdjustmentResponse();
        RequestStatus requestStatus = new RequestStatus();
        if (msg != null) {
            requestStatus.setMsg(msg);
        }
        requestStatus.setSuccessCode(isSuccess ? "Success" : "Failure");
        stockAdjustmentResponse.setReturn(requestStatus);
        return stockAdjustmentResponse;
    }
}
