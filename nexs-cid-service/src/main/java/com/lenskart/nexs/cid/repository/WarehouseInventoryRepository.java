package com.lenskart.nexs.cid.repository;

import com.lenskart.nexs.cid.entity.WarehouseInventory;
import com.lenskart.nexs.commons.repository.BaseRepository;
import com.lenskart.nexs.ims.model.Availability;
import com.lenskart.nexs.ims.model.Condition;
import com.lenskart.nexs.ims.model.Status;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface WarehouseInventoryRepository extends BaseRepository<WarehouseInventory> {

    @Query("SELECT w from WarehouseInventory w WHERE (w.productId = :productId) and " +
            "(:condition is null or w.condition = :condition) and" +
            "(:status is null or w.status = :status) and" +
            "(:availability is null or w.availability = :availability) and" +
            "(:facility is null or w.facility = :facility) and" +
            "(w.legalOwner= :legalOwner)")
    Page<WarehouseInventory> getWarehouseInventory(Long productId, Condition condition, Status status,
            Availability availability, String facility, Pageable pageRequest, String legalOwner);



}
