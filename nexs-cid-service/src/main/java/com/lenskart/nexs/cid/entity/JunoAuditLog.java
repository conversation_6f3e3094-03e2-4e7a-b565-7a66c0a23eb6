package com.lenskart.nexs.cid.entity;

import com.lenskart.nexs.cid.enums.JunoApiStatus;
import com.lenskart.nexs.commons.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

/**
 * Entity to store audit information for Juno API calls
 */
@Getter
@Setter
@ToString(callSuper = true)
@Entity
@Table(name = "juno_audit_log")
public class JunoAuditLog extends BaseEntity {

    @Column(name = "correlation_id", nullable = false, length = 100)
    private String correlationId;

    @Column(name = "request_payload", columnDefinition = "TEXT")
    private String requestPayload;

    @Column(name = "response_payload", columnDefinition = "TEXT")
    private String responsePayload;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private JunoApiStatus status;

    @Column(name = "http_status_code")
    private Integer httpStatusCode;

    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    @Column(name = "error_stack_trace", columnDefinition = "TEXT")
    private String errorStackTrace;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "request_timestamp", nullable = false)
    private Date requestTimestamp;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "response_timestamp")
    private Date responseTimestamp;

    @Column(name = "duration_ms")
    private Long durationMs;

    @Column(name = "retry_count", nullable = false)
    private Integer retryCount = 0;

    @Column(name = "api_endpoint", length = 500)
    private String apiEndpoint;

    @Column(name = "product_ids", columnDefinition = "TEXT")
    private String productIds;

    @Column(name = "facility_codes", columnDefinition = "TEXT")
    private String facilityCodes;

    @Column(name = "legal_owners", columnDefinition = "TEXT")
    private String legalOwners;

    @Column(name = "transaction_count")
    private Integer transactionCount;

    @Column(name = "source", length = 50)
    private String source;
}
