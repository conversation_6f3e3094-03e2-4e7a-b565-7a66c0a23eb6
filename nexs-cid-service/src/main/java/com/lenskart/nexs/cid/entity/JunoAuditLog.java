package com.lenskart.nexs.cid.entity;

import com.lenskart.nexs.cid.enums.JunoApiStatus;
import com.lenskart.nexs.commons.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

/**
 * Entity to store audit information for Juno API calls
 */
@Getter
@Setter
@ToString(callSuper = true)
@Entity
@Table(name = "juno_audit_log")
public class JunoAuditLog extends BaseEntity {

    @Column(name = "correlation_id", nullable = false, length = 100)
    private String correlationId;

    @Column(name = "product_id", nullable = false)
    private Integer productId;

    @Column(name = "facility_code", nullable = false, length = 50)
    private String facilityCode;

    @Column(name = "quantity", nullable = false)
    private Integer quantity;

    @Column(name = "transaction_type", nullable = false, length = 20)
    private String transactionType;

    @Column(name = "legal_owner", nullable = false, length = 50)
    private String legalOwner;

    @Column(name = "update_time", length = 50)
    private String updateTime;

    @Column(name = "request_payload", columnDefinition = "TEXT")
    private String requestPayload;

    @Column(name = "response_payload", columnDefinition = "TEXT")
    private String responsePayload;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private JunoApiStatus status;

    @Column(name = "http_status_code")
    private Integer httpStatusCode;

    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "request_timestamp", nullable = false)
    private Date requestTimestamp;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "response_timestamp")
    private Date responseTimestamp;

    @Column(name = "duration_ms")
    private Long durationMs;
}
