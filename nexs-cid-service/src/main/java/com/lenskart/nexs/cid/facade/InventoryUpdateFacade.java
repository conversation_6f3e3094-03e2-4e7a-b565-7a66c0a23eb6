package com.lenskart.nexs.cid.facade;

import com.lenskart.nexs.cid.constant.CommonConstants;
import com.lenskart.nexs.cid.constant.MetricConstants;
import com.lenskart.nexs.cid.dto.WarehouseBlockedInventoryDto;
import com.lenskart.nexs.cid.dto.WarehouseBlockedOrderItemDto;
import com.lenskart.nexs.cid.dto.WarehouseInventoryDto;
import com.lenskart.nexs.cid.enums.WarehouseBlockedOrderItemStatus;
import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.cid.producer.EMSInventoryEventProducer;
import com.lenskart.nexs.cid.producer.InventoryUpdateProducer;
import com.lenskart.nexs.cid.request.FulfillabilityUpdateEventDao;
import com.lenskart.nexs.cid.request.StockReleaseWrapper;
import com.lenskart.nexs.cid.request.UpdateCIDRequest;
import com.lenskart.nexs.cid.service.WarehouseBlockedOrderItemService;
import com.lenskart.nexs.cid.service.WarehouseBlockedInventoryService;
import com.lenskart.nexs.cid.service.WarehouseInventoryService;
import com.lenskart.nexs.cid.strategy.InventoryUpdateStrategyExecutor;
import com.lenskart.nexs.cid.util.EmsEventUtil;
import com.lenskart.nexs.cid.util.LegalOwnerUtil;
import com.lenskart.nexs.commonlogger.annotations.CustomLogger;
import com.lenskart.nexs.ems.model.EmsExceptionEvent;
import com.lenskart.nexs.ims.connector.FMSConnector;
import com.lenskart.nexs.ims.enums.CidInventoryOperation;
import com.lenskart.nexs.ims.enums.LocationType;
import com.lenskart.nexs.ims.model.Availability;
import com.lenskart.nexs.ims.model.Condition;
import com.lenskart.nexs.ims.model.Status;
import com.lenskart.nexs.ims.request.InventoryUpdateRequest;
import com.lenskart.nexs.ims.request.InventoryUpdateRequestWrapper;
import com.lenskart.nexs.ims.request.StockReleaseRequest;
import com.lenskart.nexs.ims.response.FacilityDetails;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import lombok.Setter;
import org.apache.logging.log4j.Logger;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;

import static com.lenskart.nexs.cid.util.WarehouseBlockedInventoryUtil.*;

@Component
public class InventoryUpdateFacade {

    private final Counter errorCounter  = Metrics.counter(MetricConstants.INVENTORY_RELEASE_UPDATE, "result", "failure");

    @CustomLogger
    private Logger logger;

    @Setter(onMethod__ = {@Autowired})
    private WarehouseBlockedInventoryService warehouseBlockedInventoryService;

    @Setter(onMethod__ = {@Autowired})
    private WarehouseBlockedOrderItemService warehouseBlockedOrderItemService;

    @Setter(onMethod__ = {@Autowired})
    private InventoryUpdateProducer inventoryUpdateProducer;

    @Setter(onMethod__ = {@Autowired})
    private InventoryUpdateStrategyExecutor inventoryUpdateStrategyExecutor;
    @Value("${legalOwner.defaultLegalOnwner}")
    private String defaultLegalOwner;
    @Setter(onMethod__ = {@Autowired})
    private WarehouseInventoryService warehouseInventoryService;
    @Setter(onMethod__ = {@Autowired})
    private EMSInventoryEventProducer emsInventoryEventProducer;

    @Setter(onMethod__ = {@Autowired})
    private static FMSConnector fmsConnector;

    @Value("${fms-connector.base.url}")
    private static String fmsConnectorBaseUrl;

    @Value("${fms-connector.facility.details.url}")
    private static String fmsConnectorFacilityDetailsUrl;

    @Transactional(rollbackFor = Exception.class)
    public void updateInventory(UpdateCIDRequest updateCIDRequest) throws ApplicationException {
        InventoryUpdateRequestWrapper inventoryUpdateRequestWrapper = new InventoryUpdateRequestWrapper();
        inventoryUpdateRequestWrapper.setUpdatedBy(MDC.get(CommonConstants.USER_ID));
        inventoryUpdateRequestWrapper.setInventoryUpdateRequests(updateCIDRequest.getInventoryUpdateRequests());
        inventoryUpdateProducer.sendMessage(inventoryUpdateRequestWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateInventory(InventoryUpdateRequestWrapper inventoryUpdateRequestWrapper) throws ApplicationException {
        boolean isInvUpdateRequestEmpty = CollectionUtils.isEmpty(inventoryUpdateRequestWrapper.getInventoryUpdateRequests());
        if (inventoryUpdateRequestWrapper.getStockReleaseRequest() != null) {
            if (!StringUtils.hasLength(inventoryUpdateRequestWrapper.getStockReleaseRequest().getLegalOwner())) {
                if (LegalOwnerUtil.getFacilityLegalOwnerMap().containsKey(inventoryUpdateRequestWrapper.getStockReleaseRequest().getFacility())) {
                    inventoryUpdateRequestWrapper.getStockReleaseRequest().setLegalOwner(LegalOwnerUtil.getFacilityLegalOwnerMap().get(inventoryUpdateRequestWrapper.getStockReleaseRequest().getFacility()));
                } else {
                    inventoryUpdateRequestWrapper.getStockReleaseRequest().setLegalOwner(defaultLegalOwner);
                }
            }
            handleReleaseInventory(inventoryUpdateRequestWrapper.getStockReleaseRequest(), isInvUpdateRequestEmpty);
        }
        if (!isInvUpdateRequestEmpty) {
            for (InventoryUpdateRequest inventoryUpdateRequest : inventoryUpdateRequestWrapper.getInventoryUpdateRequests()) {
                // TODO: 27/08/22 fallback condition added, remove after IMS CID stability on prod
                if(legalOwnerValidator(inventoryUpdateRequest,inventoryUpdateRequest.getSupplier())){
                    if (Objects.isNull(inventoryUpdateRequest.getCidInventoryOperation())) {
                        inventoryUpdateRequest.setCidInventoryOperation(CidInventoryOperation.ABSOLUTE);
                    }
                    if (inventoryUpdateRequest.getUpdatedBy() == null) {
                        inventoryUpdateRequest.setUpdatedBy(MDC.get(CommonConstants.USER_ID));
                    }
                    inventoryUpdateStrategyExecutor.doExecute(inventoryUpdateRequest);
                }else{
                    throw new ApplicationException("Legal owner is not mentioned");
                }
            }
        }
    }

//    @Retryable(value = { ObjectOptimisticLockingFailureException.class }, maxAttempts = 3)
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void handleReleaseInventory(StockReleaseRequest stockReleaseRequest, boolean triggerInvReleaseEvent) throws ApplicationException {
        logger.info("[handleReleaseInventory] request for stock release {}", stockReleaseRequest);
        Map<Integer, WarehouseBlockedOrderItemDto> warehouseBlockedOrderItemMap =
                warehouseBlockedOrderItemService.getExistingWarehouseBlockedInventoryMapForRequest(
                        stockReleaseRequest.getOrderId());

        if (CollectionUtils.isEmpty(stockReleaseRequest.getOrderItems())) {
            throw new ApplicationException("Order Item list is empty in the stock release request.");
        }

        Set<Long> releasedWarehouseBlockedInventorySet = new HashSet<>();
        WarehouseBlockedInventoryDto warehouseBlockedInventoryDto = new WarehouseBlockedInventoryDto();
        for (Integer orderItemId : stockReleaseRequest.getOrderItems()) {
            if (!warehouseBlockedOrderItemMap.containsKey(orderItemId)) {
                WarehouseBlockedOrderItemDto warehouseBlockedOrderItemDto = warehouseBlockedOrderItemService.findBySearchTerms("orderItemId.eq:" + orderItemId);
                if (!ObjectUtils.isEmpty(warehouseBlockedOrderItemDto)) {
                    logger.error("[handleReleaseInventory] Bad Request, orderItemId {} is already assigned to different orderId {}", orderItemId, warehouseBlockedOrderItemDto.getOrderId());
                    errorCounter.increment();
                    throw new ApplicationException("orderItemId " + orderItemId + " is already blocked for different orderId " + warehouseBlockedOrderItemDto.getOrderId());
                }
                createInventoryWithStatusRelease(stockReleaseRequest, orderItemId);
            } else {
                logger.info("[handleReleaseInventory] stock release request for orderItem {}", orderItemId);
                WarehouseBlockedOrderItemDto warehouseBlockedOrderItemDto = warehouseBlockedOrderItemMap.get(orderItemId);
                if (Objects.nonNull(warehouseBlockedOrderItemDto) &&
                        WarehouseBlockedOrderItemStatus.BLOCKED.equals(warehouseBlockedOrderItemDto.getWarehouseBlockedOrderItemStatus())) {

                        warehouseBlockedInventoryDto = warehouseBlockedOrderItemDto.getWarehouseBlockedInventoryDto();
                        markItemStatusReleased(warehouseBlockedOrderItemDto, releasedWarehouseBlockedInventorySet);
                }
            }
        }

        updateWarehouseBlockedInventoryAndTriggerInventoryUpdate(triggerInvReleaseEvent, releasedWarehouseBlockedInventorySet, warehouseBlockedInventoryDto, stockReleaseRequest.getLegalOwner());
    }

    private void updateWarehouseBlockedInventoryAndTriggerInventoryUpdate(boolean triggerInvReleaseEvent, Set<Long> releasedWarehouseBlockedInventorySet, WarehouseBlockedInventoryDto warehouseBlockedInventoryDto, String legalOwner) throws ApplicationException {
        if (Objects.nonNull(warehouseBlockedInventoryDto) && releasedWarehouseBlockedInventorySet.size() > 0) {
            warehouseBlockedInventoryDto.setQuantity(warehouseBlockedInventoryDto.getQuantity() - releasedWarehouseBlockedInventorySet.size());
            warehouseBlockedInventoryService.update(warehouseBlockedInventoryDto, warehouseBlockedInventoryDto.getId());

            publishInventoryUpdateEvent(Math.toIntExact(warehouseBlockedInventoryDto.getProductId()),
                    warehouseBlockedInventoryDto.getFacility(),
                    triggerInvReleaseEvent,
                    legalOwner
            );
        }
    }
    public FulfillabilityUpdateEventDao getInventoryUpdateEventDao(WarehouseBlockedInventoryDto warehouseBlockedInventoryDto, Long pid, String facility, String legalOwner) {
        WarehouseInventoryDto warehouseInventory = warehouseInventoryService.findByProductIdFacilityConditionAvailabilityAndStatusAndLegalOwnerAndLocationType(pid,
                facility, Condition.GOOD, Availability.AVAILABLE, Status.AVAILABLE, legalOwner, LocationType.DEFAULT);
        long totalAvailableQuantityAgainstPidAndFacility = warehouseInventory != null ? warehouseInventory.getQuantity() : 0;
        Integer existingUnfulfillable = 0;
        if (!ObjectUtils.isEmpty(warehouseBlockedInventoryDto)) {
            existingUnfulfillable = warehouseBlockedInventoryDto.getUnfulfillableQty() != null
                    ? warehouseBlockedInventoryDto.getUnfulfillableQty()
                    : (int) Math.max(0, warehouseBlockedInventoryDto.getQuantity() - totalAvailableQuantityAgainstPidAndFacility);
        }
        FulfillabilityUpdateEventDao result = new FulfillabilityUpdateEventDao(warehouseInventory, totalAvailableQuantityAgainstPidAndFacility, existingUnfulfillable);
        return result;
    }

    public void sendFulfillablityEventToEms(WarehouseBlockedInventoryDto warehouseBlockedInventoryDto, String legalOwner, Integer existingUnfulfillable, String eventType) {
        if(!existingUnfulfillable.equals(warehouseBlockedInventoryDto.getUnfulfillableQty())){
            try {
                logger.info("[updateExistingWarehouseBlockedInventoryData] sending event to ems as there is {} in" +
                        " unfullfilable count for pid {} and facility {}",eventType, warehouseBlockedInventoryDto.getProductId(), warehouseBlockedInventoryDto.getFacility());
                EmsExceptionEvent emsExceptionEvent = EmsEventUtil.populateEmsEventPayload(warehouseBlockedInventoryDto, eventType, legalOwner);
                emsInventoryEventProducer.sendMessage(emsExceptionEvent, warehouseBlockedInventoryDto.getProductId());
            } catch (Exception ex) {
                logger.error("[updateExistingWarehouseBlockedInventoryData] exception while creating or sending " +
                        "event to ems for warehouseBlockedInventoryDto {}", warehouseBlockedInventoryDto);
            }
        }
        logger.info("[sendFulfillablityEventToEms] not sending event to ems as there is  no change in unfullfillable qty for pid {} and facility {}", warehouseBlockedInventoryDto.getProductId(), warehouseBlockedInventoryDto.getFacility());
    }
    @Recover
    public void handleReleaseInventoryFallback(ObjectOptimisticLockingFailureException opException, StockReleaseRequest stockReleaseRequest) throws ApplicationException {
        logger.info("[handleReleaseInventoryFallback] OptimisticLockException fallback for request {}", stockReleaseRequest);
        errorCounter.increment();
        throw new ApplicationException("Error while processing inventory release, please try again.");
    }

    private void markItemStatusReleased(WarehouseBlockedOrderItemDto warehouseBlockedOrderItemDto, Set<Long> releasedWarehouseBlockedInventorySet) {
        logger.info("[Stock Release - updateInventoryWithStatusRelease] pending order item is {}", warehouseBlockedOrderItemDto);
        warehouseBlockedOrderItemDto.setWarehouseBlockedOrderItemStatus(WarehouseBlockedOrderItemStatus.RELEASED);
        releasedWarehouseBlockedInventorySet.add(warehouseBlockedOrderItemDto.getOrderItemId());
        warehouseBlockedOrderItemService.save(warehouseBlockedOrderItemDto);
    }

    private void createInventoryWithStatusRelease(StockReleaseRequest stockReleaseRequest, Integer orderItemId) throws ApplicationException {
        MDC.put(CommonConstants.USER_ID, stockReleaseRequest.getUpdatedBy());
        WarehouseBlockedInventoryDto warehouseBlockedInventoryDto = warehouseBlockedInventoryService.findByProductIdAndFacilityAndLegalOwner(
                Long.valueOf(stockReleaseRequest.getPid()), stockReleaseRequest.getFacility(), stockReleaseRequest.getLegalOwner()
        );
         if (ObjectUtils.isEmpty(warehouseBlockedInventoryDto)) {
            warehouseBlockedInventoryDto = getWarehouseBlockedInventoryDtoWithLegalOwner(
                    stockReleaseRequest.getFacility(), stockReleaseRequest.getPid(), 0,
                    stockReleaseRequest.getLegalOwner()
            );
        } else {
            WarehouseBlockedOrderItemDto warehouseBlockedOrderItemDto = warehouseBlockedOrderItemService.findBySearchTerms("orderItemId.eq:" + orderItemId);
            if (!ObjectUtils.isEmpty(warehouseBlockedOrderItemDto)) {
                errorCounter.increment();
                throw new ApplicationException("orderItem already exists for different PID, OrderId and Facility combination");
            }
        }

        WarehouseBlockedOrderItemDto warehouseBlockedOrderItemDto = getWarehouseBlockedOrderItemDto(
                orderItemId, stockReleaseRequest.getOrderId(),
                warehouseBlockedInventoryDto, WarehouseBlockedOrderItemStatus.RELEASED
        );

        WarehouseBlockedInventoryDto savedWarehouseBlockedInventoryDto = warehouseBlockedInventoryService.save(warehouseBlockedOrderItemDto.getWarehouseBlockedInventoryDto());
        warehouseBlockedOrderItemDto.setWarehouseBlockedInventoryDto(savedWarehouseBlockedInventoryDto);
        warehouseBlockedOrderItemService.save(warehouseBlockedOrderItemDto);
    }

    private void publishInventoryUpdateEvent(Integer pid, String facility, boolean triggerInvReleaseEvent, String legalOwner) throws ApplicationException {
        if (triggerInvReleaseEvent) {
            List<InventoryUpdateRequest> inventoryUpdateRequestList = new ArrayList<>();
            populateInventoryUpdateRequest(pid, facility, inventoryUpdateRequestList, CidInventoryOperation.INV_RELEASE, legalOwner);

            InventoryUpdateRequestWrapper inventoryUpdateRequestWrapper = new InventoryUpdateRequestWrapper();
            inventoryUpdateRequestWrapper.setUpdatedBy(MDC.get(CommonConstants.USER_ID));
            inventoryUpdateRequestWrapper.setInventoryUpdateRequests(inventoryUpdateRequestList);
            updateInventory(inventoryUpdateRequestWrapper);
        }
    }

    private boolean legalOwnerValidator(InventoryUpdateRequest inventoryUpdateRequest, String supplier) {
//        if(inventoryUpdateRequest.getLegalOwner() == null && supplier.equalsIgnoreCase("NEXS")){
//            return false;
////        }else if(inventoryUpdateRequest.getLegalOwner() == null){
////            inventoryUpdateRequest.setLegalOwner(defaultLegalOwner);
////        }
//        return true;
//    }
        if (StringUtils.isEmpty(inventoryUpdateRequest.getLegalOwner())) {
            inventoryUpdateRequest.setLegalOwner(defaultLegalOwner);
        }
        return true;
    }


    @Transactional(rollbackFor = Exception.class)
    public void releaseInventory(StockReleaseWrapper stockReleaseWrapper) throws ApplicationException {
        logger.info("[releaseInventory] release inventory wrapper request {}", stockReleaseWrapper);
        for (StockReleaseRequest stockReleaseRequest : stockReleaseWrapper.getStockReleaseRequestList()) {
            if (LegalOwnerUtil.getFacilityLegalOwnerMap().containsKey(stockReleaseRequest.getFacility())) {
                stockReleaseRequest.setLegalOwner(LegalOwnerUtil.getFacilityLegalOwnerMap().get(stockReleaseRequest.getFacility()));
            } else {
                try {
                    FacilityDetails facilityDetails = fmsConnector.fetchLegalOwner(stockReleaseRequest.getFacility(), fmsConnectorBaseUrl + fmsConnectorFacilityDetailsUrl);
                    if (!Objects.isNull(facilityDetails)) {
                        stockReleaseRequest.setLegalOwner(facilityDetails.getLegalOwner());
                    } else {
                        stockReleaseRequest.setLegalOwner(defaultLegalOwner);
                    }
                } catch (Exception e) {
                    stockReleaseRequest.setLegalOwner(defaultLegalOwner);
                }
            }
            handleReleaseInventory(stockReleaseRequest, true);
        }
    }
}
