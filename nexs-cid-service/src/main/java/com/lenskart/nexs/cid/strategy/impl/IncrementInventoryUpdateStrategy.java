package com.lenskart.nexs.cid.strategy.impl;

import com.lenskart.nexs.cid.constant.CommonConstants;
import com.lenskart.nexs.cid.constant.MetricConstants;
import com.lenskart.nexs.cid.dto.WarehouseInventoryDto;
import com.lenskart.nexs.cid.exception.ApplicationException;
import com.lenskart.nexs.cid.response.WarehouseInventoryCountResponse;
import com.lenskart.nexs.cid.strategy.BaseInventoryUpdateStrategy;
import com.lenskart.nexs.ims.enums.CidInventoryOperation;
import com.lenskart.nexs.ims.enums.LocationType;
import com.lenskart.nexs.ims.request.InventoryUpdateRequest;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

@Component
public class IncrementInventoryUpdateStrategy extends BaseInventoryUpdateStrategy {

    private final Counter errorCounter = Metrics.counter(MetricConstants.STALE_INVENTORY_UPDATE, "invOperation", "increment");
    @Value("${legalOwner.defaultLegalOnwner}")
    private String defaultLegalOnwner;

    @Value("${legalOwner.uaeLegalOwner}")
    private String uaeLegalOwner;

    @Override
    public CidInventoryOperation supportedInventoryOperation() {
        return CidInventoryOperation.INCREMENT;
    }

    @Override
    public void execute(InventoryUpdateRequest request) throws ApplicationException {
        WarehouseInventoryDto persistedWhInvDto = warehouseInventoryService.findByProductIdFacilityConditionAvailabilityAndStatusAndLegalOwnerAndLocationType(
                request.getProductId().longValue(),
                request.getFacility(),
                request.getCondition(),
                request.getAvailability(),
                request.getStatus(),
                request.getLegalOwner(),
                request.getLocationType()
        );
        if (isStaleEvent(request, persistedWhInvDto, errorCounter)) return;
        int warehouseQuantity;
        if (Objects.nonNull(persistedWhInvDto)) {
            warehouseQuantity = persistedWhInvDto.getQuantity() + request.getQuantity();
        } else {
            warehouseQuantity = 1;
        }
        persistedWhInvDto = updateWarehouseInventory(persistedWhInvDto, request, warehouseQuantity);
        if (isGoodAvailableAvailableInventoryUpdateEvent(request)) {
            request.setQuantity(persistedWhInvDto.getQuantity());
            WarehouseInventoryCountResponse warehouseInventoryCountResponse = getTotalWarehouseInventory(request);
            int bufferedQuantity = warehouseInventoryCountResponse.getBufferedWhInventoryCount();
            logger.info("processing absolute update where productId: {} facility: {} condition: {} availability: {}, " +
                            "status: {} increment count: {} totalWhInventoryCount: {} bufferedQuantity: {}",
                    request.getProductId(), request.getFacility(), request.getCondition(), request.getAvailability(), request.getStatus(),
                    persistedWhInvDto.getQuantity(), warehouseInventoryCountResponse.getTotalWhInventoryCount(), bufferedQuantity);
            updateStorefrontInventory(request, warehouseInventoryCountResponse.getTotalWhInventoryCount(), bufferedQuantity);
            sendInventoryUpdateToSCM(request, warehouseInventoryCountResponse.getTotalWhInventoryCount(), bufferedQuantity);
            updateBlockedInventoryDtoAndSendEvent(request, persistedWhInvDto);
            sendInventoryUpdateToJuno(request);
        }
    }

}
