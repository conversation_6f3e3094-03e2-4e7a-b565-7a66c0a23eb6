# Juno Audit Configuration
# Add these properties to your application.properties or consul configuration

# Enable/disable Juno audit logging (default: true)
juno.audit.enabled=true

# Audit data retention settings (optional - for future implementation)
juno.audit.retention.days=90
juno.audit.cleanup.enabled=true
juno.audit.cleanup.cron=0 0 2 * * ?

# Audit performance settings (optional)
juno.audit.async.enabled=false
juno.audit.batch.size=100

# Existing Juno configuration (ensure these are properly set)
juno-connector.base.url=${JUNO_BASE_URL:http://localhost:8080}
juno.inventory.update.url=${JUNO_INVENTORY_UPDATE_URL:/api/v1/inventory/update}
juno.connection.timeout=${JUNO_CONNECTION_TIMEOUT:30000}
juno.read.timeout=${JUNO_READ_TIMEOUT:60000}
juno-connector.username=${JUNO_USERNAME:your-username}
juno-connector.password=${JUNO_PASSWORD:your-password}

# Database configuration for audit table
# Ensure your datasource is properly configured to access the juno_audit_log table
