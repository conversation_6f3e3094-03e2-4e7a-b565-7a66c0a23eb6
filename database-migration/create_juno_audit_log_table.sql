-- Create table for Juno audit logging
-- This table will store audit information for all Juno API calls
-- Each row represents one transaction from the JunoInventoryUpdateRequest

CREATE TABLE IF NOT EXISTS juno_audit_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    correlation_id VARCHAR(100) NOT NULL,
    product_id INT NOT NULL,
    facility_code VARCHAR(50) NOT NULL,
    quantity INT NOT NULL,
    transaction_type VARCHAR(20) NOT NULL,
    legal_owner VARCHAR(50) NOT NULL,
    update_time VARCHAR(50),
    request_payload TEXT,
    response_payload TEXT,
    status VARCHAR(20) NOT NULL,
    http_status_code INT,
    error_message TEXT,
    request_timestamp TIMESTAMP NOT NULL,
    response_timestamp TIMESTAMP,
    duration_ms BIGINT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VA<PERSON><PERSON><PERSON>(100) NOT NULL DEFAULT 'system',
    updated_by VARCHAR(100) NOT NULL DEFAULT 'system',
    version INT NOT NULL DEFAULT 1
);

-- Create indexes for better query performance
CREATE INDEX idx_juno_audit_correlation_id ON juno_audit_log(correlation_id);
CREATE INDEX idx_juno_audit_product_id ON juno_audit_log(product_id);
CREATE INDEX idx_juno_audit_facility_code ON juno_audit_log(facility_code);
CREATE INDEX idx_juno_audit_created_at ON juno_audit_log(created_at);

-- Add comments to the table and columns
ALTER TABLE juno_audit_log COMMENT = 'Audit log table for tracking Juno inventory update API calls - one row per transaction';
ALTER TABLE juno_audit_log MODIFY COLUMN correlation_id VARCHAR(100) NOT NULL COMMENT 'Unique correlation ID for tracking requests';
ALTER TABLE juno_audit_log MODIFY COLUMN product_id INT NOT NULL COMMENT 'Product ID from the transaction';
ALTER TABLE juno_audit_log MODIFY COLUMN facility_code VARCHAR(50) NOT NULL COMMENT 'Facility code from the transaction';
ALTER TABLE juno_audit_log MODIFY COLUMN quantity INT NOT NULL COMMENT 'Quantity from the transaction';
ALTER TABLE juno_audit_log MODIFY COLUMN transaction_type VARCHAR(20) NOT NULL COMMENT 'Transaction type (e.g., EXACT)';
ALTER TABLE juno_audit_log MODIFY COLUMN legal_owner VARCHAR(50) NOT NULL COMMENT 'Legal owner from the transaction';
ALTER TABLE juno_audit_log MODIFY COLUMN update_time VARCHAR(50) COMMENT 'Update time from the transaction';
ALTER TABLE juno_audit_log MODIFY COLUMN request_payload TEXT COMMENT 'JSON payload sent to Juno API';
ALTER TABLE juno_audit_log MODIFY COLUMN response_payload TEXT COMMENT 'Response received from Juno API';
ALTER TABLE juno_audit_log MODIFY COLUMN status VARCHAR(20) NOT NULL COMMENT 'Status of the API call (SUCCESS, FAILED, TIMEOUT, PENDING)';
ALTER TABLE juno_audit_log MODIFY COLUMN http_status_code INT COMMENT 'HTTP status code returned by Juno API';
ALTER TABLE juno_audit_log MODIFY COLUMN error_message TEXT COMMENT 'Error message if the API call failed';
ALTER TABLE juno_audit_log MODIFY COLUMN request_timestamp TIMESTAMP NOT NULL COMMENT 'Timestamp when the request was sent';
ALTER TABLE juno_audit_log MODIFY COLUMN response_timestamp TIMESTAMP COMMENT 'Timestamp when the response was received';
ALTER TABLE juno_audit_log MODIFY COLUMN duration_ms BIGINT COMMENT 'Duration of the API call in milliseconds';
