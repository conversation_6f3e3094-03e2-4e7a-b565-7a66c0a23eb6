-- Create table for Juno audit logging
-- This table will store audit information for all Juno API calls

CREATE TABLE IF NOT EXISTS juno_audit_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    correlation_id VARCHAR(100) NOT NULL,
    request_payload TEXT,
    response_payload TEXT,
    status VARCHAR(20) NOT NULL,
    http_status_code INT,
    error_message TEXT,
    error_stack_trace TEXT,
    request_timestamp TIMESTAMP NOT NULL,
    response_timestamp TIMESTAMP,
    duration_ms BIGINT,
    retry_count INT NOT NULL DEFAULT 0,
    api_endpoint VARCHAR(500),
    product_ids TEXT,
    facility_codes TEXT,
    legal_owners TEXT,
    transaction_count INT,
    source VARCHAR(50),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VA<PERSON><PERSON><PERSON>(100) NOT NULL DEFAULT 'system',
    updated_by VARCHAR(100) NOT NULL DEFAULT 'system'
);

-- Create indexes for better query performance
CREATE INDEX idx_juno_audit_correlation_id ON juno_audit_log(correlation_id);
CREATE INDEX idx_juno_audit_status ON juno_audit_log(status);
CREATE INDEX idx_juno_audit_created_at ON juno_audit_log(created_at);
CREATE INDEX idx_juno_audit_request_timestamp ON juno_audit_log(request_timestamp);
CREATE INDEX idx_juno_audit_product_ids ON juno_audit_log(product_ids(255));
CREATE INDEX idx_juno_audit_facility_codes ON juno_audit_log(facility_codes(255));
CREATE INDEX idx_juno_audit_legal_owners ON juno_audit_log(legal_owners(255));
CREATE INDEX idx_juno_audit_source ON juno_audit_log(source);
CREATE INDEX idx_juno_audit_retry_count ON juno_audit_log(retry_count);

-- Add comments to the table and columns
ALTER TABLE juno_audit_log COMMENT = 'Audit log table for tracking Juno inventory update API calls';
ALTER TABLE juno_audit_log MODIFY COLUMN correlation_id VARCHAR(100) NOT NULL COMMENT 'Unique correlation ID for tracking requests';
ALTER TABLE juno_audit_log MODIFY COLUMN request_payload TEXT COMMENT 'JSON payload sent to Juno API';
ALTER TABLE juno_audit_log MODIFY COLUMN response_payload TEXT COMMENT 'Response received from Juno API';
ALTER TABLE juno_audit_log MODIFY COLUMN status VARCHAR(20) NOT NULL COMMENT 'Status of the API call (SUCCESS, FAILED, TIMEOUT, RETRY, PENDING)';
ALTER TABLE juno_audit_log MODIFY COLUMN http_status_code INT COMMENT 'HTTP status code returned by Juno API';
ALTER TABLE juno_audit_log MODIFY COLUMN error_message TEXT COMMENT 'Error message if the API call failed';
ALTER TABLE juno_audit_log MODIFY COLUMN error_stack_trace TEXT COMMENT 'Stack trace of the error if available';
ALTER TABLE juno_audit_log MODIFY COLUMN request_timestamp TIMESTAMP NOT NULL COMMENT 'Timestamp when the request was sent';
ALTER TABLE juno_audit_log MODIFY COLUMN response_timestamp TIMESTAMP COMMENT 'Timestamp when the response was received';
ALTER TABLE juno_audit_log MODIFY COLUMN duration_ms BIGINT COMMENT 'Duration of the API call in milliseconds';
ALTER TABLE juno_audit_log MODIFY COLUMN retry_count INT NOT NULL DEFAULT 0 COMMENT 'Number of retry attempts for this request';
ALTER TABLE juno_audit_log MODIFY COLUMN api_endpoint VARCHAR(500) COMMENT 'Full API endpoint URL called';
ALTER TABLE juno_audit_log MODIFY COLUMN product_ids TEXT COMMENT 'Comma-separated list of product IDs in the request';
ALTER TABLE juno_audit_log MODIFY COLUMN facility_codes TEXT COMMENT 'Comma-separated list of facility codes in the request';
ALTER TABLE juno_audit_log MODIFY COLUMN legal_owners TEXT COMMENT 'Comma-separated list of legal owners in the request';
ALTER TABLE juno_audit_log MODIFY COLUMN transaction_count INT COMMENT 'Number of transactions in the request';
ALTER TABLE juno_audit_log MODIFY COLUMN source VARCHAR(50) COMMENT 'Source system that initiated the request (e.g., CID)';
