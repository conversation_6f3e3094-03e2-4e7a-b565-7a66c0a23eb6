version: '3'

services:
  nexs-cid-adaptor: &trail
    image: 928401551325.dkr.ecr.ap-southeast-1.amazonaws.com/nexs-cid-dev:latest
    container_name: nexs-cid-adaptor
    restart: unless-stopped
    ports:
      - "8300:8080"
      - "8320:9700"
    environment: &trail_env
      TZ: Asia/Kolkata
      PROFILE: qa
      CID_AEROSPIKE_HOST: ${AEROSPIKE_HOST}
      CID_AEROSPIKE_PORT: ${AEROSPIKE_PORT}
      VSM_WSDL_URL: ${VSM_WSDL_URL}
      LKS_AUTH_URL: ${LKS_AUTH_URL}
      DKS_AUTH_URL: ${DKS_AUTH_URL}
      UNICOMMERCE_BASE_URL: ${UNICOMMERCE_BASE_URL}
      UNICOMMERCE_B2BLK_BASE_URL: ${UNICOMMERCE_B2BLK_BASE_URL}
      CID_KAFKA_BROKERS: ${KAFKA_BROKERS}
      CID_KAFKA_GROUP_ID: nexs_cid
      AWS_S3_ACCESS_KEY: ${AWS_S3_ACCESS_KEY}
      AWS_S3_SECRET_KEY: ${AWS_S3_SECRET_KEY}
      AWS_S3_BUCKET: ${AWS_S3_BUCKET}
      CID_MONGO_URI: ${NEXS_MONGO_URI}
      INVENTORY_SYNC_TOPIC: nexs-cid-inventory-sync
      CID_KAFKA_ENABLED: "false"
      IMS_FETCH_STOCK_DETAILS_URL: "http://nexs-ims-adaptor:8080/nexs/api/ims/fetchStockCount"
      VSM_WSDL_URL: http://vsm2.dkrt.in/index.php/orderManagement?wsdl
      JAVA_TOOL_OPTIONS: '-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=0.0.0.0:9700'
    ulimits:
      nproc: 65535
      nofile:
        soft: 65535
        hard: 65535
    deploy:
      resources:
        limits:
          memory: 1024M
        reservations:
          memory: 128M

  nexs-cid-consumer:
    <<: *trail
    container_name: nexs-cid-consumer
    ports:
      - "8301:8080"
    environment:
      <<: *trail_env
      CID_KAFKA_ENABLED: "true"

networks:
  default:
    external:
      name: nexs