# Juno Inventory Update Audit Tracking

## Overview

This implementation provides comprehensive audit tracking for Juno inventory update API calls. It tracks all requests, responses, errors, and performance metrics to help monitor and troubleshoot issues with the Juno integration.

## Features

### 1. **Complete Request/Response Tracking**
- Stores full request payload sent to Juno
- Captures complete response from Juno API
- Records HTTP status codes and response times

### 2. **Error Tracking & Debugging**
- Captures detailed error messages
- Stores complete stack traces for exceptions
- Categorizes different types of failures (HTTP errors, timeouts, etc.)

### 3. **Performance Monitoring**
- Records request and response timestamps
- Calculates and stores API call duration
- Tracks retry attempts and patterns

### 4. **Correlation & Traceability**
- Generates unique correlation IDs for each request
- Links audit records to original inventory update requests
- Enables end-to-end tracing of inventory updates

### 5. **Search & Analytics**
- Query audit logs by various criteria (product ID, facility, date range, status)
- Generate statistics and success/failure rates
- Identify high-retry scenarios and performance issues

## Database Schema

### Table: `juno_audit_log`

| Column | Type | Description |
|--------|------|-------------|
| `id` | BIGINT | Primary key |
| `correlation_id` | VARCHAR(100) | Unique identifier for tracking |
| `request_payload` | TEXT | JSON request sent to Juno |
| `response_payload` | TEXT | Response received from Juno |
| `status` | VARCHAR(20) | API call status (SUCCESS, FAILED, TIMEOUT, RETRY, PENDING) |
| `http_status_code` | INT | HTTP status code from Juno |
| `error_message` | TEXT | Error message if failed |
| `error_stack_trace` | TEXT | Stack trace for debugging |
| `request_timestamp` | TIMESTAMP | When request was sent |
| `response_timestamp` | TIMESTAMP | When response was received |
| `duration_ms` | BIGINT | API call duration in milliseconds |
| `retry_count` | INT | Number of retry attempts |
| `api_endpoint` | VARCHAR(500) | Full API endpoint URL |
| `product_ids` | TEXT | Comma-separated product IDs |
| `facility_codes` | TEXT | Comma-separated facility codes |
| `legal_owners` | TEXT | Comma-separated legal owners |
| `transaction_count` | INT | Number of transactions in request |
| `source` | VARCHAR(50) | Source system (e.g., "CID") |

## API Endpoints

### 1. **Query by Correlation ID**
```
GET /nexs/cid/api/v1/juno-audit/correlation/{correlationId}
```
Returns all audit logs for a specific correlation ID.

### 2. **Query by Status**
```
GET /nexs/cid/api/v1/juno-audit/status/{status}?page=0&size=20
```
Returns paginated audit logs filtered by status (SUCCESS, FAILED, TIMEOUT, RETRY, PENDING).

### 3. **Query by Date Range**
```
GET /nexs/cid/api/v1/juno-audit/date-range?startDate=2024-01-01 00:00:00&endDate=2024-01-31 23:59:59&page=0&size=20
```
Returns paginated audit logs within a specific date range.

### 4. **Query by Product ID**
```
GET /nexs/cid/api/v1/juno-audit/product/{productId}?page=0&size=20
```
Returns paginated audit logs containing a specific product ID.

### 5. **Query by Facility Code**
```
GET /nexs/cid/api/v1/juno-audit/facility/{facilityCode}?page=0&size=20
```
Returns paginated audit logs for a specific facility.

### 6. **Query by Legal Owner**
```
GET /nexs/cid/api/v1/juno-audit/legal-owner/{legalOwner}?page=0&size=20
```
Returns paginated audit logs for a specific legal owner.

### 7. **Get Failed Logs**
```
GET /nexs/cid/api/v1/juno-audit/failed?startDate=2024-01-01 00:00:00&endDate=2024-01-31 23:59:59
```
Returns all failed audit logs within a date range.

### 8. **Get Audit Statistics**
```
GET /nexs/cid/api/v1/juno-audit/stats?startDate=2024-01-01 00:00:00&endDate=2024-01-31 23:59:59
```
Returns comprehensive statistics including success rates, failure rates, and performance metrics.

### 9. **Get High Retry Logs**
```
GET /nexs/cid/api/v1/juno-audit/high-retry?minRetryCount=3
```
Returns logs with retry count above the specified threshold.

## Implementation Details

### Key Components

1. **JunoAuditLog Entity** - Database entity for storing audit information
2. **JunoAuditService** - Service layer for audit operations
3. **JunoAuditController** - REST API for querying audit data
4. **Modified JunoConnector** - Enhanced with audit logging capabilities

### Status Types

- **PENDING**: Request initiated but not yet completed
- **SUCCESS**: Request completed successfully
- **FAILED**: Request failed due to business logic or HTTP errors
- **TIMEOUT**: Request timed out
- **RETRY**: Request is being retried

### Error Handling

The system captures different types of errors:
- **HTTP Client/Server Errors**: 4xx and 5xx status codes
- **Timeout Errors**: Network timeouts and connection issues
- **General Exceptions**: Any other unexpected errors

## Usage Examples

### 1. **Track a Failed Request**
```bash
# Get failed logs for today
curl "http://localhost:8080/nexs/cid/api/v1/juno-audit/failed?startDate=2024-01-15 00:00:00&endDate=2024-01-15 23:59:59"
```

### 2. **Monitor Success Rate**
```bash
# Get statistics for the last 24 hours
curl "http://localhost:8080/nexs/cid/api/v1/juno-audit/stats?startDate=2024-01-14 00:00:00&endDate=2024-01-15 00:00:00"
```

### 3. **Debug Specific Product Issues**
```bash
# Get all logs for product ID 12345
curl "http://localhost:8080/nexs/cid/api/v1/juno-audit/product/12345"
```

### 4. **Find High Retry Scenarios**
```bash
# Get logs with 3 or more retries
curl "http://localhost:8080/nexs/cid/api/v1/juno-audit/high-retry?minRetryCount=3"
```

## Database Migration

Run the provided SQL script to create the audit table:

```sql
-- Execute the migration script
source database-migration/create_juno_audit_log_table.sql
```

## Benefits

1. **Improved Debugging**: Complete visibility into Juno API interactions
2. **Performance Monitoring**: Track response times and identify bottlenecks
3. **Reliability Tracking**: Monitor success/failure rates and retry patterns
4. **Compliance**: Maintain audit trail for inventory updates
5. **Proactive Monitoring**: Identify issues before they impact business operations

## Future Enhancements

1. **Alerting**: Add automated alerts for high failure rates
2. **Dashboard**: Create monitoring dashboard for real-time visibility
3. **Data Retention**: Implement data archival policies
4. **Advanced Analytics**: Add trend analysis and predictive monitoring
