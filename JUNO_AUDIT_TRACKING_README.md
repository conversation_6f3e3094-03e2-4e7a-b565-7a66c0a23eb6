# Juno Inventory Update Audit Tracking

## Overview

This implementation provides comprehensive audit tracking for Juno inventory update API calls. It tracks all requests, responses, errors, and performance metrics to help monitor and troubleshoot issues with the Juno integration. Each transaction in the request is stored as a separate audit record for detailed tracking.

## Features

### 1. **Transaction-Level Tracking**
- Each transaction from JunoInventoryUpdateRequest is stored as a separate audit record
- Stores individual product ID, facility code, quantity, transaction type, legal owner, and update time
- Enables precise tracking of which specific transactions succeeded or failed

### 2. **Complete Request/Response Tracking**
- Stores full request payload sent to Juno
- Captures complete response from Juno API
- Records HTTP status codes and response times

### 3. **Error Tracking & Debugging**
- Captures detailed error messages
- Categorizes different types of failures (HTTP errors, timeouts, etc.)
- All transactions in a request share the same error information

### 4. **Performance Monitoring**
- Records request and response timestamps
- Calculates and stores API call duration
- Tracks API performance over time

### 5. **Correlation & Traceability**
- Generates unique correlation IDs for each request
- Links all transactions from the same request via correlation ID
- Enables end-to-end tracing of inventory updates

## Database Schema

### Table: `juno_audit_log`

| Column | Type | Description |
|--------|------|-------------|
| `id` | BIGINT | Primary key |
| `correlation_id` | VARCHAR(100) | Unique identifier for tracking requests |
| `product_id` | INT | Product ID from the transaction |
| `facility_code` | VARCHAR(50) | Facility code from the transaction |
| `quantity` | INT | Quantity from the transaction |
| `transaction_type` | VARCHAR(20) | Transaction type (e.g., EXACT) |
| `legal_owner` | VARCHAR(50) | Legal owner from the transaction |
| `update_time` | VARCHAR(50) | Update time from the transaction |
| `request_payload` | TEXT | JSON request sent to Juno |
| `response_payload` | TEXT | Response received from Juno |
| `status` | VARCHAR(20) | API call status (SUCCESS, FAILED, TIMEOUT, PENDING) |
| `http_status_code` | INT | HTTP status code from Juno |
| `error_message` | TEXT | Error message if failed |
| `request_timestamp` | TIMESTAMP | When request was sent |
| `response_timestamp` | TIMESTAMP | When response was received |
| `duration_ms` | BIGINT | API call duration in milliseconds |
| `created_at` | TIMESTAMP | Record creation timestamp |
| `updated_at` | TIMESTAMP | Record update timestamp |
| `created_by` | VARCHAR(100) | User who created the record |
| `updated_by` | VARCHAR(100) | User who updated the record |

## Data Access

Since this implementation focuses on audit logging without REST APIs, data can be accessed directly through:

### 1. **Database Queries**
You can query the `juno_audit_log` table directly using SQL:

```sql
-- Find all audit logs for a specific correlation ID
SELECT * FROM juno_audit_log WHERE correlation_id = 'your-correlation-id' ORDER BY created_at DESC;

-- Find all audit logs for a specific product ID
SELECT * FROM juno_audit_log WHERE product_id = 12345 ORDER BY created_at DESC;

-- Find all failed requests in the last 24 hours
SELECT * FROM juno_audit_log WHERE status = 'FAILED' AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR);

-- Find all audit logs for a specific facility
SELECT * FROM juno_audit_log WHERE facility_code = 'FC001' ORDER BY created_at DESC;

-- Get success/failure statistics for a date range
SELECT
    status,
    COUNT(*) as count,
    AVG(duration_ms) as avg_duration_ms
FROM juno_audit_log
WHERE created_at BETWEEN '2024-01-01' AND '2024-01-31'
GROUP BY status;
```

### 2. **Repository Methods**
The `JunoAuditLogRepository` provides several finder methods:

```java
// Find by correlation ID
List<JunoAuditLog> logs = junoAuditLogRepository.findByCorrelationIdOrderByCreatedAtDesc(correlationId);

// Find by product ID
List<JunoAuditLog> logs = junoAuditLogRepository.findByProductIdOrderByCreatedAtDesc(productId);

// Find by facility code
List<JunoAuditLog> logs = junoAuditLogRepository.findByFacilityCodeOrderByCreatedAtDesc(facilityCode);

// Find by legal owner
List<JunoAuditLog> logs = junoAuditLogRepository.findByLegalOwnerOrderByCreatedAtDesc(legalOwner);

// Find failed logs in date range
List<JunoAuditLog> failedLogs = junoAuditLogRepository.findFailedLogsBetweenDates(startDate, endDate);
```

## Implementation Details

### Key Components

1. **JunoAuditLog Entity** - Database entity for storing audit information (one record per transaction)
2. **JunoAuditService** - Service layer for audit operations
3. **Modified JunoConnector** - Enhanced with audit logging capabilities

### Status Types

- **PENDING**: Request initiated but not yet completed
- **SUCCESS**: Request completed successfully
- **FAILED**: Request failed due to business logic or HTTP errors
- **TIMEOUT**: Request timed out

### Error Handling

The system captures different types of errors:
- **HTTP Client/Server Errors**: 4xx and 5xx status codes
- **Timeout Errors**: Network timeouts and connection issues
- **General Exceptions**: Any other unexpected errors

## Usage Examples

### 1. **Track a Failed Request**
```sql
-- Get failed logs for today
SELECT * FROM juno_audit_log
WHERE status = 'FAILED'
AND DATE(created_at) = CURDATE()
ORDER BY created_at DESC;
```

### 2. **Monitor Success Rate**
```sql
-- Get statistics for the last 24 hours
SELECT
    status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
FROM juno_audit_log
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
GROUP BY status;
```

### 3. **Debug Specific Product Issues**
```sql
-- Get all logs for product ID 12345
SELECT * FROM juno_audit_log
WHERE product_id = 12345
ORDER BY created_at DESC;
```

### 4. **Find Requests by Correlation ID**
```sql
-- Get all transactions for a specific request
SELECT * FROM juno_audit_log
WHERE correlation_id = 'your-correlation-id'
ORDER BY created_at DESC;
```

## Database Migration

Run the provided SQL script to create the audit table:

```sql
-- Execute the migration script
source database-migration/create_juno_audit_log_table.sql
```

## Deployment Steps

1. **Database Migration**
   ```sql
   source database-migration/create_juno_audit_log_table.sql
   ```

2. **Code Deployment**
   - Deploy the enhanced codebase
   - Verify audit logging is working by checking the database table

3. **Monitoring Setup**
   - Set up monitoring dashboards using database queries
   - Configure alerts for high failure rates if needed

## Benefits

1. **Improved Debugging**: Complete visibility into Juno API interactions
2. **Performance Monitoring**: Track response times and identify bottlenecks
3. **Reliability Tracking**: Monitor success/failure rates and retry patterns
4. **Compliance**: Maintain audit trail for inventory updates
5. **Proactive Monitoring**: Identify issues before they impact business operations

## Future Enhancements

1. **Alerting**: Add automated alerts for high failure rates
2. **Dashboard**: Create monitoring dashboard for real-time visibility
3. **Data Retention**: Implement data archival policies
4. **Advanced Analytics**: Add trend analysis and predictive monitoring
